<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 样式定义 -->
    <style type="text/css">
      /* 通用颜色定义 - 适应黑色背景 */
      .bg-black { fill: #000000; } /* 纯黑色背景 */
      .text-primary-light { fill: #F8FAFC; } /* 用于黑色背景上的主文本色 */
      .text-secondary-light { fill: #94A3B8; } /* 用于黑色背景上的辅助文本色 */
      .accent-red { fill: #E31937; } /* 特斯拉红强调色 */
      .primary-blue { fill: #1E40AF; } /* 主蓝色，用于装饰元素 */
      .accent-blue { fill: #3B82F6; } /* 强调蓝色，用于装饰元素和图标 */

      /* 渐变定义 - 使用特斯拉红和蓝色，配合透明度制造科技感 */
      .gradient-red-fade-stop-1 { stop-color: #E31937; stop-opacity: 1; }
      .gradient-red-fade-stop-2 { stop-color: #E31937; stop-opacity: 0; }
      .gradient-blue-fade-stop-1 { stop-color: #3B82F6; stop-opacity: 1; }
      .gradient-blue-fade-stop-2 { stop-color: #3B82F6; stop-opacity: 0; }

      /* 字体样式定义 */
      .font-yahei { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-source-han { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-times { font-family: 'Times New Roman', serif; }

      .hero-title {
        font-size: 72px;
        font-weight: 700; /* bold */
        line-height: 1.1; /* tight */
        letter-spacing: 0em; /* normal */
        fill: #F8FAFC; /* 主文本色 */
      }

      .main-title {
        font-size: 56px;
        font-weight: 600; /* semibold */
        line-height: 1.4; /* normal */
        letter-spacing: 0em;
        fill: #F8FAFC; /* 主文本色 */
      }

      .section-title {
        font-size: 36px;
        font-weight: 500; /* medium */
        line-height: 1.4;
        letter-spacing: 0em;
        fill: #F8FAFC;
      }

      .content-title {
        font-size: 28px;
        font-weight: 500;
        line-height: 1.4;
        letter-spacing: 0em;
        fill: #F8FAFC;
      }

      .body-text {
        font-size: 22px;
        font-weight: 400; /* normal */
        line-height: 1.6; /* relaxed */
        letter-spacing: 0em;
        fill: #94A3B8; /* 辅助文本色 */
      }

      .small-text {
        font-size: 16px;
        font-weight: 400;
        line-height: 1.6;
        letter-spacing: 0em;
        fill: #94A3B8;
      }

      .caption {
        font-size: 14px;
        font-weight: 300; /* light */
        line-height: 1.6;
        letter-spacing: 0.025em; /* wide */
        fill: #94A3B8;
      }

      /* 装饰元素样式 */
      .divider-line {
        stroke: #475569; /* 辅助蓝色，用于分割线 */
        stroke-width: 1;
      }

      /* Logo 容器样式 */
      .logo-container {
        filter: drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.2)); /* 微妙的阴影效果 */
      }
    </style>

    <!-- 线性渐变定义 -->
    <linearGradient id="redGradientHorizontal" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" class="gradient-red-fade-stop-1" />
      <stop offset="100%" class="gradient-red-fade-stop-2" />
    </linearGradient>

    <linearGradient id="redGradientVertical" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" class="gradient-red-fade-stop-1" />
      <stop offset="100%" class="gradient-red-fade-stop-2" />
    </linearGradient>

    <linearGradient id="blueGradientDiagonal" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" class="gradient-blue-fade-stop-1" />
      <stop offset="100%" class="gradient-blue-fade-stop-2" />
    </linearGradient>

  </defs>

  <!-- 背景层 -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-black" />

  <!-- 装饰元素 - 结合Bento Grid风格和特斯拉红亮点 -->
  <g id="decorative_elements">
    <!-- 页面号“01”作为超大视觉元素，带有红色垂直渐变 -->
    <text x="1780" y="220" class="font-times hero-title" fill="url(#redGradientVertical)" text-anchor="end" opacity="0.08">01</text>

    <!-- 对角线红色抽象形状，低透明度 -->
    <path d="M-50 900 L 1000 1100 L 1200 800 L 200 600 Z" fill="url(#redGradientHorizontal)" opacity="0.15" />

    <!-- 几何网格线 - 使用主蓝色，低透明度 -->
    <line x1="80" y1="400" x2="400" y2="400" stroke="#1E40AF" stroke-width="1.5" stroke-opacity="0.2" />
    <line x1="80" y1="450" x2="350" y2="450" stroke="#1E40AF" stroke-width="1.5" stroke-opacity="0.2" />

    <!-- 右上角蓝色方块装饰 -->
    <rect x="1800" y="0" width="120" height="120" fill="#1E40AF" opacity="0.08" />
    <rect x="1840" y="40" width="80" height="80" fill="#1E40AF" opacity="0.12" />

    <!-- 右下角蓝色方块装饰 -->
    <rect x="1600" y="960" width="320" height="120" fill="#3B82F6" opacity="0.06" />
    <rect x="1650" y="990" width="270" height="90" fill="#3B82F6" opacity="0.1" />

    <!-- 抽象数据可视化折线图 - 强调蓝色，低透明度 -->
    <polyline points="100 800, 200 750, 300 820, 400 770, 500 850" fill="none" stroke="#3B82F6" stroke-width="2" stroke-opacity="0.3" />
    <polyline points="100 850, 200 800, 300 870, 400 820, 500 900" fill="none" stroke="#1E40AF" stroke-width="2" stroke-opacity="0.2" />

    <!-- 底部装饰性分割线 -->
    <line x1="80" y1="900" x2="1840" y2="900" stroke="#3B82F6" stroke-width="1.5" stroke-opacity="0.25" />
  </g>

  <!-- Logo 区域 (左上角) -->
  <g id="logo_area" class="logo-container">
    <!-- 请替换 {logo_url} 为您的Logo图片URL -->
    <!-- 建议Logo尺寸：宽度200px，高度80px，可根据实际Logo比例调整 -->
    <image href="{logo_url}" x="80" y="60" width="200" height="80" preserveAspectRatio="xMidYMid meet" />
    <!-- 如果没有Logo图片，可以使用文本占位符，请删除上面的<image>标签并取消注释下面一行 -->
    <!-- <text x="80" y="110" class="main-title font-yahei" fill="#F8FAFC">您的品牌</text> -->
  </g>

  <!-- 主标题和副标题内容区域 -->
  <g id="main_titles">
    <!-- 主标题 (中文大字体粗体) -->
    <text x="960" y="450" text-anchor="middle" class="hero-title font-yahei">
      <tspan x="960" dy="0">商业计划书</tspan>
    </text>

    <!-- 副标题 (英文小字作为点缀) -->
    <text x="960" y="530" text-anchor="middle" class="main-title font-source-han" fill="#94A3B8">
      <tspan x="960" dy="0">Business Plan Proposal</tspan>
    </text>

    <!-- 描述性副标题 -->
    <text x="960" y="650" text-anchor="middle" class="body-text font-source-han">
      <tspan x="960" dy="0">市场分析、财务预测和风险评估</tspan>
      <tspan x="960" dy="40">助力您的投资决策和战略规划</tspan>
    </text>
  </g>

  <!-- 底部信息 (日期、作者和页码) -->
  <g id="footer_info">
    <text x="80" y="980" class="small-text font-source-han">
      <tspan x="80" dy="0">日期: {date}</tspan>
    </text>
    <text x="80" y="1010" class="small-text font-source-han">
      <tspan x="80" dy="0">作者: {author}</tspan>
    </text>

    <!-- 页码 -->
    <text x="1840" y="1010" text-anchor="end" class="caption font-times">
      <tspan x="1840" dy="0">1/10</tspan>
    </text>
  </g>

</svg>