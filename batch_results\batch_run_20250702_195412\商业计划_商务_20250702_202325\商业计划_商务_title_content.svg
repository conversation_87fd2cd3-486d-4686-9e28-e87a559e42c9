<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- 统一蓝色系配色方案 -->
    <style type="text/css">
      /* Color variables */
      :root {
        --primary-color: #1E40AF;
        --secondary-color: #475569;
        --accent-color: #3B82F6;
        --background-color: #F8FAFC;
        --text-primary: #1E293B;
        --text-secondary: #64748B;
        --card-background: #FFFFFF;
        --card-border: #BAE6FD;
        --hover-color: #7DD3FC; /* From design norms */
      }

      /* Font system */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }

      /* Font sizes and weights */
      .hero-title { font-size: 72px; font-weight: 700; fill: var(--text-primary); }
      .main-title { font-size: 56px; font-weight: 700; fill: var(--text-primary); }
      .section-title { font-size: 36px; font-weight: 700; fill: var(--text-primary); }
      .content-title { font-size: 28px; font-weight: 600; fill: var(--text-primary); }
      .body-text { font-size: 22px; font-weight: 400; fill: var(--text-secondary); }
      .small-text { font-size: 16px; font-weight: 400; fill: var(--text-secondary); }
      .caption { font-size: 14px; font-weight: 400; fill: var(--text-secondary); }

      /* Text colors */
      .text-primary-color { fill: var(--text-primary); }
      .text-secondary-color { fill: var(--text-secondary); }
      .accent-text-color { fill: var(--accent-color); }
      .primary-accent-color { fill: var(--primary-color); }

      /* Card style */
      .card-style {
        fill: var(--card-background);
        stroke: var(--card-border);
        stroke-width: 1px;
        filter: url(#shadow);
      }

      /* Bullet point style */
      .bullet-point {
        fill: var(--accent-color);
      }

      /* Outline icon style */
      .icon-outline {
        stroke: var(--primary-color);
        stroke-width: 2;
        fill: none;
      }
    </style>

    <!-- 阴影效果 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="6" flood-color="rgba(0, 0, 0, 0.1)"/>
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="rgba(0, 0, 0, 0.06)"/>
    </filter>

    <!-- 渐变背景装饰 (primary_gradient from design norms) -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF"/>
      <stop offset="100%" stop-color="#475569"/>
    </linearGradient>

    <!-- 强调色渐变 (accent_gradient from design norms) -->
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- 动态图标：增长箭头 -->
    <g id="icon-growth">
      <path class="icon-outline" d="M12 20V4M4 12L12 4L20 12"/>
    </g>

    <!-- 动态图标：市场分析 -->
    <g id="icon-market">
      <path class="icon-outline" d="M22 12H18L15 21L9 3L6 12H2"/>
    </g>

    <!-- 动态图标：数据图表 -->
    <g id="icon-chart">
      <rect x="3" y="10" width="4" height="11" rx="1" ry="1" class="icon-outline"/>
      <rect x="10" y="3" width="4" height="18" rx="1" ry="1" class="icon-outline"/>
      <rect x="17" y="14" width="4" height="7" rx="1" ry="1" class="icon-outline"/>
    </g>

  </defs>

  <!-- 背景色 -->
  <rect x="0" y="0" width="1920" height="1080" fill="var(--background-color)"/>

  <!-- 顶部装饰性波浪线 -->
  <path d="M0 0 C 300 80, 600 20, 960 80 C 1320 140, 1620 100, 1920 180 L 1920 0 L 0 0 Z" fill="url(#primaryGradient)" opacity="0.1"/>

  <!-- 左上角Logo占位符 -->
  <image x="80" y="60" width="160" height="40" href="{logo_url}" />
  <rect x="80" y="60" width="160" height="40" fill="none" stroke="var(--primary-color)" stroke-dasharray="5 5" stroke-width="1"/>
  <text x="160" y="85" text-anchor="middle" class="small-text" fill="var(--primary-color)">Logo</text>

  <!-- 页面标题 -->
  <text x="80" y="150" class="main-title font-primary text-primary-color">{title}</text>
  <text x="80" y="220" class="content-title font-secondary text-secondary-color">{subtitle}</text>

  <!-- 主要内容卡片区域 -->
  <rect x="80" y="280" width="1760" height="740" rx="12" ry="12" class="card-style"/>

  <!-- 卡片内部内容 -->
  <!-- 内容标题 -->
  <text x="120" y="360" class="section-title font-primary text-primary-color">
    <tspan>市场分析和增长策略</tspan>
  </text>
  <use xlink:href="#icon-market" x="1750" y="330" width="32" height="32"/>

  <!-- 正文内容段落 -->
  <text x="120" y="420" class="body-text font-secondary text-secondary-color">
    <tspan x="120" dy="0">我们深入分析了当前市场趋势、竞争格局和潜在机遇，旨在为您的商业计划提供坚实基础。</tspan>
    <tspan x="120" dy="35">通过对行业数据的全面解读，我们识别出核心增长驱动因素和未来发展方向。</tspan>
    <tspan x="120" dy="35">此分析将指导我们制定有效的市场进入和扩张策略，确保投资回报的最大化。</tspan>
  </text>

  <!-- 要点列表 -->
  <!-- 列表标题 -->
  <text x="120" y="560" class="content-title font-primary text-primary-color">
    <tspan>关键市场洞察</tspan>
  </text>
  <use xlink:href="#icon-growth" x="1750" y="530" width="32" height="32"/>


  <!-- 列表项 1 -->
  <circle cx="120" cy="620" r="6" class="bullet-point"/>
  <text x="140" y="625" class="body-text font-secondary text-secondary-color">
    <tspan x="140" dy="0">新兴技术驱动行业变革，带来新的增长点和商业模式。</tspan>
  </text>

  <!-- 列表项 2 -->
  <circle cx="120" cy="620 + 45" r="6" class="bullet-point"/>
  <text x="140" y="625 + 45" class="body-text font-secondary text-secondary-color">
    <tspan x="140" dy="0">消费者行为变化加速，个性化和定制化需求日益增长。</tspan>
  </text>

  <!-- 列表项 3 -->
  <circle cx="120" cy="620 + 90" r="6" class="bullet-point"/>
  <text x="140" y="625 + 90" class="body-text font-secondary text-secondary-color">
    <tspan x="140" dy="0">全球化趋势下，跨区域合作和市场拓展成为战略重点。</tspan>
  </text>

  <!-- 数据图表占位符 -->
  <text x="120" y="780" class="content-title font-primary text-primary-color">
    <tspan>市场趋势数据概览</tspan>
  </text>
  <use xlink:href="#icon-chart" x="1750" y="750" width="32" height="32"/>

  <!-- 模拟柱状图 -->
  <g transform="translate(120, 800)">
    <!-- Y轴标签 -->
    <text x="-20" y="0" class="small-text text-secondary-color" text-anchor="end">高</text>
    <text x="-20" y="100" class="small-text text-secondary-color" text-anchor="end">中</text>
    <text x="-20" y="200" class="small-text text-secondary-color" text-anchor="end">低</text>
    <!-- X轴 -->
    <line x1="0" y1="220" x2="1680" y2="220" stroke="var(--card-border)" stroke-width="1"/>
    <!-- Y轴 -->
    <line x1="0" y1="0" x2="0" y2="220" stroke="var(--card-border)" stroke-width="1"/>

    <!-- 柱子和X轴标签 -->
    <rect x="50" y="100" width="80" height="120" fill="var(--accent-color)" rx="4" ry="4"/>
    <text x="90" y="240" text-anchor="middle" class="small-text text-secondary-color">Q1</text>
    <rect x="250" y="70" width="80" height="150" fill="var(--accent-color)" rx="4" ry="4"/>
    <text x="290" y="240" text-anchor="middle" class="small-text text-secondary-color">Q2</text>
    <rect x="450" y="130" width="80" height="90" fill="var(--accent-color)" rx="4" ry="4"/>
    <text x="490" y="240" text-anchor="middle" class="small-text text-secondary-color">Q3</text>
    <rect x="650" y="50" width="80" height="170" fill="var(--accent-color)" rx="4" ry="4"/>
    <text x="690" y="240" text-anchor="middle" class="small-text text-secondary-color">Q4</text>
    <rect x="850" y="80" width="80" height="140" fill="var(--accent-color)" rx="4" ry="4"/>
    <text x="890" y="240" text-anchor="middle" class="small-text text-secondary-color">预测</text>
  </g>

  <!-- 页脚：页码和日期 -->
  <text x="80" y="1040" class="caption font-secondary text-secondary-color">4/10</text>
  <text x="1840" y="1040" text-anchor="end" class="caption font-secondary text-secondary-color">{date}</text>

</svg>