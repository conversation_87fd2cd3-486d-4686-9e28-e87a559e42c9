<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- 样式定义 -->
    <style type="text/css"><![CDATA[
      /* 颜色变量 */
      .background-color { fill: #F8FAFC; }
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .card-background { fill: #FFFFFF; }
      .card-border { stroke: #BAE6FD; }
      .container-background { fill: #E0F2FE; }

      /* 字体系统 */
      .font-primary { font-family: "Microsoft YaHei", "Segoe UI", sans-serif; }
      .font-secondary { font-family: "Source Han Sans CN", "Noto Sans CJK SC", sans-serif; }
      .font-accent { font-family: "Times New Roman", serif; }

      /* 文本样式 */
      .hero-title {
        font-size: 72px;
        font-weight: 700; /* bold */
        line-height: 1.1; /* tight */
        fill: url(#textGradient); /* 使用文本渐变 */
      }
      .main-title {
        font-size: 56px;
        font-weight: 700;
        line-height: 1.1;
        fill: url(#textGradient);
      }
      .section-title {
        font-size: 36px;
        font-weight: 600; /* semibold */
        line-height: 1.4; /* normal */
        fill: #1E293B; /* text-primary */
      }
      .content-title {
        font-size: 28px;
        font-weight: 500; /* medium */
        line-height: 1.4;
        fill: #1E293B;
      }
      .body-text {
        font-size: 22px;
        font-weight: 400; /* normal */
        line-height: 1.4;
        fill: #64748B; /* text-secondary */
      }
      .small-text {
        font-size: 16px;
        font-weight: 400;
        line-height: 1.4;
        fill: #64748B;
      }
      .caption-text {
        font-size: 14px;
        font-weight: 400;
        line-height: 1.4;
        fill: #94A3B8; /* text-light */
      }

      /* 装饰元素 */
      .deco-shape-primary {
        fill: #1E40AF; /* primary-color */
        opacity: 0.1;
      }
      .deco-shape-accent {
        fill: #3B82F6; /* accent-color */
        opacity: 0.15;
      }
      .deco-line {
        stroke: #BAE6FD; /* card-border */
        stroke-width: 1px;
      }
    ]]></style>

    <!-- 渐变定义 -->
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#F8FAFC;" />
      <stop offset="100%" style="stop-color:#E0F2FE;" />
    </linearGradient>

    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E40AF;" />
      <stop offset="100%" style="stop-color:#475569;" />
    </linearGradient>

    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;" />
      <stop offset="100%" style="stop-color:#1E40AF;" />
    </linearGradient>

    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1E3A8A;" />
      <stop offset="100%" style="stop-color:#1E40AF;" />
    </linearGradient>

    <!-- 滤镜：用于阴影效果 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="4" flood-color="rgba(0,0,0,0.1)" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)" />

  <!-- 装饰元素 -->
  <g id="decorative-elements">
    <!-- 右上角矩形 -->
    <rect x="1500" y="80" width="300" height="150" class="deco-shape-primary" rx="20" ry="20" />
    <!-- 右下角圆形 -->
    <circle cx="1700" cy="900" r="100" class="deco-shape-accent" />
    <!-- 左下角三角形 -->
    <polygon points="100,980 400,1050 200,900" class="deco-shape-primary" />
    <!-- 左上角细长矩形 -->
    <rect x="80" y="80" width="100" height="200" class="deco-shape-accent" rx="10" ry="10" />

    <!-- 贯穿页面的虚线 -->
    <line x1="0" y1="540" x2="1920" y2="540" class="deco-line" stroke-dasharray="10 20" opacity="0.3" />
    <line x1="960" y1="0" x2="960" y2="1080" class="deco-line" stroke-dasharray="10 20" opacity="0.3" />

    <!-- 具有渐变和阴影的大型几何图形 -->
    <rect x="1200" y="300" width="500" height="500" rx="30" ry="30" fill="url(#primaryGradient)" opacity="0.1" filter="url(#shadow)" />
    <polygon points="200,200 600,100 700,500 300,600" fill="url(#accentGradient)" opacity="0.15" filter="url(#shadow)" />
  </g>

  <!-- 主要内容 - 居中显示 -->
  <g id="main-content" text-anchor="middle" class="font-primary">
    <!-- 主标题 -->
    <text x="960" y="450" class="hero-title" filter="url(#shadow)">
      <tspan x="960" dy="0">{title}</tspan>
    </text>

    <!-- 副标题 -->
    <text x="960" y="550" class="section-title text-secondary">
      <tspan x="960" dy="0">{subtitle}</tspan>
    </text>
  </g>

  <!-- 品牌Logo区域 -->
  <g id="brand-logo">
    <!-- 提示：请根据需要选择使用图片Logo或文本Logo，并注释掉不需要的部分。 -->
    <!-- 如果使用图片Logo，请取消下方行的注释，并替换 {logo_url} 为您的Logo图片链接 -->
    <image x="80" y="60" width="180" height="auto" href="{logo_url}" />
    <!-- 如果使用文本Logo，请取消下方行的注释 -->
    <!-- <text x="80" y="150" class="section-title text-primary font-primary" style="font-weight: 700;">
      <tspan x="80" dy="0">您的品牌名称</tspan>
    </text> -->
  </g>

  <!-- 底部信息：日期和作者 -->
  <g id="footer-info" text-anchor="end" class="font-primary">
    <text x="1840" y="960" class="small-text caption-text">
      <tspan x="1840" dy="0">日期: {date}</tspan>
      <tspan x="1840" dy="30">作者: {author}</tspan>
    </text>
  </g>

</svg>