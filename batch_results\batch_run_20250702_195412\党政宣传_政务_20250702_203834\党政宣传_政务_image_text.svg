<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E3A8A"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E3A8A"/>
    </linearGradient>
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#F8FAFC"/>
      <stop offset="100%" stop-color="#E0F2FE"/>
    </linearGradient>
    <linearGradient id="textGradientPrimary" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E3A8A"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>
    <linearGradient id="decorativeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="rgba(59, 130, 246, 0.2)"/>
      <stop offset="50%" stop-color="rgba(59, 130, 246, 0.7)"/>
      <stop offset="100%" stop-color="rgba(59, 130, 246, 0.2)"/>
    </linearGradient>

    <!-- Filter for subtle shadow on elements -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4"/>
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="4"/>
      <feBlend in="SourceGraphic" in2="blurOut" mode="normal"/>
    </filter>

    <!-- Placeholder for an official seal/icon -->
    <g id="officialIcon">
      <circle cx="0" cy="0" r="20" fill="#1E3A8A"/>
      <path d="M-10 0 L0 -15 L10 0 L0 15 Z" fill="#F8FAFC"/>
    </g>

  </defs>

  <style>
    /* Global styles and resets */
    .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
    .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
    .font-accent { font-family: 'Times New Roman', serif; }

    /* Colors */
    .fill-primary { fill: #1E3A8A; }
    .fill-secondary { fill: #1E40AF; }
    .fill-accent { fill: #3B82F6; }
    .fill-background { fill: #F8FAFC; }
    .fill-text-primary { fill: #1E293B; }
    .fill-text-secondary { fill: #64748B; }
    .fill-text-light { fill: #94A3B8; }
    .stroke-card-border { stroke: #BAE6FD; }
    .fill-card-background { fill: #FFFFFF; }
    .fill-container-background { fill: #E0F2FE; }

    /* Font sizes and weights */
    .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
    .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
    .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; }
    .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; }
    .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
    .small-text { font-size: 16px; font-weight: 400; line-height: 1.4; }
    .caption-text { font-size: 14px; font-weight: 400; line-height: 1.4; }
    .bold { font-weight: 700; }
    .medium { font-weight: 500; }
    .semibold { font-weight: 600; }

    /* Specific element styles */
    .card {
      fill: #FFFFFF;
      stroke: #BAE6FD;
      stroke-width: 1px;
      filter: url(#shadow); /* Apply shadow filter */
    }

    .image-frame {
      stroke: #BAE6FD;
      stroke-width: 2px;
      fill: #F8FAFC; /* Placeholder background for image area */
    }

    /* Ultra-large number style */
    .ultra-number {
      font-size: 200px; /* Significantly larger */
      font-weight: 900;
      fill: url(#textGradientPrimary);
      opacity: 0.1; /* Subtle background element */
    }

    /* Decorative line style */
    .decorative-line {
      stroke: url(#decorativeGradient);
      stroke-width: 4px;
      stroke-linecap: round;
    }

  </style>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

  <!-- Header Section -->
  <g id="header">
    <!-- Logo Placeholder -->
    <rect x="80" y="60" width="150" height="40" fill="#1E3A8A"/>
    <text x="95" y="87" class="small-text font-primary" fill="#F8FAFC">
      <tspan>政务标识</tspan>
    </text>
    <!-- Actual Logo (if provided via placeholder) -->
    <!-- <image x="80" y="60" width="150" height="40" href="{logo_url}"/> -->

    <!-- Main Title -->
    <text x="960" y="120" text-anchor="middle" class="main-title font-primary fill-text-primary">
      <tspan>{title}</tspan>
    </text>
    <text x="960" y="180" text-anchor="middle" class="section-title font-secondary fill-text-secondary">
      <tspan>{subtitle}</tspan>
    </text>
  </g>

  <!-- Content Section - Image and Text Layout -->
  <g id="content-section">
    <!-- Image Column (Left) -->
    <g id="image-column">
      <rect x="80" y="280" width="704" height="600" rx="12" class="image-frame"/>
      <!-- Image Placeholder (suggested 800x600px region, scaled to fit 704x600 within column) -->
      <!-- For actual image, use: <image x="80" y="280" width="704" height="600" preserveAspectRatio="xMidYMid slice" href="{image_url}"/> -->
      <text x="432" y="580" text-anchor="middle" class="body-text font-secondary fill-text-light">
        <tspan>图片展示区域 (建议800x600px)</tspan>
      </text>
      <text x="432" y="615" text-anchor="middle" class="small-text font-secondary fill-text-light">
        <tspan>Image Display Area</tspan>
      </text>

      <!-- Decorative elements around image -->
      <rect x="50" y="250" width="30" height="30" fill="#3B82F6" rx="5"/>
      <rect x="760" y="870" width="30" height="30" fill="#1E3A8A" rx="5"/>
      <use xlink:href="#officialIcon" x="120" y="850" transform="scale(1.5)"/>
    </g>

    <!-- Text Column (Right) -->
    <g id="text-column">
      <!-- Calculated start X for text column: 80 (margin) + 704 (image width) + 100 (gap) = 884 -->
      <text x="884" y="320" class="content-title font-primary fill-text-primary">
        <tspan>深化改革，促进高质量发展</tspan>
      </text>

      <!-- Ultra-large background number -->
      <text x="1700" y="450" text-anchor="end" class="ultra-number font-primary">
        <tspan>05</tspan>
      </text>

      <text x="884" y="400" class="body-text font-secondary fill-text-secondary">
        <tspan>当前，我们正处在实现中华民族伟大复兴的关键时期。</tspan>
        <tspan x="884" dy="40">深化改革是推动国家治理体系和治理能力现代化的</tspan>
        <tspan x="884" dy="40">根本动力，也是实现经济社会高质量发展的必然选择。</tspan>
        <tspan x="884" dy="40">本年度，各项重点改革任务稳步推进，取得了显著成效。</tspan>
      </text>

      <text x="884" y="580" class="body-text font-secondary fill-text-secondary">
        <tspan>通过优化营商环境，激发市场主体活力，为经济增长</tspan>
        <tspan x="884" dy="40">注入了新的动能。同时，加强民生保障，确保发展成果</tspan>
        <tspan x="884" dy="40">惠及全体人民，提升人民群众的获得感和幸福感。</tspan>
        <tspan x="884" dy="40">这充分体现了以人民为中心的发展思想。</tspan>
      </text>

      <text x="884" y="760" class="body-text font-secondary fill-text-secondary">
        <tspan>未来，我们将继续坚持以人民为中心的发展思想，</tspan>
        <tspan x="884" dy="40">聚焦重点领域和关键环节，以更大力度、更实举措</tspan>
        <tspan x="884" dy="40">推动改革向纵深发展，为实现第二个百年奋斗目标</tspan>
        <tspan x="884" dy="40">奠定坚实基础，谱写社会主义现代化建设新篇章。</tspan>
      </text>

      <!-- Decorative line divider -->
      <line x1="884" y1="940" x2="1764" y2="940" class="decorative-line"/>

    </g>
  </g>

  <!-- Footer Section -->
  <g id="footer">
    <text x="80" y="1020" class="caption-text font-secondary fill-text-secondary">
      <tspan>{date} 发布</tspan>
    </text>
    <text x="1840" y="1020" text-anchor="end" class="caption-text font-secondary fill-text-secondary">
      <tspan>制图单位: {author}</tspan>
    </text>
    <text x="960" y="1020" text-anchor="middle" class="caption-text font-secondary fill-text-light">
      <tspan>党政宣传模板 页面 5/10</tspan>
    </text>
  </g>

</svg>