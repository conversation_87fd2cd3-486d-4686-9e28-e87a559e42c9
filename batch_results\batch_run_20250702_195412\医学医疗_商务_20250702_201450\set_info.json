{"set_name": "医学医疗_商务_20250702_201450", "scenario": "医学医疗", "style": "商务", "created_at": "2025-07-02T20:14:50.075573", "template_count": 10, "templates": [{"template_id": "医学医疗_商务_cover", "type": "封面页", "filename": "医学医疗_商务_cover.svg", "page_number": 1}, {"template_id": "医学医疗_商务_agenda", "type": "目录页", "filename": "医学医疗_商务_agenda.svg", "page_number": 2}, {"template_id": "医学医疗_商务_section_divider", "type": "章节分隔页", "filename": "医学医疗_商务_section_divider.svg", "page_number": 3}, {"template_id": "医学医疗_商务_title_content", "type": "标题内容页", "filename": "医学医疗_商务_title_content.svg", "page_number": 4}, {"template_id": "医学医疗_商务_image_text", "type": "图文混排页", "filename": "医学医疗_商务_image_text.svg", "page_number": 5}, {"template_id": "医学医疗_商务_data_display", "type": "数据展示页", "filename": "医学医疗_商务_data_display.svg", "page_number": 6}, {"template_id": "医学医疗_商务_comparison", "type": "对比分析页", "filename": "医学医疗_商务_comparison.svg", "page_number": 7}, {"template_id": "医学医疗_商务_timeline", "type": "时间线页", "filename": "医学医疗_商务_timeline.svg", "page_number": 8}, {"template_id": "医学医疗_商务_quote", "type": "引用页", "filename": "医学医疗_商务_quote.svg", "page_number": 9}, {"template_id": "医学医疗_商务_conclusion", "type": "总结页", "filename": "医学医疗_商务_conclusion.svg", "page_number": 10}], "combination_config": {"scenario": {"scenario_type": "医学医疗", "display_name": "医学医疗", "description": "医疗健康、学术研究、临床应用", "visual_characteristics": {"emphasis_on": "严谨准确、专业权威", "layout_style": "学术规范", "decorative_elements": "医疗图标、数据图表、流程图"}, "content_focus": ["临床数据", "研究成果", "治疗方案"], "target_audience": "医护人员、研究者", "tone": "scientific"}, "style": {"style_type": "商务", "display_name": "商务", "description": "专业正式，体现权威和可信度", "design_principles": {"layout": "规范布局、对称平衡", "elements": "正式图形、商务图标", "emphasis": "专业性、权威性"}, "visual_elements": {"shapes": "矩形、稳重几何形", "lines": "直线、规整边框", "decorations": "商务图标、数据图表"}, "typography": {"font_style": "正式字体", "weight": "中粗", "spacing": "标准间距"}}, "colors": {"primary": "#1E40AF", "secondary": "#475569", "accent": "#3B82F6", "background": "#F8FAFC", "text_primary": "#1E293B", "text_secondary": "#64748B"}, "fusion_rules": {"layout_priority": "scenario", "color_adaptation": "balanced", "typography_blend": "harmonious", "visual_emphasis": []}, "aesthetic_enhancements": {"typography_refinements": {"font_pairing": "complementary", "hierarchy_enhancement": true, "readability_optimization": true}, "layout_improvements": {"golden_ratio_application": true, "visual_balance_optimization": true, "spacing_harmonization": true}, "color_harmony": {"contrast_optimization": true, "accessibility_compliance": true, "emotional_resonance": true}, "visual_elements": {"icon_style_consistency": true, "decorative_element_coordination": true, "brand_element_integration": true}}}, "design_specification": {"theme_selection": {"primary_style": "商务专业 (Business Professional)", "scenario_adaptation": "医学医疗场景深度适配 (Deep Adaptation for Medical Healthcare Scenarios)", "visual_theme": "极致黑红对比下的现代医学商务风 (Modern Medical Business Style with Extreme Black-Red Contrast)", "design_philosophy": "融合医学严谨与商务专业，通过对比色彩、Bento Grid布局和超大字体，提升信息传递的权威性、清晰度和视觉冲击力，专为医护人员和研究者优化阅读体验。", "fusion_strategy": "场景与风格深度融合，以商务专业为基调，医学医疗为内容导向，强调数据准确性与视觉创新。"}, "color_palette": {"primary_color": "#1E40AF", "secondary_color": "#475569", "accent_color": "#3B82F6", "background_color": "#F8FAFC", "text_primary": "#1E293B", "text_secondary": "#64748B", "text_light": "#94A3B8", "success_color": "#10B981", "warning_color": "#F59E0B", "error_color": "#EF4444", "info_color": "#3B82F6", "gradient_primary": "linear-gradient(135deg, #1E40AF, #475569)", "gradient_accent": "linear-gradient(45deg, #3B82F6, #1E40AF)", "card_background": "#FFFFFF", "card_border": "#BAE6FD", "container_background": "#E0F2FE", "hover_color": "#7DD3FC", "active_color": "#1E40AF", "disabled_color": "#64748B"}, "layout_principles": {"canvas_size": {"width": 1920, "height": 1080}, "golden_ratio": 1.618, "grid_system": {"columns": 12, "gutter": 24, "margin": 80}, "spacing": {"xs": 4, "sm": 8, "md": 16, "lg": 24, "xl": 32, "2xl": 48, "3xl": 64, "4xl": 96}, "page_margins": {"horizontal": 80, "vertical": 60, "content_max_width": 1760}, "content_spacing": {"module_gap": 32, "section_gap": 48, "element_gap": 16, "text_gap": 12}, "visual_hierarchy": {"z_index_background": 0, "z_index_content": 10, "z_index_overlay": 20, "z_index_modal": 30}, "alignment": {"text_align": "left", "content_align": "center", "title_align": "center"}}, "typography_system": {"primary_font": "Microsoft YaHei, Segoe UI, sans-serif", "secondary_font": "Source <PERSON> CN, Noto Sans CJK SC, sans-serif", "accent_font": "Times New Roman, serif", "font_sizes": {"hero_title": 72, "main_title": 56, "section_title": 36, "content_title": 28, "body_text": 22, "small_text": 16, "caption": 14}, "font_weights": {"thin": 100, "light": 300, "normal": 400, "medium": 500, "semibold": 600, "bold": 700, "black": 900}, "line_heights": {"tight": 1.1, "normal": 1.4, "relaxed": 1.6, "loose": 1.8}, "letter_spacing": {"tight": "-0.025em", "normal": "0em", "wide": "0.025em", "wider": "0.05em"}}, "visual_elements": {"card_style": {"background": "#FFFFFF", "border_radius": 12, "border": "1px solid #BAE6FD", "shadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)", "hover_shadow": "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"}, "decorative_elements": ["几何图形装饰", "渐变背景", "图标点缀", "分割线", "阴影效果"], "gradients": {"primary_gradient": "linear-gradient(135deg, #4A86E8, #3B82F6)", "accent_gradient": "linear-gradient(45deg, #0EA5E9, #06B6D4)", "background_gradient": "linear-gradient(180deg, #F8FAFC, #E0F2FE)", "text_gradient": "linear-gradient(135deg, #1E3A8A, #1E40AF)"}, "image_effects": {"border_radius": 8, "shadow_style": "0 4px 8px rgba(0, 0, 0, 0.1)", "overlay_style": "linear-gradient(135deg, #4A86E820, #3B82F620)", "hover_transform": "scale(1.02)", "filter_effects": "brightness(1.05) contrast(1.05)"}, "logo_effects": {"positioning": "左上角或页面顶部中央", "size_guidelines": "最大高度80px，宽度自适应", "background_treatment": "透明背景或浅色背景", "shadow": "subtle drop shadow", "hover_effect": "slight glow"}, "icon_system": {"style": "outline", "stroke_width": 2, "size_sm": 16, "size_md": 24, "size_lg": 32, "color": "#4A86E8"}, "dividers": {"horizontal_line": "1px solid #BAE6FD", "vertical_line": "1px solid #BAE6FD", "decorative_divider": "波浪线或装饰性分割", "gradient_divider": "linear-gradient(90deg, transparent, #7DD3FC, transparent)"}}, "template_consistency": {"scenario_coherence": "确保所有模板在视觉元素、数据呈现和信息流上都高度契合医学医疗场景的专业性和严谨性，例如统一的图表样式、数据标注规范、以及对关键医学信息的突出。", "style_unity": "所有模板严格遵循商务风格的设计语言，包括Bento Grid布局、字体层级、黑红色彩运用和交互模式，确保品牌形象的统一性。", "color_harmony": "黑底特斯拉红高亮配色方案贯穿所有模板，辅助以蓝色系渐变和中性色，形成统一且富有冲击力的视觉体系，避免色彩冲突，强化专业感。", "visual_rhythm": "通过Bento Grid的灵活组合、超大字体的策略性应用以及统一的模块间距，创造有节奏感且易于阅读的视觉流，即使是复杂数据也能清晰呈现。", "brand_consistency": "从Logo整合到所有视觉元素的细节，都力求保持品牌识别的一致性，强化专业、创新和可靠的品牌印象，适用于医学研究和临床汇报。"}, "created_at": "2025-07-02T20:09:35.984708", "scenario": "医学医疗", "style": "商务", "canvas_size": {"width": 1920, "height": 1080}, "raw_response": "```json\n{\n    \"theme_selection\": {\n        \"primary_style\": \"商务专业 (Business Professional)\",\n        \"scenario_adaptation\": \"医学医疗场景深度适配 (Deep Adaptation for Medical Healthcare Scenarios)\",\n        \"visual_theme\": \"极致黑红对比下的现代医学商务风 (Modern Medical Business Style with Extreme Black-Red Contrast)\",\n        \"design_philosophy\": \"融合医学严谨与商务专业，通过对比色彩、Bento Grid布局和超大字体，提升信息传递的权威性、清晰度和视觉冲击力，专为医护人员和研究者优化阅读体验。\",\n        \"fusion_strategy\": \"场景与风格深度融合，以商务专业为基调，医学医疗为内容导向，强调数据准确性与视觉创新。\"\n    },\n    \"color_palette\": {\n        \"background_color\": \"#000000\",\n        \"highlight_color\": \"#E31937\",\n        \"highlight_transparent\": \"rgba(227, 25, 55, 0.7)\",\n        \"highlight_gradient_start\": \"#E31937\",\n        \"highlight_gradient_end\": \"rgba(227, 25, 55, 0.3)\",\n        \"primary_color\": \"#1E40AF\",\n        \"secondary_color\": \"#475569\",\n        \"text_primary\": \"#FFFFFF\",\n        \"text_secondary\": \"#86868B\",\n        \"text_subtle\": \"#F5F5F7\",\n        \"success_color\": \"#10B981\",\n        \"warning_color\": \"#F59E0B\",\n        \"error_color\": \"#EF4444\",\n        \"card_background\": \"#0D0D0D\",\n        \"card_border\": \"#1D1D1F\",\n        \"hover_color\": \"#E31937\"\n    },\n    \"layout_principles\": {\n        \"canvas_size\": {\"width\": 1920, \"height\": 1080},\n        \"golden_ratio_application\": true,\n        \"grid_system\": {\"columns\": 12, \"gutter\": 24, \"margin\": 80},\n        \"page_margins\": {\"horizontal\": 80, \"vertical\": 60},\n        \"content_spacing\": {\"module_gap\": 48, \"section_gap\": 72, \"element_gap\": 24},\n        \"visual_hierarchy\": \"通过Bento Grid、超大字体和黑红对比，构建清晰且富有冲击力的信息层级，引导医护人员快速获取关键数据和洞察。\",\n        \"alignment_system\": \"严格遵循网格系统，实现左右对称与视觉平衡，确保医学数据的严谨呈现和专业外观。\"\n    },\n    \"typography_system\": {\n        \"primary_font\": \"系统无衬线字体栈 (如 'PingFang SC', 'Helvetica Neue', 'Arial', 'sans-serif') 以保证跨平台兼容性、高可读性和专业性，特别适用于医学报告。\",\n        \"font_sizes\": {\n            \"hero_title\": 140,\n            \"main_title\": 84,\n            \"section_title\": 56,\n            \"content_title\": 38,\n            \"body_text\": 28,\n            \"small_text\": 20,\n            \"accent_number\": 200\n        },\n        \"font_weights\": {\n            \"title\": \"ExtraBold\",\n            \"content\": \"Regular\",\n            \"emphasis\": \"SemiBold\",\n            \"chinese\": \"Heavy\",\n            \"english\": \"Light\"\n        },\n        \"line_heights\": {\n            \"title\": 1.05,\n            \"content\": 1.6,\n            \"dense\": 1.4\n        },\n        \"mixed_typography\": {\n            \"chinese_style\": \"超大号加粗，极致视觉冲击力，用于核心概念或标题。\",\n            \"english_style\": \"小号纤细，作为点缀、专业术语补充或数据单位。\",\n            \"number_style\": \"超大号特斯拉红高亮，作为数据核心焦点，强调临床指标或研究成果。\"\n        },\n        \"readability_optimization\": \"优化对比度与行间距，确保在长时间阅读医学报告时眼睛不易疲劳；中英文混排提升国际化专业度，并遵循严格的文字间距和溢出处理规范。\"\n    },\n    \"visual_elements\": {\n        \"scenario_specific_elements\": \"心电图线条、DNA螺旋、细胞结构、医疗器械抽象剪影、医学符号（如十字、蛇杖变体）等，以简洁线条或低透明度形式融入。\",\n        \"style_characteristics\": \"极简主义、几何对称、高对比度、清晰的模块化、未来科技感，完美融合商务的严谨与医疗的精准。\",\n        \"bento_grid_layout\": \"采用灵活多变的Bento Grid布局，将关键数据、图表、文字内容以不同大小的卡片形式组合，创造动态且平衡的视觉体验，尤其适合医学报告的多维数据展示。\",\n        \"black_red_theme\": \"纯黑色背景(#000000)与特斯拉红色(#E31937)形成强烈视觉对比，营造高端、权威、现代的医学科技氛围。\",\n        \"oversized_typography\": \"关键数字、百分比或核心结论使用超大字号（如180-200px）并以特斯拉红高亮，瞬间抓住医护人员的注意力，强化数据洞察。\",\n        \"decorative_elements\": [\n            \"简洁线条图形元素：用于图表、流程图、时间轴，以特斯拉红或辅助色勾勒，清晰且不干扰内容，如心跳曲线、数据流线。\",\n            \"特斯拉红色透明度渐变元素：如背景中的微弱光晕、卡片边缘的柔和渐变、数据柱图的填充色，营造科技感和深度，避免多色渐变。\",\n            \"中英文混排排版元素：中文标题粗大醒目，英文副标题或专业术语纤细精致，形成鲜明对比，提升国际化和专业感。\",\n            \"符合商务风格的装饰元素：如精致的分割线、内敛的几何图案、带有微弱光泽的按钮或卡片边缘。\",\n            \"适合医学医疗场景的装饰元素：抽象的生物结构、数据流线、健康脉搏图等，以低透明度融入背景或卡片，加强专业识别度。\"\n        ],\n        \"card_style\": {\n            \"background\": \"#0D0D0D\",\n            \"border_radius\": \"统一的适中圆角 (如 `rx='20'`)，赋予卡片现代感和亲和力，避免过于锐利，符合商务专业气质。\",\n            \"shadow\": \"轻微的内阴影或外阴影，使用背景色系深色透明度变化，增加卡片浮动感而不失专业，避免分散注意力。\",\n            \"border\": \"1.5px宽的特斯拉红色细边框，或由特斯拉红渐变至透明的边框，强调卡片内容，增加科技感和视觉深度。\"\n        },\n        \"image_integration\": {\n            \"border_radius\": 12,\n            \"shadow_style\": \"极简的暗色阴影（如 `opacity='0.2'` 的 `drop-shadow`），不喧宾夺主，保持图像清晰度。\",\n            \"overlay_style\": \"在特定情境下，可使用特斯拉红的半透明遮罩（`opacity='0.3'`），用于图片与文字的融合或强调，特别是医学影像。\",\n            \"bento_grid_placement\": \"图片作为Bento Grid中的一个模块，尺寸和位置与网格系统完美对齐，可全宽或部分占据单元格，支持高质量医学影像展示，确保图像的视觉焦点。\"\n        },\n        \"logo_treatment\": {\n            \"positioning\": \"通常位于页面左上角或右下角的固定区域，保持品牌可见性，不干扰核心内容，符合商务演示规范。\",\n            \"size_guidelines\": \"尺寸适中，确保在不同缩放比例下清晰可辨，但不过大，保持专业和内敛。\",\n            \"integration_style\": \"Logo颜色可适应黑底白字或特斯拉红高亮，确保与整体主题和谐统一，可考虑单色或负形处理，增加高级感。\",\n            \"animation_hint\": \"滚动时Logo可轻微淡入淡出或缩小，模仿Apple官网的精致动效，提升用户体验和品牌记忆。\"\n        }\n    },\n    \"template_consistency\": {\n        \"scenario_coherence\": \"确保所有模板在视觉元素、数据呈现和信息流上都高度契合医学医疗场景的专业性和严谨性，例如统一的图表样式、数据标注规范、以及对关键医学信息的突出。\",\n        \"style_unity\": \"所有模板严格遵循商务风格的设计语言，包括Bento Grid布局、字体层级、黑红色彩运用和交互模式，确保品牌形象的统一性。\",\n        \"color_harmony\": \"黑底特斯拉红高亮配色方案贯穿所有模板，辅助以蓝色系渐变和中性色，形成统一且富有冲击力的视觉体系，避免色彩冲突，强化专业感。\",\n        \"visual_rhythm\": \"通过Bento Grid的灵活组合、超大字体的策略性应用以及统一的模块间距，创造有节奏感且易于阅读的视觉流，即使是复杂数据也能清晰呈现。\",\n        \"brand_consistency\": \"从Logo整合到所有视觉元素的细节，都力求保持品牌识别的一致性，强化专业、创新和可靠的品牌印象，适用于医学研究和临床汇报。\"\n    }\n}\n```"}}