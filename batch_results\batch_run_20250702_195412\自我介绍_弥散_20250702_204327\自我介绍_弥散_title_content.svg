<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Color Palette -->
    <style type="text/css">
      :root {
        --primary-color: #3B82F6;
        --secondary-color: #1E40AF;
        --accent-color: #06B6D4;
        --background-color: #F8FAFC;
        --text-primary: #1E293B;
        --text-secondary: #64748B;
        --text-light: #94A3B8;
        --card-background: #FFFFFF;
        --card-border: #BAE6FD;
        --container-background: #E0F2FE;
        --hover-color: #7DD3FC;
      }

      /* Font System */
      .font-primary { font-family: "Microsoft YaHei", "Segoe UI", sans-serif; }
      .font-secondary { font-family: "Source Han Sans CN", "Noto Sans CJK SC", sans-serif; }
      .font-accent { font-family: "Times New Roman", serif; }

      /* Font Sizes and Weights */
      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; fill: var(--text-primary); } /* Bold, for large emphasis numbers */
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; fill: var(--text-primary); } /* Bold */
      .section-title { font-size: 36px; font-weight: 600; line-height: 1.4; fill: var(--text-primary); } /* Semibold */
      .content-title { font-size: 28px; font-weight: 500; line-height: 1.4; fill: var(--text-primary); } /* Medium */
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; fill: var(--text-secondary); } /* Normal */
      .body-text-primary { font-size: 22px; font-weight: 400; line-height: 1.6; fill: var(--text-primary); } /* Normal */
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; fill: var(--text-secondary); } /* Normal */
      .caption { font-size: 14px; font-weight: 400; line-height: 1.6; fill: var(--text-light); } /* Normal */
      .list-item-text { font-size: 22px; font-weight: 400; line-height: 1.6; fill: var(--text-primary); } /* Normal, specifically for list items */
      .list-item-text-secondary { font-size: 16px; font-weight: 400; line-height: 1.6; fill: var(--text-secondary); } /* Normal, for English subtext */


      /* General Styles */
      .card-background { fill: var(--card-background); stroke: var(--card-border); stroke-width: 1px; }
      .card-shadow { filter: url(#drop-shadow); }

      /* Diffuse Background Gradients */
      .bg-gradient-1 {
        stop-color: #F8FAFC; /* Background color */
      }
      .bg-gradient-2 {
        stop-color: #E0F2FE; /* Container background */
      }
      .diffuse-shape-fill-primary {
        fill: url(#PrimaryDiffuseGradient);
        opacity: 0.6; /* Softness */
      }
      .diffuse-shape-fill-accent {
        fill: url(#AccentDiffuseGradient);
        opacity: 0.5; /* Softness */
      }

      /* Icons */
      .icon-color { fill: var(--primary-color); }
      .icon-stroke { stroke: var(--primary-color); stroke-width: 2; }
    </style>

    <!-- Diffuse Background Gradient -->
    <linearGradient id="BackgroundGradient" x1="960" y1="0" x2="960" y2="1080" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="var(--background-color)"/>
      <stop offset="1" stop-color="var(--container-background)"/>
    </linearGradient>

    <!-- Primary Diffuse Gradient for shapes -->
    <linearGradient id="PrimaryDiffuseGradient" x1="0" y1="0" x2="1" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.8"/>
      <stop offset="100%" stop-color="#1E40AF" stop-opacity="0.4"/>
    </linearGradient>

    <!-- Accent Diffuse Gradient for shapes -->
    <linearGradient id="AccentDiffuseGradient" x1="0" y1="0" x2="1" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0%" stop-color="#06B6D4" stop-opacity="0.7"/>
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0.3"/>
    </linearGradient>

    <!-- Soft Drop Shadow Filter for cards/elements -->
    <filter id="drop-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="8"/>
      <feOffset dx="0" dy="4"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Icon for List Items (simple circle) -->
    <symbol id="BulletIcon" viewBox="0 0 24 24">
      <circle cx="12" cy="12" r="6" fill="var(--accent-color)"/>
    </symbol>
  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#BackgroundGradient)"/>

  <!-- Diffuse background shapes (subtle, flowing) -->
  <g opacity="0.4">
    <ellipse cx="200" cy="800" rx="250" ry="150" transform="rotate(-15 200 800)" fill="url(#PrimaryDiffuseGradient)" filter="url(#drop-shadow)"/>
    <ellipse cx="1700" cy="200" rx="300" ry="200" transform="rotate(25 1700 200)" fill="url(#AccentDiffuseGradient)" filter="url(#drop-shadow)"/>
    <rect x="800" y="900" width="400" height="150" rx="75" transform="rotate(5 1000 975)" fill="url(#AccentDiffuseGradient)" filter="url(#drop-shadow)"/>
  </g>

  <!-- Main Content Area - Centered Card -->
  <rect x="160" y="100" width="1600" height="880" rx="24" class="card-background card-shadow"/>

  <!-- Logo Placeholder -->
  <!-- Assuming logo is small and at top left within the card or canvas margin -->
  <image href="{logo_url}" x="220" y="160" width="120" height="auto"/>

  <!-- Page Title -->
  <text x="380" y="220" class="section-title font-primary">
    <tspan>{title}</tspan>
  </text>
  <text x="380" y="265" class="content-title font-secondary">
    <tspan>{subtitle}</tspan>
  </text>

  <!-- Main Content Paragraph -->
  <!-- Start Y for content after title + 60px spacing + title height -->
  <!-- Title ends at y=220 (section-title, approx 36px height), subtitle at y=265 (content-title, approx 28px height).
       Content starts at 265 + 28 (subtitle height) + 60 (required spacing) = 353. Using 360 for cleaner alignment.
       Body text is 22px, line-height 1.6 (approx 35.2px per line). Using dy=35 for multi-line.
  -->
  <text x="220" y="360" class="body-text font-secondary">
    <tspan x="220" dy="0">{content}</tspan>
    <tspan x="220" dy="35">这是关于个人经历、专业技能和个人优势的详细描述。</tspan>
    <tspan x="220" dy="35">旨在向面试官和同事展示我的核心能力和价值。</tspan>
    <tspan x="220" dy="35">我的工作理念是持续学习和创新，追求卓越的成果。</tspan>
  </text>

  <!-- Key Points Section Title -->
  <!-- Position after content: 360 (content start Y) + (4 lines * 35px line height) + 60px (spacing) = 360 + 140 + 60 = 560 -->
  <text x="220" y="560" class="content-title font-primary">
    <tspan>核心优势和技能</tspan>
    <tspan x="220" dy="30" class="small-text font-secondary">Core Strengths and Skills</tspan>
  </text>

  <!-- Bullet Point List -->
  <!-- Start Y for list items after "核心优势和技能" title (28px) + English subtext (16px) + 40px spacing = 560 + 28 + 16 + 40 = 644 -->
  <!-- List item spacing at least 35px. Using 45px for better readability, and 24px for icon height. -->
  <g class="list-container">
    <!-- Item 1 -->
    <use href="#BulletIcon" x="220" y="644" width="24" height="24"/>
    <text x="260" y="664" class="list-item-text font-primary">
      <tspan>专业技能精通：掌握多项核心技术和工具。</tspan>
      <tspan x="260" dy="25" class="list-item-text-secondary">Proficient in multiple core technologies and tools.</tspan>
    </text>

    <!-- Item 2 -->
    <!-- Calculation for next item Y: Previous item Y (644) + icon height (24) + item text height (22+16) + spacing (45) = 644 + 24 + 38 + 45 = 751 -->
    <use href="#BulletIcon" x="220" y="751" width="24" height="24"/>
    <text x="260" y="771" class="list-item-text font-primary">
      <tspan>项目管理经验：成功领导和交付多个复杂项目。</tspan>
      <tspan x="260" dy="25" class="list-item-text-secondary">Successfully led and delivered multiple complex projects.</tspan>
    </text>

    <!-- Item 3 -->
    <use href="#BulletIcon" x="220" y="751 + 24 + 38 + 45" width="24" height="24"/>
    <text x="260" y="771 + 24 + 38 + 45" class="list-item-text font-primary">
      <tspan>创新解决能力：善于发现问题并提供创造性解决方案。</tspan>
      <tspan x="260" dy="25" class="list-item-text-secondary">Adept at problem-solving and creative solutions.</tspan>
    </text>

    <!-- Item 4 -->
    <use href="#BulletIcon" x="220" y="751 + 2*(24 + 38 + 45)" width="24" height="24"/>
    <text x="260" y="771 + 2*(24 + 38 + 45)" class="list-item-text font-primary">
      <tspan>团队协作精神：有效沟通和协作，推动团队目标达成。</tspan>
      <tspan x="260" dy="25" class="list-item-text-secondary">Strong teamwork and collaborative spirit.</tspan>
    </text>
  </g>

  <!-- Example of a large number / emphasis element -->
  <!-- Placed to the right of the main content, respecting overall layout and avoiding overlap -->
  <text x="1400" y="400" class="hero-title font-accent" fill="url(#AccentDiffuseGradient)" text-anchor="middle">
    <tspan>5+</tspan>
  </text>
  <text x="1400" y="460" class="section-title font-primary" text-anchor="middle">
    <tspan>年经验</tspan>
  </text>
  <text x="1400" y="500" class="small-text font-secondary" text-anchor="middle">
    <tspan>Years of Experience</tspan>
  </text>

  <!-- Another decorative element: Simple outline graphic with data focus -->
  <rect x="1300" y="600" width="200" height="200" rx="20" stroke="var(--accent-color)" stroke-width="3" fill="none" opacity="0.7"/>
  <circle cx="1400" cy="700" r="60" stroke="var(--primary-color)" stroke-width="3" fill="none" opacity="0.7"/>
  <text x="1400" y="705" class="content-title font-primary" text-anchor="middle" fill="var(--primary-color)">
    <tspan>100%</tspan>
  </text>
  <text x="1400" y="740" class="small-text font-secondary" text-anchor="middle">
    <tspan>Commitment</tspan>
  </text>

  <!-- Footer/Author/Date Placeholder (bottom right of the card) -->
  <text x="1700" y="940" class="small-text font-secondary" text-anchor="end">
    <tspan>{author}</tspan>
  </text>
  <text x="1700" y="960" class="caption font-secondary" text-anchor="end">
    <tspan>{date}</tspan>
  </text>

</svg>