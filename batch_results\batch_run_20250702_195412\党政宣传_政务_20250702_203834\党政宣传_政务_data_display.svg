<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette -->
    <style type="text/css">
      .primary-color { fill: #1E3A8A; }
      .secondary-color { fill: #1E40AF; }
      .accent-color { fill: #3B82F6; }
      .background-color { fill: #F8FAFC; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .card-background { fill: #FFFFFF; }
      .card-border { stroke: #BAE6FD; stroke-width: 1px; }

      /* Gradients */
      .gradient-primary-bg { fill: url(#primaryGradient); }
      .gradient-accent-line { stroke: url(#accentGradient); stroke-width: 3; }

      /* Font System */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
      .section-title { font-size: 36px; font-weight: 600; line-height: 1.4; }
      .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; }
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.4; }
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.4; }
      .caption { font-size: 14px; font-weight: 400; line-height: 1.4; }
      .font-bold { font-weight: 700; }
      .font-semibold { font-weight: 600; }

      /* Card Shadow (simplified for SVG using filter) */
      .card-shadow { filter: url(#dropShadow); }

      /* Icon Style */
      .icon-stroke { stroke: #3B82F6; stroke-width: 2; fill: none; stroke-linecap: round; stroke-linejoin: round; }
    </style>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E3A8A" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E3A8A" />
    </linearGradient>
    <linearGradient id="accentTransparentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.8" />
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0" />
    </linearGradient>

    <!-- Drop Shadow Filter -->
    <filter id="dropShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="4" result="blur" />
      <feOffset dx="0" dy="4" result="offsetBlur" />
      <feFlood flood-color="rgba(0, 0, 0, 0.1)" result="color" />
      <feComposite in="color" in2="offsetBlur" operator="in" result="shadow" />
      <feMerge>
        <feMergeNode in="shadow" />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>
  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="background-color" />

  <!-- Decorative Elements: Top Left & Bottom Right abstract shapes -->
  <circle cx="100" cy="100" r="80" class="accent-color" opacity="0.05" />
  <rect x="1800" y="980" width="80" height="80" class="primary-color" opacity="0.05" transform="rotate(45 1840 1020)" />

  <!-- Header Section -->
  <g id="header-section">
    <text x="960" y="140" text-anchor="middle" class="main-title font-primary text-primary">{title}</text>
    <text x="960" y="200" text-anchor="middle" class="section-title font-primary text-secondary">{subtitle}</text>
  </g>

  <!-- Main Content Area: Data Visualization and Statistics (Bento Grid feel) -->
  <g id="main-content-area">
    <!-- Left Panel: Main Chart Area -->
    <rect x="80" y="280" width="1000" height="720" rx="12" ry="12" class="card-background card-border card-shadow" />
    <g id="chart-area-1">
      <text x="120" y="330" class="content-title font-primary text-primary">年度工作成果总览</text>
      <text x="120" y="365" class="small-text font-primary text-secondary">Annual Work Achievements Overview</text>

      <!-- Bar Chart Example -->
      <g id="bar-chart" transform="translate(180, 420)">
        <!-- X-axis labels -->
        <text x="0" y="250" class="small-text text-secondary" text-anchor="middle">2021</text>
        <text x="180" y="250" class="small-text text-secondary" text-anchor="middle">2022</text>
        <text x="360" y="250" class="small-text text-secondary" text-anchor="middle">2023</text>
        <text x="540" y="250" class="small-text text-secondary" text-anchor="middle">2024</text>

        <!-- Y-axis labels -->
        <text x="-20" y="200" class="small-text text-secondary" text-anchor="end">0</text>
        <text x="-20" y="150" class="small-text text-secondary" text-anchor="end">25%</text>
        <text x="-20" y="100" class="small-text text-secondary" text-anchor="end">50%</text>
        <text x="-20" y="50" class="small-text text-secondary" text-anchor="end">75%</text>
        <text x="-20" y="0" class="small-text text-secondary" text-anchor="end">100%</text>

        <!-- Bars (example data) - base Y is 220, so Y = 220 - height -->
        <rect x="0" y="100" width="60" height="120" class="accent-color" opacity="0.8" /> <!-- 2021: 60% -->
        <rect x="180" y="60" width="60" height="160" class="accent-color" opacity="0.8" /> <!-- 2022: 80% -->
        <rect x="360" y="40" width="60" height="180" class="accent-color" opacity="0.8" /> <!-- 2023: 90% -->
        <rect x="540" y="20" width="60" height="200" class="accent-color" opacity="0.8" /> <!-- 2024: 100% -->

        <!-- Highlighted Bar for 2024 with value -->
        <rect x="540" y="20" width="60" height="200" class="accent-color" opacity="1" />
        <text x="570" y="10" text-anchor="middle" class="small-text font-bold text-primary">100%</text>

        <!-- Baseline -->
        <line x1="-30" y1="220" x2="600" y2="220" stroke="#BAE6FD" stroke-width="1" />
      </g>

      <!-- Chart Description -->
      <text x="120" y="720" class="body-text font-primary text-primary">
        <tspan x="120" dy="0">此图展示了近年来各项工作指标的完成情况。</tspan>
        <tspan x="120" dy="30">我们持续优化策略，确保实现预期目标。</tspan>
      </text>

      <!-- Key Data Highlight -->
      <g id="key-data-highlight" transform="translate(120, 820)">
        <text x="0" y="0" class="caption font-primary text-secondary">核心指标提升</text>
        <text x="0" y="40" class="hero-title font-bold text-primary">
          <tspan fill="#3B82F6">150%</tspan>
        </text>
        <text x="250" y="0" class="caption font-primary text-secondary">服务覆盖率</text>
        <text x="250" y="40" class="hero-title font-bold text-primary">
          <tspan fill="#3B82F6">98%</tspan>
        </text>
      </g>
    </g>

    <!-- Right Panel: Data Cards -->
    <g id="data-cards-area">
      <!-- Top Card -->
      <rect x="1100" y="280" width="740" height="340" rx="12" ry="12" class="card-background card-border card-shadow" />
      <g id="card-1">
        <text x="1140" y="330" class="content-title font-primary text-primary">政策实施进展</text>
        <text x="1140" y="365" class="small-text font-primary text-secondary">Policy Implementation Progress</text>

        <!-- Icon: Checkmark -->
        <circle cx="1140" cy="450" r="30" class="primary-color" opacity="0.1" />
        <path d="M1125 450 L1135 460 L1155 435" class="icon-stroke" />

        <text x="1200" y="450" class="body-text font-primary text-secondary">已完成政策制定</text>
        <text x="1200" y="480" class="hero-title font-bold text-primary">
          <tspan fill="#3B82F6">35</tspan> 项
        </text>

        <text x="1500" y="450" class="body-text font-primary text-secondary">正在落实政策</text>
        <text x="1500" y="480" class="hero-title font-bold text-primary">
          <tspan fill="#3B82F6">12</tspan> 项
        </text>
      </g>

      <!-- Bottom Card -->
      <rect x="1100" y="660" width="740" height="340" rx="12" ry="12" class="card-background card-border card-shadow" />
      <g id="card-2">
        <text x="1140" y="710" class="content-title font-primary text-primary">党建工作成效</text>
        <text x="1140" y="745" class="small-text font-primary text-secondary">Party Building Effectiveness</text>

        <!-- Icon: Plus sign -->
        <circle cx="1140" cy="830" r="30" class="primary-color" opacity="0.1" />
        <path d="M1140 810 L1140 850 M1120 830 L1160 830" class="icon-stroke" />

        <text x="1200" y="830" class="body-text font-primary text-secondary">党员参与率</text>
        <text x="1200" y="860" class="hero-title font-bold text-primary">
          <tspan fill="#3B82F6">99.5%</tspan>
        </text>

        <text x="1500" y="830" class="body-text font-primary text-secondary">支部活动数量</text>
        <text x="1500" y="860" class="hero-title font-bold text-primary">
          <tspan fill="#3B82F6">200+</tspan>
        </text>
      </g>
    </g>
  </g>

  <!-- Bottom Decorative Line (Gradient) -->
  <rect x="80" y="1040" width="1760" height="4" class="gradient-primary-bg" rx="2" ry="2" />

  <!-- Content Placeholders (for easy editing, hidden from view) -->
  <text x="0" y="0" visibility="hidden" id="placeholder-title">{title}</text>
  <text x="0" y="0" visibility="hidden" id="placeholder-subtitle">{subtitle}</text>
  <text x="0" y="0" visibility="hidden" id="placeholder-content">{content}</text>
  <text x="0" y="0" visibility="hidden" id="placeholder-image">{image_url}</text>
  <text x="0" y="0" visibility="hidden" id="placeholder-logo">{logo_url}</text>
  <text x="0" y="0" visibility="hidden" id="placeholder-date">{date}</text>
  <text x="0" y="0" visibility="hidden" id="placeholder-author">{author}</text>
</svg>