<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Styles for corporate/business theme with unified blue palette -->
    <style type="text/css">
      <![CDATA[
        /* Color Palette */
        .bg-color { fill: #F8FAFC; }
        .primary-color { fill: #1E40AF; }
        .secondary-color { fill: #475569; }
        .accent-color { fill: #3B82F6; }
        .text-primary-color { fill: #1E293B; }
        .text-secondary-color { fill: #64748B; }
        .text-light-color { fill: #94A3B8; }
        .card-bg { fill: #FFFFFF; }
        .card-border { stroke: #BAE6FD; }
        .container-bg { fill: #E0F2FE; }

        /* Font System */
        .font-primary { font-family: "Microsoft YaHei", "Segoe UI", sans-serif; }
        .font-secondary { font-family: "Source Han Sans CN", "Noto Sans CJK SC", sans-serif; }
        .font-accent { font-family: "Times New Roman", serif; }

        .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; } /* bold */
        .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; } /* bold */
        .section-title { font-size: 36px; font-weight: 600; line-height: 1.4; } /* semibold */
        .content-title { font-size: 28px; font-weight: 500; line-height: 1.4; } /* medium */
        .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; } /* normal */
        .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; } /* normal */
        .caption-text { font-size: 14px; font-weight: 400; line-height: 1.6; } /* normal */

        /* General Text Styles */
        .text-bold { font-weight: 700; }
        .text-medium { font-weight: 500; }

        /* Card Styles */
        .card-shadow { filter: url(#dropShadow); }

        /* Icon Styles (conceptual, represented by simple shapes) */
        .icon-style { stroke: #4A86E8; stroke-width: 2px; fill: none; }

        /* Gradients */
        .primary-gradient-fill { fill: url(#primaryGradient); }
        .accent-gradient-fill { fill: url(#accentGradient); }
        .text-gradient-fill { fill: url(#textGradient); }

        /* Decorative elements */
        .deco-line { stroke: #BAE6FD; stroke-width: 1px; }
      ]]>
    </style>

    <!-- Drop Shadow Filter for cards -->
    <filter id="dropShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4" />
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="3" />
      <feColorMatrix result="matrixOut" in="blurOut" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" />
      <feBlend in="SourceGraphic" in2="matrixOut" mode="normal" />
    </filter>

    <!-- Primary Gradient -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E40AF" />
      <stop offset="100%" style="stop-color:#475569" />
    </linearGradient>

    <!-- Accent Gradient -->
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6" />
      <stop offset="100%" style="stop-color:#1E40AF" />
    </linearGradient>

    <!-- Text Gradient -->
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1E3A8A" />
      <stop offset="100%" style="stop-color:#1E40AF" />
    </linearGradient>

    <!-- High-light color transparency gradient for tech feel (using accent color) -->
    <linearGradient id="techHighlightGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:0.2" />
      <stop offset="50%" style="stop-color:#3B82F6;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:0.2" />
    </linearGradient>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color" />

  <!-- Decorative Background Elements (Subtle, tech-like) -->
  <rect x="100" y="900" width="100" height="4" class="accent-color" opacity="0.3" rx="2" ry="2"/>
  <rect x="1700" y="100" width="100" height="4" class="accent-color" opacity="0.3" rx="2" ry="2"/>
  <circle cx="100" cy="100" r="20" class="primary-color" opacity="0.1"/>
  <circle cx="1820" cy="980" r="30" class="accent-color" opacity="0.15"/>
  <rect x="150" y="800" width="1600" height="2" class="deco-line" opacity="0.5"/>


  <!-- Header Section (Logo and Page Number) -->
  <g id="header">
    <!-- Logo Placeholder (Top Left) -->
    <rect x="80" y="60" width="180" height="60" fill="#E0F2FE" rx="8" ry="8" class="card-shadow" />
    <text x="170" y="100" text-anchor="middle" class="text-primary-color content-title font-primary text-bold">
      {logo_url}
    </text>

    <!-- Page Number (Top Right) -->
    <text x="1840" y="100" text-anchor="end" class="text-secondary-color small-text font-secondary">
      页面 5/10
    </text>
  </g>

  <!-- Main Content Area - Image and Text Balance -->
  <g id="main-content">
    <!-- Left Column: Image Display Area -->
    <g id="image-column">
      <rect x="80" y="200" width="700" height="500" class="card-bg card-border card-shadow" rx="12" ry="12" />
      <!-- Image Placeholder -->
      <!-- Note: For actual image display, replace the text with <image x="95" y="215" width="670" height="470" xlink:href="{image_url}" preserveAspectRatio="xMidYMid slice" /> -->
      <rect x="95" y="215" width="670" height="470" fill="#E0F2FE" rx="8" ry="8" />
      <text x="430" y="450" text-anchor="middle" class="text-secondary-color content-title font-primary">
        图片展示区域
      </text>
      <text x="430" y="480" text-anchor="middle" class="text-light-color small-text font-primary">
        (建议 800x600px 图像)
      </text>

      <!-- Decorative element on image card -->
      <rect x="80" y="690" width="700" height="10" class="accent-gradient-fill" rx="0" ry="0" opacity="0.8"/>
    </g>

    <!-- Right Column: Text Content Area -->
    <g id="text-column">
      <!-- Title -->
      <text x="940" y="250" class="text-primary-color main-title font-primary text-bold">
        <tspan>
          {title}
        </tspan>
      </text>

      <!-- Subtitle/Accent Text -->
      <text x="940" y="310" class="accent-color section-title font-primary text-medium">
        <tspan>
          {subtitle}
        </tspan>
      </text>

      <!-- Main Body Content -->
      <text x="940" y="380" class="text-secondary-color body-text font-secondary">
        <tspan>{content}</tspan>
        <tspan x="940" dy="35">
          这里是企业介绍的核心内容，详细阐述公司的发展历程和核心业务。
        </tspan>
        <tspan x="940" dy="35">
          我们专注于创新和卓越，致力于为客户提供高质量的解决方案。
        </tspan>
        <tspan x="940" dy="35">
          团队的专业实力是我们取得成功的关键，不断提升服务标准。
        </tspan>
        <tspan x="940" dy="35">
          我们秉承诚信、协作、共赢的企业文化，与合作伙伴共同成长。
        </tspan>
        <tspan x="940" dy="35">
          未来，我们将继续深耕行业，拓展市场，实现可持续发展。
        </tspan>
      </text>

      <!-- Large Number/Data Point (Enhanced aesthetics) -->
      <text x="940" y="700" class="text-primary-color hero-title font-accent text-bold" fill="url(#textGradient)">
        <tspan>
          {date}
        </tspan>
      </text>
      <text x="1100" y="700" class="text-secondary-color body-text font-primary text-bold">
        <tspan>年</tspan>
      </text>
      <text x="940" y="750" class="accent-color small-text font-primary">
        <tspan>里程碑 Milestone</tspan>
      </text>

      <!-- Author/Additional Info -->
      <text x="940" y="800" class="text-light-color small-text font-secondary">
        <tspan>内容作者: {author}</tspan>
      </text>
    </g>
  </g>

  <!-- Decorative elements simulating data visualization/tech feel -->
  <g id="decorative-graphics">
    <!-- Simple bar chart like elements -->
    <rect x="80" y="780" width="100" height="10" class="primary-color" opacity="0.6" rx="3" ry="3"/>
    <rect x="80" y="800" width="150" height="10" class="secondary-color" opacity="0.6" rx="3" ry="3"/>
    <rect x="80" y="820" width="200" height="10" class="accent-color" opacity="0.6" rx="3" ry="3"/>

    <!-- Gradient line (tech highlight) -->
    <rect x="0" y="950" width="1920" height="10" fill="url(#techHighlightGradient)" />
  </g>

</svg>