<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      /* Colors */
      .background-color { fill: #F8FAFC; }
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .card-background { fill: #FFFFFF; }
      .card-border { stroke: #BAE6FD; }

      /* Fonts */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      /* Note: accent_font 'Times New Roman' is not used for this template's primary text, adhering to business style. */

      /* Font Sizes and Weights */
      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; } /* bold */
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; } /* bold */
      .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; } /* bold */
      .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; } /* semibold */
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; } /* normal */
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; } /* normal */
      .caption { font-size: 14px; font-weight: 400; line-height: 1.6; } /* normal */

      /* Icon Styles */
      .icon-style { stroke: #3B82F6; stroke-width: 2; fill: none; }

      /* Card Styles */
      .card-style {
        fill: #FFFFFF;
        stroke: #BAE6FD;
        stroke-width: 1px;
        filter: url(#shadow); /* Apply shadow filter */
      }
      /* Specific style for active card border */
      .card-active-border {
        stroke: #3B82F6;
        stroke-width: 2px;
      }

      /* Gradients for background elements */
      /* Note: Gradient colors are consistent with the blue palette, not conflicting with the "no cross-color gradients" rule for highlights. */
      .gradient-bg-stop1 { stop-color: #1E40AF; }
      .gradient-bg-stop2 { stop-color: #475569; }
      .gradient-accent-stop1 { stop-color: #3B82F6; }
      .gradient-accent-stop2 { stop-color: #1E40AF; }

    </style>

    <!-- Shadow filter for cards -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="4"/>
      <feGaussianBlur stdDeviation="3"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Linear Gradient for subtle background pattern -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" class="gradient-bg-stop1" stop-opacity="0.1"/>
      <stop offset="100%" class="gradient-bg-stop2" stop-opacity="0.05"/>
    </linearGradient>

    <!-- Accent Gradient for highlight elements, as per provided spec -->
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" class="gradient-accent-stop1"/>
      <stop offset="100%" class="gradient-accent-stop2"/>
    </linearGradient>

    <!-- Icon Definitions (placeholders, using outline style) -->
    <symbol id="icon-briefcase" viewBox="0 0 24 24">
      <path d="M16 6H8C6.89543 6 6 6.89543 6 8V20C6 21.1046 6.89543 22 8 22H16C17.1046 22 18 21.1046 18 20V8C18 6.89543 17.1046 6 16 6Z" class="icon-style"/>
      <path d="M4 10C4 8.89543 4.89543 8 6 8H18C19.1046 8 20 8.89543 20 10V14C20 15.1046 19.1046 16 18 16H6C4.89543 16 4 15.1046 4 14V10Z" class="icon-style"/>
      <path d="M9 2L15 2V6L9 6L9 2Z" class="icon-style"/>
    </symbol>
    <symbol id="icon-culture" viewBox="0 0 24 24">
      <path d="M12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2Z" class="icon-style"/>
      <path d="M12 6V18" class="icon-style"/>
      <path d="M9 9L15 15" class="icon-style"/>
      <path d="M15 9L9 15" class="icon-style"/>
    </symbol>
    <symbol id="icon-team" viewBox="0 0 24 24">
      <circle cx="9" cy="7" r="4" class="icon-style"/>
      <path d="M5.5 16C6.5 13.5 9 12 12 12C15 12 17.5 13.5 18.5 16" class="icon-style"/>
      <circle cx="16" cy="17" r="3" class="icon-style"/>
      <path d="M14 20C14.5 18.5 16 17 18 17C20 17 21.5 18.5 22 20" class="icon-style"/>
    </symbol>
    <symbol id="icon-history" viewBox="0 0 24 24">
      <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" class="icon-style"/>
      <path d="M12 6V12L16 14" class="icon-style"/>
    </symbol>
    <symbol id="icon-advantage" viewBox="0 0 24 24">
      <path d="M12 2L18 12L12 22L6 12L12 2Z" class="icon-style"/>
      <path d="M12 2V22" class="icon-style"/>
      <path d="M6 12H18" class="icon-style"/>
    </symbol>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="background-color"/>

  <!-- Subtle Decorative Background Elements with Gradients -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#primaryGradient)" opacity="0.4"/>
  <circle cx="1700" cy="100" r="150" fill="url(#accentGradient)" opacity="0.1"/>
  <circle cx="200" cy="980" r="100" fill="url(#accentGradient)" opacity="0.1"/>

  <!-- Main Content Area (offset by page margins) -->
  <g transform="translate(80 60)">
    <!-- Header Section -->
    <g>
      <!-- Logo Placeholder -->
      <rect x="0" y="0" width="150" height="40" fill="#E0F2FE" rx="8"/>
      <text x="75" y="27" text-anchor="middle" class="small-text text-primary font-primary">
        {logo_url}
      </text>

      <!-- Page Main Title -->
      <text x="880" y="30" text-anchor="middle" class="section-title text-primary font-primary">
        {title}
      </text>
      <text x="880" y="80" text-anchor="middle" class="body-text text-secondary font-primary">
        {subtitle}
      </text>

      <!-- Page Number and Progress Indicator -->
      <text x="1760" y="30" text-anchor="end" class="body-text text-secondary font-primary">
        2 / 10
      </text>
      <rect x="1500" y="50" width="260" height="4" fill="#E0F2FE" rx="2"/>
      <rect x="1500" y="50" width="52" height="4" fill="#3B82F6" rx="2"/> <!-- 2/10 of 260 is 52 pixels -->
    </g>

    <!-- Table of Contents Section -->
    <g transform="translate(0 150)">
      <text x="0" y="0" class="main-title text-primary font-primary">
        目录
      </text>
      <text x="0" y="70" class="section-title text-secondary font-primary">
        Table of Contents
      </text>

      <!-- Horizontal Divider Line -->
      <line x1="0" y1="120" x2="1760" y2="120" stroke="#BAE6FD" stroke-width="1"/>

      <!-- Chapters/Sections (Bento Grid style with cards) -->

      <!-- Chapter 1: Company Overview -->
      <g transform="translate(0 180)">
        <rect x="0" y="0" width="860" height="160" rx="12" class="card-style"/>
        <use href="#icon-briefcase" x="30" y="30" width="32" height="32"/>
        <text x="90" y="55" class="content-title text-primary font-primary">
          01. 公司简介
        </text>
        <text x="90" y="95" class="small-text text-secondary font-primary">
          Company Overview
        </text>
        <text x="90" y="130" class="body-text text-secondary font-primary">
          了解我们的使命、愿景和核心价值观。
        </text>
        <text x="800" y="130" text-anchor="end" class="small-text text-light font-primary">
          Page 3
        </text>
      </g>

      <!-- Chapter 2: Corporate Culture (Active/Highlighted) -->
      <g transform="translate(900 180)">
        <rect x="0" y="0" width="860" height="160" rx="12" class="card-style card-active-border"/>
        <use href="#icon-culture" x="30" y="30" width="32" height="32"/>
        <text x="90" y="55" class="content-title accent-color font-primary">
          02. 企业文化
        </text>
        <text x="90" y="95" class="small-text text-secondary font-primary">
          Corporate Culture
        </text>
        <text x="90" y="130" class="body-text text-secondary font-primary">
          探索我们独特的文化理念和工作氛围。
        </text>
        <text x="800" y="130" text-anchor="end" class="small-text text-light font-primary">
          Page 4
        </text>
      </g>

      <!-- Chapter 3: Our Team -->
      <g transform="translate(0 370)">
        <rect x="0" y="0" width="860" height="160" rx="12" class="card-style"/>
        <use href="#icon-team" x="30" y="30" width="32" height="32"/>
        <text x="90" y="55" class="content-title text-primary font-primary">
          03. 团队展示
        </text>
        <text x="90" y="95" class="small-text text-secondary font-primary">
          Our Team
        </text>
        <text x="90" y="130" class="body-text text-secondary font-primary">
          认识我们的专业团队和核心成员。
        </text>
        <text x="800" y="130" text-anchor="end" class="small-text text-light font-primary">
          Page 5
        </text>
      </g>

      <!-- Chapter 4: Company Strengths -->
      <g transform="translate(900 370)">
        <rect x="0" y="0" width="860" height="160" rx="12" class="card-style"/>
        <use href="#icon-advantage" x="30" y="30" width="32" height="32"/>
        <text x="90" y="55" class="content-title text-primary font-primary">
          04. 企业优势
        </text>
        <text x="90" y="95" class="small-text text-secondary font-primary">
          Company Strengths
        </text>
        <text x="90" y="130" class="body-text text-secondary font-primary">
          了解我们独特的竞争优势。
        </text>
        <text x="800" y="130" text-anchor="end" class="small-text text-light font-primary">
          Page 6
        </text>
      </g>

      <!-- Chapter 5: Development History -->
      <g transform="translate(0 560)">
        <rect x="0" y="0" width="860" height="160" rx="12" class="card-style"/>
        <use href="#icon-history" x="30" y="30" width="32" height="32"/>
        <text x="90" y="55" class="content-title text-primary font-primary">
          05. 发展历程
        </text>
        <text x="90" y="95" class="small-text text-secondary font-primary">
          Development History
        </text>
        <text x="90" y="130" class="body-text text-secondary font-primary">
          回顾我们的成长足迹和重要里程碑。
        </text>
        <text x="800" y="130" text-anchor="end" class="small-text text-light font-primary">
          Page 7
        </text>
      </g>
    </g>
  </g>

  <!-- Footer Section -->
  <g transform="translate(80 980)">
    <text x="0" y="0" class="small-text text-secondary font-primary">
      {date}
    </text>
    <text x="1760" y="0" text-anchor="end" class="small-text text-secondary font-primary">
      {author}
    </text>
  </g>
</svg>