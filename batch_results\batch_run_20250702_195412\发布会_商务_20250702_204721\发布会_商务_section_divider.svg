<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette -->
    <style type="text/css">
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .background-color { fill: #F8FAFC; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .card-background { fill: #FFFFFF; }
      .card-border-stroke { stroke: #BAE6FD; }

      /* Font Styles */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* Text Sizes and Weights */
      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; } /* bold */
      .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; } /* bold */
      .content-title { font-size: 28px; font-weight: 700; line-height: 1.4; } /* bold */
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; } /* normal */
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; } /* normal */

      /* Icon Styles */
      .icon-stroke { stroke: #3B82F6; stroke-width: 2; } /* Using accent color for icons */

      /* Shadows (for card/elements, not directly used for background for this page type) */
      .shadow-light { filter: drop-shadow(0px 4px 6px rgba(0, 0, 0, 0.1)) drop-shadow(0px 2px 4px rgba(0, 0, 0, 0.06)); }
      .shadow-hover { filter: drop-shadow(0px 10px 15px rgba(0, 0, 0, 0.1)) drop-shadow(0px 4px 6px rgba(0, 0, 0, 0.05)); }
    </style>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF"/>
      <stop offset="100%" stop-color="#3B82F6"/>
    </linearGradient>

    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>

    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#F8FAFC"/>
      <stop offset="100%" stop-color="#E0F2FE"/>
    </linearGradient>

    <!-- Filter for subtle blur/glow effect (not applied to any element in this template) -->
    <filter id="glowEffect" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="20" result="blur"/>
      <feFlood flood-color="#3B82F6" flood-opacity="0.6" result="flood"/>
      <feComposite in="flood" in2="blur" operator="in" result="comp"/>
      <feMerge>
        <feMergeNode in="comp"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

  <!-- Decorative Elements for Transition and Business Style -->
  <!-- Large abstract shape at top left -->
  <path d="M0 0 H960 L600 300 L0 400 Z" fill="#1E40AF" fill-opacity="0.08"/>
  <path d="M0 0 H800 L450 250 L0 350 Z" fill="#3B82F6" fill-opacity="0.04"/>

  <!-- Abstract wave/flow element across the middle-bottom -->
  <path d="M0 540 C300 440, 600 640, 960 540 C1320 440, 1620 640, 1920 540 V1080 H0 Z" fill="url(#accentGradient)" fill-opacity="0.12"/>
  <path d="M0 580 C300 480, 600 680, 960 580 C1320 480, 1620 680, 1920 580 V1080 H0 Z" fill="#1E40AF" fill-opacity="0.08"/>

  <!-- Geometric patterns at bottom right -->
  <rect x="1600" y="800" width="320" height="280" fill="#475569" fill-opacity="0.08" rx="20"/>
  <rect x="1700" y="900" width="220" height="180" fill="#3B82F6" fill-opacity="0.05" rx="15"/>

  <!-- Main Content - Centered for Section Separator -->
  <g transform="translate(960, 540)"> <!-- Center point of the canvas -->

    <!-- Section Title -->
    <text x="0" y="-80" text-anchor="middle" class="font-primary hero-title text-primary">
      <tspan x="0" dy="0">{title}</tspan>
    </text>

    <!-- Subtitle / English Tagline -->
    <text x="0" y="30" text-anchor="middle" class="font-primary body-text text-secondary">
      <tspan x="0" dy="0">{subtitle}</tspan>
    </text>

  </g>

  <!-- Page Number / Branding at bottom right -->
  <text x="1840" y="1020" text-anchor="end" class="font-primary small-text text-light">
    <tspan x="1840" dy="0">Page 3/10</tspan>
  </text>

  <!-- Placeholder for Logo at top left -->
  <image x="80" y="60" width="150" height="60" href="{logo_url}" />

</svg>