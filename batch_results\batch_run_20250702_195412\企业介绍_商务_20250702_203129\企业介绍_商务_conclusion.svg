<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Styles for colors, fonts， and general elements -->
    <style type="text/css">
      /* Colors */
      .bg-color { fill: #F8FAFC; }
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .card-background { fill: #FFFFFF; }
      .card-border { stroke: #BAE6FD; }

      /* Fonts */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; } /* bold */
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; } /* bold */
      .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; } /* bold */
      .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; } /* semibold */
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; } /* normal */
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; } /* normal */
      .caption-text { font-size: 14px; font-weight: 400; line-height: 1.6; } /* normal */

      /* Text alignment */
      .text-align-left { text-anchor: start; }
      .text-align-center { text-anchor: middle; }
      .text-align-right { text-anchor: end; }

      /* Card shadow */
      .card-shadow { filter: url(#shadowFilter); }

      /* Icon style */
      .icon-style { stroke: #3B82F6; stroke-width: 2; fill: none; stroke-linecap: round; stroke-linejoin: round; }
    </style>

    <!-- Filter for card shadow -->
    <filter id="shadowFilter" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="4"/>
      <feGaussianBlur stdDeviation="6"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF"/>
      <stop offset="100%" stop-color="#475569"/>
    </linearGradient>
    <linearGradient id="accentTransparentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.8"/>
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0.2"/>
    </linearGradient>

    <!-- Icons - Define once to reuse -->
    <!-- Icon for Conclusion 1 (e.g., Globe for Global Presence) -->
    <symbol id="icon-globe" viewBox="0 0 24 24">
      <circle cx="12" cy="12" r="10" class="icon-style"/>
      <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" class="icon-style"/>
      <path d="M2 12h20" class="icon-style"/>
    </symbol>
    <!-- Icon for Conclusion 2 (e.g., Award for Achievement) -->
    <symbol id="icon-award" viewBox="0 0 24 24">
      <circle cx="12" cy="8" r="7" class="icon-style"/>
      <polyline points="8.21 13.89 7 22 12 18 17 22 15.79 13.88" class="icon-style"/>
    </symbol>
    <!-- Icon for Conclusion 3 (e.g., Trending Up for Growth) -->
    <symbol id="icon-trending-up" viewBox="0 0 24 24">
      <polyline points="23 6 13.5 15.5 8.5 10.5 1 18" class="icon-style"/>
      <polyline points="17 6 23 6 23 12" class="icon-style"/>
    </symbol>
    <!-- Icon for Contact (e.g., Mail) -->
    <symbol id="icon-mail" viewBox="0 0 24 24">
      <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" class="icon-style"/>
      <polyline points="22,6 12,13 2,6" class="icon-style"/>
    </symbol>
    <!-- Icon for Phone -->
    <symbol id="icon-phone" viewBox="0 0 24 24">
      <path d="M22 16.92v3.08c0 1.1-.9 2-2 2A15.92 15.92 0 0 1 3 3c-1.1 0-2 .9-2 2v3c0 .55.22 1.05.59 1.42L7 13l-4 4-3-3.08c-.37-.37-.59-.87-.59-1.42V5c0-1.1.9-2 2-2z" class="icon-style"/>
      <path d="M15.07 7.05a8 8 0 0 1 0 11.31" class="icon-style"/>
      <path d="M18.17 4.93a12 12 0 0 1 0 17.14" class="icon-style"/>
    </symbol>
    <!-- Icon for Website -->
    <symbol id="icon-link" viewBox="0 0 24 24">
      <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07L9.44 6" class="icon-style"/>
      <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" class="icon-style"/>
    </symbol>
  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color"/>

  <!-- Decorative elements -->
  <g opacity="0.1">
    <rect x="1500" y="0" width="420" height="300" fill="url(#accentTransparentGradient)"/>
    <rect x="0" y="800" width="500" height="280" fill="url(#accentTransparentGradient)"/>
    <circle cx="1700" cy="900" r="150" fill="url(#accentTransparentGradient)"/>
  </g>

  <!-- Header -->
  <g class="header">
    <image href="{logo_url}" x="80" y="60" width="160" height="80" preserveAspectRatio="xMidYMid meet"/>
    <text x="1840" y="100" class="font-primary small-text text-secondary text-align-right">10/10</text>
  </g>

  <!-- Main Title -->
  <text x="960" y="200" class="font-primary main-title text-primary text-align-center">{title}</text>
  <text x="960" y="260" class="font-primary body-text text-secondary text-align-center">{subtitle}</text>

  <!-- Content Area - Organized in a flexible grid-like manner -->
  <g class="content-area">
    <!-- Summary Points Section -->
    <g transform="translate(180, 350)">
      <text x="0" y="0" class="font-primary section-title text-primary text-align-left">主要结论</text>

      <!-- Conclusion 1 Card -->
      <rect x="0" y="60" width="500" height="160" rx="12" class="card-background card-border card-shadow"/>
      <use href="#icon-globe" x="30" y="85" width="32" height="32"/>
      <text x="80" y="105" class="font-primary content-title text-primary text-align-left">全球市场领导者</text>
      <text x="80" y="140" class="font-primary body-text text-secondary text-align-left">
        <tspan x="80" dy="0">我们在全球市场中占据领先地位，</tspan>
        <tspan x="80" dy="30">持续扩大业务范围和影响力。</tspan>
      </text>

      <!-- Conclusion 2 Card -->
      <rect x="0" y="240" width="500" height="160" rx="12" class="card-background card-border card-shadow"/>
      <use href="#icon-award" x="30" y="265" width="32" height="32"/>
      <text x="80" y="285" class="font-primary content-title text-primary text-align-left">创新驱动的卓越</text>
      <text x="80" y="320" class="font-primary body-text text-secondary text-align-left">
        <tspan x="80" dy="0">凭借持续的研发投入和技术创新，</tspan>
        <tspan x="80" dy="30">我们不断推出行业领先的解决方案。</tspan>
      </text>

      <!-- Conclusion 3 Card -->
      <rect x="0" y="420" width="500" height="160" rx="12" class="card-background card-border card-shadow"/>
      <use href="#icon-trending-up" x="30" y="445" width="32" height="32"/>
      <text x="80" y="465" class="font-primary content-title text-primary text-align-left">稳健增长与价值</text>
      <text x="80" y="500" class="font-primary body-text text-secondary text-align-left">
        <tspan x="80" dy="0">公司财务表现稳健，为股东和客户</tspan>
        <tspan x="80" dy="30">创造了持续增长的价值。</tspan>
      </text>
    </g>

    <!-- Call to Action / Key Highlight (Right Section) -->
    <g transform="translate(780, 350)">
      <rect x="0" y="0" width="960" height="300" rx="20" fill="url(#primaryGradient)" class="card-shadow"/>
      <text x="480" y="120" class="font-primary hero-title text-align-center" fill="#FFFFFF">未来五年</text>
      <text x="480" y="210" class="font-primary main-title text-align-center" fill="#FFFFFF">战略增长 300%</text>
      <text x="480" y="255" class="font-primary content-title text-align-center" fill="#BAE6FD">携手共创辉煌</text>
    </g>

    <!-- Contact Information Section -->
    <g transform="translate(780, 700)">
      <text x="0" y="0" class="font-primary section-title text-primary text-align-left">联系我们</text>

      <rect x="0" y="50" width="960" height="180" rx="12" class="card-background card-border card-shadow"/>

      <use href="#icon-mail" x="50" y="90" width="24" height="24"/>
      <text x="90" y="110" class="font-primary body-text text-primary text-align-left">
        <tspan x="90" dy="0">邮箱: <EMAIL></tspan>
      </text>

      <use href="#icon-phone" x="50" y="140" width="24" height="24"/>
      <text x="90" y="160" class="font-primary body-text text-primary text-align-left">
        <tspan x="90" dy="0">电话: +86 123 4567 8900</tspan>
      </text>

      <use href="#icon-link" x="50" y="190" width="24" height="24"/>
      <text x="90" y="210" class="font-primary body-text text-primary text-align-left">
        <tspan x="90" dy="0">网址: www.yourcompany.com</tspan>
      </text>
    </g>

    <!-- Thank You / Closing Remarks -->
    <g transform="translate(960, 1000)">
      <text x="0" y="0" class="font-primary body-text text-primary text-align-center">感谢您的时间！</text>
      <text x="0" y="40" class="font-primary small-text text-secondary text-align-center">期待与您携手共创未来</text>
    </g>

  </g>
</svg>