<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- CSS Styles -->
    <style>
      :root {
        --primary-color: #4A86E8;
        --secondary-color: #3B82F6;
        --accent-color: #0EA5E9;
        --background-color: #F8FAFC;
        --text-primary: #1E293B;
        --text-secondary: #64748B;
        --text-light: #94A3B8;
        --card-background: #FFFFFF;
        --container-background: #E0F2FE;
      }

      /* Font Styles */
      .font-primary {
        font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
      }
      .font-secondary {
        font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif;
      }
      .font-accent {
        font-family: 'Times New Roman', serif;
      }

      /* Text Sizes and Weights */
      .hero-title {
        font-size: 72px; /* hero_title */
        font-weight: 700; /* bold */
        fill: var(--text-primary);
      }
      .main-title {
        font-size: 56px; /* main_title */
        font-weight: 700; /* bold */
        fill: var(--text-primary);
      }
      .section-title {
        font-size: 36px; /* section_title */
        font-weight: 600; /* semibold */
        fill: var(--text-primary);
      }
      .content-title {
        font-size: 28px; /* content_title */
        font-weight: 500; /* medium */
        fill: var(--text-primary);
      }
      .body-text {
        font-size: 22px; /* body_text */
        font-weight: 400; /* normal */
        fill: var(--text-secondary);
        line-height: 1.4; /* normal */
      }
      .small-text {
        font-size: 16px; /* small_text */
        font-weight: 400; /* normal */
        fill: var(--text-secondary);
      }
      .caption-text {
        font-size: 14px; /* caption */
        font-weight: 400; /* normal */
        fill: var(--text-light);
      }

      /* Specific element styles */
      .bg-primary { fill: var(--primary-color); }
      .bg-secondary { fill: var(--secondary-color); }
      .bg-accent { fill: var(--accent-color); }
      .bg-background { fill: var(--background-color); }
      .bg-container { fill: var(--container-background); }

      .text-primary-color { fill: var(--text-primary); }
      .text-secondary-color { fill: var(--text-secondary); }
      .text-accent-color { fill: var(--accent-color); }

      .stroke-primary { stroke: var(--primary-color); }
      .stroke-accent { stroke: var(--accent-color); }
      .stroke-secondary { stroke: var(--text-secondary); }

      /* General element styling for illustration */
      .shape-fill-blue { fill: var(--primary-color); opacity: 0.8; }
      .shape-fill-light-blue { fill: var(--container-background); opacity: 0.9; }
      .shape-fill-accent { fill: var(--accent-color); opacity: 0.7; }
      .shape-stroke-blue { stroke: var(--primary-color); stroke-width: 2; fill: none; }
      .shape-stroke-accent { stroke: var(--accent-color); stroke-width: 2; fill: none; }

      /* Icon styling */
      .icon-stroke { stroke: var(--accent-color); stroke-width: 2; fill: none; }
      .icon-fill { fill: var(--accent-color); }

    </style>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4A86E8" />
      <stop offset="100%" stop-color="#3B82F6" />
    </linearGradient>

    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#0EA5E9" />
      <stop offset="100%" stop-color="#4A86E8" />
    </linearGradient>

    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#F8FAFC" />
      <stop offset="100%" stop-color="#E0F2FE" />
    </linearGradient>

  </defs>

  <!-- Background Layer -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)" />

  <!-- Decorative Abstract Shapes (Illustrative过渡设计) -->
  <!-- Large fluid shape at bottom left, creating a sense of flow -->
  <path d="M-50 700 C 150 850, 400 950, 600 1080 L -50 1080 Z" fill="url(#primaryGradient)" opacity="0.6" />
  <!-- Smaller fluid shape top right, balancing the composition -->
  <path d="M1970 -50 C 1700 150, 1500 300, 1300 400 L 1970 400 Z" fill="url(#accentGradient)" opacity="0.5" />

  <!-- Abstract geometric shapes for educational feel (Icons/Decorations) -->
  <!-- Circle decoration -->
  <circle cx="1700" cy="100" r="60" class="shape-fill-light-blue" opacity="0.7" />
  <!-- Triangle decoration -->
  <path d="M1850 80 L 1900 180 L 1800 180 Z" class="shape-stroke-accent" stroke-width="3" />
  <!-- Rotated Square decoration -->
  <rect x="1600" y="950" width="80" height="80" class="shape-fill-blue" opacity="0.6" transform="rotate(15 1600 950)" />
  <!-- Abstract line element for dynamic feel -->
  <path d="M100 200 C 250 150, 400 250, 550 200" class="shape-stroke-blue" stroke-width="4" opacity="0.7" />
  <path d="M1820 880 C 1670 930, 1520 830, 1370 880" class="shape-stroke-accent" stroke-width="4" opacity="0.7" />


  <!-- Main Content Area - Centered for Chapter Title -->
  <g transform="translate(0, 0)">
    <!-- Chapter Number (突出显示) -->
    <text x="960" y="420" text-anchor="middle" class="font-primary section-title text-accent-color">
      <tspan>CHAPTER 03</tspan>
    </text>

    <!-- Chapter Title (超大字体，强烈视觉冲击) -->
    <text x="960" y="520" text-anchor="middle" class="font-primary hero-title text-primary-color">
      <tspan>{title}</tspan>
    </text>
    <text x="960" y="590" text-anchor="middle" class="font-primary content-title text-secondary-color">
      <tspan>和#38;#8212; {subtitle} 和#38;#8212;</tspan>
    </text>

    <!-- Decorative under-title line with gradient (过渡性装饰元素) -->
    <rect x="760" y="650" width="400" height="8" rx="4" ry="4" fill="url(#accentGradient)" />

    <!-- Illustrative Icon (e.g., lightbulb for knowledge, education theme) -->
    <g class="icon-stroke" transform="translate(930, 700)">
      <circle cx="30" cy="30" r="25" />
      <path d="M30 5 L30 0" />
      <path d="M10 50 L50 50 C55 50 60 45 60 40 L60 30 C60 25 55 20 50 20 L45 20 L45 10 L15 10 L15 20 L10 20 C5 20 0 25 0 30 L0 40 C0 45 5 50 10 50 Z" />
      <circle cx="30" cy="40" r="3" class="icon-fill" />
    </g>

    <!-- Chapter Description/Content (多行文本，确保间距) -->
    <text x="960" y="800" text-anchor="middle" class="font-secondary body-text text-secondary-color">
      <tspan>{content}</tspan>
      <tspan x="960" dy="40">深入学习核心概念和实践技巧</tspan>
      <tspan x="960" dy="40">助力知识传递和技能培养</tspan>
    </text>
  </g>

  <!-- Page Number / Footer (右下角) -->
  <text x="1840" y="1030" text-anchor="end" class="font-primary small-text text-light">
    <tspan>页面 3/10</tspan>
  </text>

  <!-- Logo Placeholder (Top Left) -->
  <!-- Note: SVG image elements require a 'href' attribute for external image sources. -->
  <image x="80" y="60" width="120" height="auto" href="{logo_url}" />

</svg>