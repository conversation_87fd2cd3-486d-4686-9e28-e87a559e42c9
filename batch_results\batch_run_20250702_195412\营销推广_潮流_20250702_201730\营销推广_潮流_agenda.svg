<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Gradients based on provided palette -->
    <linearGradient id="gradientPrimary" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4A86E8"/>
      <stop offset="100%" stop-color="#3B82F6"/>
    </linearGradient>
    <linearGradient id="gradientAccent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#0EA5E9"/>
      <stop offset="100%" stop-color="#4A86E8"/>
    </linearGradient>
    <linearGradient id="gradientBackground" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#F8FAFC"/>
      <stop offset="100%" stop-color="#E0F2FE"/>
    </linearGradient>
    <linearGradient id="gradientText" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#1E3A8A"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- Filter for shadows, using feGaussianBlur for blur and feOffset for position -->
    <filter id="cardShadow">
      <feDropShadow dx="0" dy="4" stdDeviation="6" flood-color="rgba(0, 0, 0, 0.1)"/>
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="rgba(0, 0, 0, 0.06)"/>
    </filter>
    <filter id="subtleShadow">
      <feDropShadow dx="0" dy="2" stdDeviation="3" flood-color="rgba(0, 0, 0, 0.08)"/>
    </filter>

    <!-- Reusable icons (simple outline style) -->
    <symbol id="iconArrowRight" viewBox="0 0 24 24">
      <path d="M9 5l7 7-7 7" stroke="#4A86E8" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>
    <symbol id="iconCheck" viewBox="0 0 24 24">
      <path d="M20 6L9 17l-5-5" stroke="#4A86E8" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>
    <symbol id="iconStar" viewBox="0 0 24 24">
      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.27l-6.18 3.25L7 14.14l-5-4.87 6.91-1.01L12 2z" stroke="#4A86E8" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>
    <symbol id="iconProduct" viewBox="0 0 24 24">
      <path d="M12 2L2 7v10l10 5 10-5V7L12 2zM12 17.5L6 14.5V9.5L12 6.5l6 3v5l-6 3z" stroke="#4A86E8" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>
    <symbol id="iconMarketing" viewBox="0 0 24 24">
      <path d="M13 18.5H5V10h8v8.5zm6-14h-8V2h8v2.5zM13 22h-8v-2.5h8V22zm0-10h-8V7.5h8V12zm6-5.5h-8V2h8v4.5zM19 18.5h-8V10h8v8.5z" stroke="#4A86E8" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>
  </defs>

  <style>
    /* CSS Variables for colors */
    .primary-color { fill: #4A86E8; }
    .secondary-color { fill: #3B82F6; }
    .accent-color { fill: #0EA5E9; }
    .background-color { fill: #F8FAFC; }
    .text-primary { fill: #1E293B; }
    .text-secondary { fill: #64748B; }
    .text-light { fill: #94A3B8; }
    .card-background { fill: #FFFFFF; }
    .card-border-color { stroke: #BAE6FD; }
    .container-background { fill: #E0F2FE; }
    .hover-color { fill: #7DD3FC; }
    .active-color { fill: #4A86E8; }
    .disabled-color { fill: #64748B; }

    /* Font styles */
    .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
    .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
    .font-accent { font-family: 'Times New Roman', serif; }

    .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; letter-spacing: -0.025em; }
    .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; letter-spacing: -0.025em; }
    .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; }
    .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; }
    .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
    .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; }
    .caption { font-size: 14px; font-weight: 400; line-height: 1.6; }

    .font-bold { font-weight: 700; }
    .font-semibold { font-weight: 600; }

    /* Card styles */
    .card-style-inner {
      fill: #FFFFFF; /* Inner white fill for cards */
      stroke: #BAE6FD;
      stroke-width: 1px;
      rx: 12px;
      ry: 12px;
      filter: url(#cardShadow);
    }

    /* Interactive states for navigation items */
    .nav-item-rect {
      fill: #FFFFFF;
      rx: 12px;
      ry: 12px;
      stroke: #BAE6FD;
      stroke-width: 1px;
      transition: all 0.3s ease-in-out;
    }
    .nav-item-rect:hover {
      fill: #E0F2FE; /* container_background as hover */
      stroke: #7DD3FC; /* hover_color for border */
    }
    .nav-item-rect.active {
      fill: #DBEAFE; /* A lighter shade of primary for active */
      stroke: #4A86E8; /* primary_color for active border */
    }
    .nav-item-text {
      fill: #1E293B; /* text_primary */
      transition: fill 0.3s ease-in-out;
    }
    .nav-item-text.active {
      fill: #4A86E8; /* primary_color */
    }
    .nav-item-icon {
      fill: #4A86E8; /* primary_color */
      transition: fill 0.3s ease-in-out;
    }
    .nav-item-icon.active {
      fill: #0EA5E9; /* accent_color */
    }
    /* Note: SVG CSS does not support complex selectors like `:hover + .sibling`.
       For true interactive effects, this would typically be done with JavaScript.
       Here, we simulate the hover effect for the rect only. */

    /* Progress bar styles */
    .progress-bar-bg {
      fill: #E2E8F0; /* Lighter grey */
      rx: 8px;
      ry: 8px;
    }
    .progress-bar-fill {
      fill: url(#gradientAccent); /* Accent gradient */
      rx: 8px;
      ry: 8px;
    }
  </style>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#gradientBackground)"/>

  <!-- Decorative elements - subtle geometric shapes -->
  <circle cx="1700" cy="150" r="120" fill="#4A86E8" opacity="0.05"/>
  <rect x="100" y="900" width="200" height="80" rx="20" ry="20" fill="#3B82F6" opacity="0.05" transform="rotate(-15 100 900)"/>
  <path d="M1920 0L1700 0C1700 100 1800 200 1920 200V0Z" fill="#0EA5E9" opacity="0.08"/>
  <path d="M0 1080L220 1080C220 980 120 880 0 880V1080Z" fill="#0EA5E9" opacity="0.08"/>


  <!-- Header Section -->
  <g id="header">
    <!-- Logo Placeholder (top-left) -->
    <rect x="80" y="60" width="160" height="60" fill="#BAE6FD" rx="8" ry="8"/>
    <text x="160" y="98" text-anchor="middle" class="font-primary font-semibold small-text text-primary">
      {logo_url}
      <tspan x="160" dy="0">品牌标识</tspan>
    </text>

    <!-- Page Title -->
    <text x="960" y="90" text-anchor="middle" class="font-primary main-title text-primary">
      <tspan x="960" dy="0">{title}</tspan>
    </text>
    <text x="960" y="140" text-anchor="middle" class="font-primary body-text text-secondary">
      <tspan x="960" dy="0">{subtitle}</tspan>
    </text>
  </g>

  <!-- Main Content Area - Bento Grid Style Layout -->
  <g id="main-content">
    <!-- Left Panel: Chapter Navigation -->
    <rect x="80" y="190" width="500" height="780" class="card-style-inner"/>
    <text x="110" y="240" class="font-primary section-title text-primary">
      <tspan x="110" dy="0">目录导航</tspan>
    </text>
    <text x="110" y="280" class="font-primary body-text text-secondary">
      <tspan x="110" dy="0">快速浏览核心章节</tspan>
    </text>

    <!-- Navigation Items -->
    <!-- Item 1 (Active) -->
    <g class="nav-item">
      <rect x="110" y="330" width="440" height="80" class="nav-item-rect active"/>
      <text x="140" y="375" class="font-primary content-title nav-item-text active">
        <tspan>01. 产品概述</tspan>
      </text>
      <text x="140" y="405" class="font-secondary small-text text-secondary">
        <tspan>Product Overview</tspan>
      </text>
      <use xlink:href="#iconArrowRight" x="500" y="355" width="32" height="32" class="nav-item-icon active"/>
    </g>

    <!-- Item 2 -->
    <g class="nav-item">
      <rect x="110" y="430" width="440" height="80" class="nav-item-rect"/>
      <text x="140" y="475" class="font-primary content-title nav-item-text">
        <tspan>02. 核心特色</tspan>
      </text>
      <text x="140" y="505" class="font-secondary small-text text-secondary">
        <tspan>Key Features</tspan>
      </text>
      <use xlink:href="#iconArrowRight" x="500" y="455" width="32" height="32" class="nav-item-icon"/>
    </g>

    <!-- Item 3 -->
    <g class="nav-item">
      <rect x="110" y="530" width="440" height="80" class="nav-item-rect"/>
      <text x="140" y="575" class="font-primary content-title nav-item-text">
        <tspan>03. 市场优势</tspan>
      </text>
      <text x="140" y="605" class="font-secondary small-text text-secondary">
        <tspan>Market Advantages</tspan>
      </text>
      <use xlink:href="#iconArrowRight" x="500" y="555" width="32" height="32" class="nav-item-icon"/>
    </g>

    <!-- Item 4 -->
    <g class="nav-item">
      <rect x="110" y="630" width="440" height="80" class="nav-item-rect"/>
      <text x="140" y="675" class="font-primary content-title nav-item-text">
        <tspan>04. 用户价值</tspan>
      </text>
      <text x="140" y="705" class="font-secondary small-text text-secondary">
        <tspan>User Value Proposition</tspan>
      </text>
      <use xlink:href="#iconArrowRight" x="500" y="655" width="32" height="32" class="nav-item-icon"/>
    </g>

    <!-- Item 5 -->
    <g class="nav-item">
      <rect x="110" y="730" width="440" height="80" class="nav-item-rect"/>
      <text x="140" y="775" class="font-primary content-title nav-item-text">
        <tspan>05. 成功案例</tspan>
      </text>
      <text x="140" y="805" class="font-secondary small-text text-secondary">
        <tspan>Success Stories</tspan>
      </text>
      <use xlink:href="#iconArrowRight" x="500" y="755" width="32" height="32" class="nav-item-icon"/>
    </g>

    <!-- Item 6 -->
    <g class="nav-item">
      <rect x="110" y="830" width="440" height="80" class="nav-item-rect"/>
      <text x="140" y="875" class="font-primary content-title nav-item-text">
        <tspan>06. 联系我们</tspan>
      </text>
      <text x="140" y="905" class="font-secondary small-text text-secondary">
        <tspan>Contact Us</tspan>
      </text>
      <use xlink:href="#iconArrowRight" x="500" y="855" width="32" height="32" class="nav-item-icon"/>
    </g>


    <!-- Right Panel: Content Overview & Value Proposition -->
    <rect x="610" y="190" width="1230" height="780" class="card-style-inner"/>

    <!-- Large number emphasis -->
    <text x="660" y="320" class="font-primary hero-title" fill="url(#gradientText)">
      <tspan>200%</tspan>
    </text>
    <text x="660" y="380" class="font-primary section-title text-primary">
      <tspan>效率提升</tspan>
    </text>
    <text x="660" y="420" class="font-secondary body-text text-secondary">
      <tspan>Efficiency Boost</tspan>
    </text>

    <!-- Product Features/Value Props -->
    <g id="features-list">
      <text x="660" y="500" class="font-primary content-title text-primary">
        <tspan>产品概述：卓越性能和创新设计</tspan>
      </text>
      <text x="660" y="540" class="font-secondary body-text text-secondary">
        <tspan>我们的产品旨在重新定义行业标准，</tspan>
        <tspan x="660" dy="35">提供无与伦比的性能和用户体验。</tspan>
      </text>

      <!-- Feature 1 -->
      <g>
        <use xlink:href="#iconCheck" x="660" y="600" width="24" height="24" class="primary-color"/>
        <text x="700" y="620" class="font-primary body-text text-primary">
          <tspan>智能自动化流程</tspan>
        </text>
        <text x="700" y="645" class="font-secondary small-text text-secondary">
          <tspan>Smart Automation Process</tspan>
        </text>
      </g>

      <!-- Feature 2 -->
      <g>
        <use xlink:href="#iconCheck" x="660" y="680" width="24" height="24" class="primary-color"/>
        <text x="700" y="700" class="font-primary body-text text-primary">
          <tspan>数据安全和隐私保护</tspan>
        </text>
        <text x="700" y="725" class="font-secondary small-text text-secondary">
          <tspan>Data Security and Privacy Protection</tspan>
        </text>
      </g>

      <!-- Feature 3 -->
      <g>
        <use xlink:href="#iconCheck" x="660" y="760" width="24" height="24" class="primary-color"/>
        <text x="700" y="780" class="font-primary body-text text-primary">
          <tspan>直观的用户界面</tspan>
        </text>
        <text x="700" y="805" class="font-secondary small-text text-secondary">
          <tspan>Intuitive User Interface</tspan>
        </text>
      </g>
    </g>

    <!-- Placeholder for data visualization / chart -->
    <g id="data-chart">
      <rect x="1050" y="470" width="700" height="400" fill="#E0F2FE" rx="12" ry="12" stroke="#BAE6FD" stroke-width="1"/>
      <text x="1400" y="495" text-anchor="middle" class="font-primary content-title text-primary">
        <tspan>市场增长趋势</tspan>
      </text>
      <text x="1400" y="525" text-anchor="middle" class="font-secondary small-text text-secondary">
        <tspan>Market Growth Trend</tspan>
      </text>
      <!-- Simple bar chart elements -->
      <rect x="1100" y="770" width="80" height="90" fill="#4A86E8" rx="4" ry="4"/>
      <rect x="1250" y="700" width="80" height="160" fill="#3B82F6" rx="4" ry="4"/>
      <rect x="1400" y="600" width="80" height="260" fill="#0EA5E9" rx="4" ry="4"/>
      <rect x="1550" y="550" width="80" height="310" fill="#4A86E8" rx="4" ry="4"/>
      <text x="1140" y="880" text-anchor="middle" class="font-secondary caption text-secondary">
        <tspan>Q1</tspan>
      </text>
      <text x="1290" y="880" text-anchor="middle" class="font-secondary caption text-secondary">
        <tspan>Q2</tspan>
      </text>
      <text x="1440" y="880" text-anchor="middle" class="font-secondary caption text-secondary">
        <tspan>Q3</tspan>
      </text>
      <text x="1590" y="880" text-anchor="middle" class="font-secondary caption text-secondary">
        <tspan>Q4</tspan>
      </text>
    </g>
  </g>

  <!-- Progress Indicator & Page Number -->
  <g id="progress-indicator">
    <text x="80" y="1020" class="font-primary body-text text-primary">
      <tspan>页面进度</tspan>
    </text>
    <rect x="220" y="1005" width="1620" height="16" class="progress-bar-bg"/>
    <!-- Current progress: 2/10 = 20% of 1620px width -->
    <rect x="220" y="1005" width="324" height="16" class="progress-bar-fill"/>
    <text x="1760" y="1020" text-anchor="end" class="font-primary body-text text-primary">
      <tspan>2/10 页</tspan>
    </text>
  </g>

</svg>