<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <style type="text/css"><![CDATA[
      /* General styles */
      .bg-dark {
        fill: #000000; /* Pure black background as per enhancement request */
      }

      /* Text Styles */
      /* Font family priority: Chinese first, then English fallback */
      .hero-title {
        font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
        font-size: 72px; /* hero_title */
        font-weight: 700; /* bold */
        fill: #F8FAFC; /* Light text on dark background for contrast */
        line-height: 1.1; /* tight */
      }
      .main-title {
        font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
        font-size: 56px; /* main_title */
        font-weight: 700; /* bold */
        fill: #F8FAFC; /* Light text on dark background */
        line-height: 1.1; /* tight */
      }
      .section-title {
        font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
        font-size: 36px; /* section_title */
        font-weight: 600; /* semibold */
        fill: #F8FAFC; /* Light text on dark background */
        line-height: 1.4; /* normal */
      }
      .content-title {
        font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
        font-size: 28px; /* content_title */
        font-weight: 600; /* semibold */
        fill: #F8FAFC; /* Light text on dark background */
        line-height: 1.4; /* normal */
      }
      .body-text {
        font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif;
        font-size: 22px; /* body_text */
        font-weight: 400; /* normal */
        fill: #94A3B8; /* text_light for body on dark background for readability */
        line-height: 1.6; /* relaxed */
      }
      .list-item-text {
        font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif;
        font-size: 22px; /* body_text */
        font-weight: 400; /* normal */
        fill: #F8FAFC; /* Light text on dark background for list items */
        line-height: 1.6; /* relaxed */
      }
      .small-text {
        font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif;
        font-size: 16px; /* small_text */
        font-weight: 400; /* normal */
        fill: #94A3B8; /* text_light for small text on dark */
      }
      .caption-text {
        font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif;
        font-size: 14px; /* caption */
        font-weight: 400; /* normal */
        fill: #94A3B8; /* text_light for caption on dark */
      }

      /* Accent Colors (Tesla red as primary highlight, blue as secondary tech accent) */
      .accent-red {
        fill: #E31937; /* Tesla Red - primary highlight */
      }
      .accent-blue {
        fill: #3B82F6; /* Accent Blue - secondary tech highlight */
      }
      .primary-blue {
        fill: #1E40AF; /* Primary Blue from original palette - subtle use */
      }
      .secondary-blue {
        fill: #475569; /* Secondary Blue from original palette - subtle use */
      }

      /* Borders and Strokes (for outline graphics and card borders) */
      .stroke-red {
        stroke: #E31937;
        stroke-width: 2px;
        fill: none;
      }
      .stroke-blue {
        stroke: #3B82F6;
        stroke-width: 2px;
        fill: none;
      }
      .stroke-secondary-blue {
        stroke: #475569;
        stroke-width: 1px;
        fill: none;
      }

      /* Gradients for decorative elements (single color transparency) */
      <linearGradient id="gradientRedTransparent" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stop-color="#E31937" stop-opacity="0.08"/>
        <stop offset="100%" stop-color="#E31937" stop-opacity="0"/>
      </linearGradient>
      <linearGradient id="gradientBlueTransparent" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.08"/>
        <stop offset="100%" stop-color="#3B82F6" stop-opacity="0"/>
      </linearGradient>

      /* Bento Grid style elements */
      .bento-card {
        fill: #0A0A0A; /* Slightly lighter black for card backgrounds */
        stroke: #1E40AF; /* Primary blue for subtle card border, maintains palette link */
        stroke-width: 1px;
        rx: 12px; /* border_radius */
        ry: 12px;
      }
      .bento-card-highlight {
        fill: #1A0507; /* Dark red for highlight cards */
        stroke: #E31937; /* Tesla Red border for emphasis */
        stroke-width: 2px;
        rx: 12px;
        ry: 12px;
      }

      /* Icon style (outline style as requested) */
      .icon-style {
        stroke: #3B82F6; /* Accent blue for icons */
        stroke-width: 2px;
        fill: none;
      }

      /* Placeholder styles */
      .placeholder-text {
        fill: #94A3B8;
        font-family: 'Source Han Sans CN', sans-serif;
        font-size: 18px;
      }
      .placeholder-image {
        fill: #1A1A1A; /* Dark gray for image placeholder */
        stroke: #475569;
        stroke-width: 1px;
      }

    ]]></style>

    <!-- Gradients for transparency effects (redefined here for clarity, though already in style block) -->
    <linearGradient id="gradientRedTransparent" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#E31937" stop-opacity="0.08"/>
      <stop offset="100%" stop-color="#E31937" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="gradientBlueTransparent" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.08"/>
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0"/>
    </linearGradient>

    <!-- Reusable icon examples (simplified for SVG, outline style) -->
    <symbol id="icon-chart" viewBox="0 0 24 24">
      <path d="M4 20H20V22H4V20ZM13 2C12.4477 2 12 2.44772 12 3V17C12 17.5523 12.4477 18 13 18H15C15.5523 18 16 17.5523 16 17V3C16 2.44772 15.5523 2 15 2H13ZM7 8C6.44772 8 6 8.44772 6 9V17C6 17.5523 6.44772 18 7 18H9C9.55228 18 10 17.5523 10 17V9C10 8.44772 9.55228 8 9 8H7ZM19 6C18.4477 6 18 6.44772 18 7V17C18 17.5523 18.4477 18 19 18H21C21.5523 18 22 17.5523 22 17V7C22 6.44772 21.5523 6 21 6H19Z" class="icon-style"/>
    </symbol>
    <symbol id="icon-star" viewBox="0 0 24 24">
      <path d="M12 17.27L18.18 21L16.54 13.97L22 9.24L14.81 8.62L12 2L9.19 8.62L2 9.24L7.46 13.97L5.82 21L12 17.27Z" class="icon-style"/>
    </symbol>
    <symbol id="icon-bulb" viewBox="0 0 24 24">
      <path d="M9 22H15C15 22.5523 14.5523 23 14 23H10C9.44772 23 9 22.5523 9 22ZM12 2C8.13401 2 5 5.13401 5 9C5 11.3857 6.19597 13.5828 7.96253 15.0001L8 15.0001V16H16V15.0001C17.804 13.5828 19 11.3857 19 9C19 5.13401 15.866 2 12 2ZM12 4C14.7614 4 17 6.23858 17 9C17 10.9822 15.9329 12.8021 14.2831 14.0001L14.2831 14.0001H9.71689C8.06713 12.8021 7 10.9822 7 9C7 6.23858 9.23858 4 12 4Z" class="icon-style"/>
    </symbol>

  </defs>

  <!-- Background Layer -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-dark" />

  <!-- Decorative elements (subtle gradients for tech feel) -->
  <rect x="0" y="0" width="1920" height="100" class="gradient-red-transparent" />
  <rect x="0" y="980" width="1920" height="100" class="gradient-blue-transparent" style="transform: rotate(180deg); transform-origin: center;" />

  <!-- Border lines for a structured look -->
  <line x1="80" y1="100" x2="1840" y2="100" class="stroke-secondary-blue" style="opacity: 0.2;"/>
  <line x1="80" y1="980" x2="1840" y2="980" class="stroke-secondary-blue" style="opacity: 0.2;"/>

  <!-- Main Content Group, translated to apply page margins -->
  <g transform="translate(80, 60)">
    <!-- Logo Placeholder (top left) -->
    <rect x="0" y="0" width="200" height="60" fill="#1A1A1A" rx="8" ry="8"/>
    <text x="100" y="38" text-anchor="middle" class="small-text" fill="#94A3B8">
      <tspan x="100" y="38">
        <tspan class="accent-red">Logo</tspan> {logo_url}
      </tspan>
    </text>

    <!-- Page Number (top right) -->
    <text x="1760" y="38" text-anchor="end" class="small-text">
      <tspan class="accent-blue">4</tspan>/10
    </text>

    <!-- Main Title Section (centered) -->
    <text x="880" y="180" text-anchor="middle" class="main-title">
      <tspan x="880" y="180">{title}</tspan>
      <tspan x="880" y="240" class="section-title accent-red">{subtitle}</tspan>
    </text>

    <!-- Content Cards based on Bento Grid style -->
    <!-- This group starts below the main title, ensuring minimum 60px spacing -->
    <g transform="translate(0, 300)">
      <!-- Card 1: Main Content Paragraph -->
      <rect x="0" y="0" width="850" height="400" class="bento-card" />
      <text x="40" y="50" class="content-title">
        <tspan x="40" y="50">核心理念和产品优势</tspan>
      </text>
      <text x="40" y="100" class="body-text">
        <tspan x="40" y="100">{content}</tspan>
        <tspan x="40" y="135">我们致力于提供创新解决方案，赋能企业实现数字化转型，</tspan>
        <tspan x="40" y="170">提升运营效率，并创造卓越的用户体验。本产品凝聚了</tspan>
        <tspan x="40" y="205">我们多年的研发和创新成果，旨在解决行业痛点，</tspan>
        <tspan x="40" y="240">为客户带来前所未有的价值。其卓越的性能和</tspan>
        <tspan x="40" y="275">可靠性将成为您业务增长的强大引擎。</tspan>
      </text>
      <!-- Icon as decorative element within the card -->
      <use xlink:href="#icon-bulb" x="780" y="340" width="32" height="32" />

      <!-- Card 2: Key Feature Highlight (Large Number Emphasis) -->
      <rect x="910" y="0" width="850" height="200" class="bento-card-highlight" />
      <text x="950" y="130" class="hero-title accent-red">
        <tspan>99.9%</tspan>
      </text>
      <text x="1300" y="90" class="content-title">
        <tspan x="1300" y="90">系统稳定性</tspan>
        <tspan x="1300" y="130" class="body-text">全年无故障运行时间</tspan>
      </text>
      <!-- Icon as decorative element within the card -->
      <use xlink:href="#icon-chart" x="1690" y="140" width="32" height="32" />

      <!-- Card 3: Key Features List -->
      <!-- Adjusted y-position to ensure 50px spacing from Card 2 -->
      <rect x="910" y="250" width="850" height="300" class="bento-card" />
      <text x="950" y="300" class="content-title">
        <tspan x="950" y="300">产品主要亮点</tspan>
      </text>
      <text x="970" y="360" class="list-item-text">
        <tspan x="970" y="360">• 极速响应性能</tspan>
        <tspan x="970" y="395">• 智能数据分析</tspan>
        <tspan x="970" y="430">• 安全加密技术</tspan>
        <tspan x="970" y="465">• 灵活扩展架构</tspan>
        <tspan x="970" y="500">• 友好的用户界面</tspan>
      </text>
      <!-- Icon as decorative element within the card -->
      <use xlink:href="#icon-star" x="1690" y="490" width="32" height="32" />

      <!-- Decorative Grid Lines for Bento effect (subtle) -->
      <line x1="880" y1="0" x2="880" y2="550" class="stroke-secondary-blue" style="opacity: 0.1;"/>
      <line x1="0" y1="225" x2="1760" y2="225" class="stroke-secondary-blue" style="opacity: 0.1;"/>
    </g>

    <!-- Footer Information -->
    <text x="0" y="960" class="small-text">
      <tspan>发布日期: {date}</tspan>
    </text>
    <text x="1760" y="960" text-anchor="end" class="small-text">
      <tspan>发布机构: {author}</tspan>
    </text>

  </g>
</svg>