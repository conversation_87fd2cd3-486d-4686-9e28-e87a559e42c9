<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <style>
    /* Global Styles */
    .dark-background {
      fill: #101010; /* 纯黑色底，略微偏离纯黑以增加深度和科技感 */
    }
    .card-background {
      fill: #1A202C; /* 深蓝灰色作为卡片背景，与整体蓝色系保持一致，同时在深色背景下提供对比 */
      stroke: #3B82F6; /* 强调色作为边框，突出专业感 */
      stroke-width: 1px;
      rx: 12px; /* 圆角 */
      ry: 12px;
      filter: url(#cardShadow); /* 应用阴影效果 */
    }
    .text-hero-title {
      font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
      font-size: 72px;
      font-weight: 700; /* 加粗 */
      fill: #F8FAFC; /* 亮色文字，在深色背景下清晰可见 */
    }
    .text-main-title {
      font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
      font-size: 56px;
      font-weight: 700;
      fill: #F8FAFC;
    }
    .text-section-title {
      font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
      font-size: 36px;
      font-weight: 600; /* 半粗体 */
      fill: #3B82F6; /* 强调色用于标题，增加视觉焦点 */
    }
    .text-content-title {
      font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
      font-size: 28px;
      font-weight: 500; /* 中等粗细 */
      fill: #F8FAFC;
    }
    .text-body {
      font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif;
      font-size: 22px;
      font-weight: 400; /* 常规 */
      fill: #94A3B8; /* 辅助文字颜色 */
    }
    .text-small {
      font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif;
      font-size: 16px;
      font-weight: 400;
      fill: #64748B; /* 较浅的灰色，用于辅助信息 */
    }
    .text-caption {
      font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif;
      font-size: 14px;
      font-weight: 300; /* 细体 */
      fill: #64748B;
    }
    .accent-color-fill {
      fill: #3B82F6;
    }
    .accent-color-stroke {
      stroke: #3B82F6;
    }
    .primary-color-fill {
      fill: #1E40AF;
    }
    .primary-color-stroke {
      stroke: #1E40AF;
    }
    .secondary-color-fill {
      fill: #475569;
    }
    .secondary-color-stroke {
      stroke: #475569;
    }
    .highlight-color-fill {
      fill: #E31937; /* 特斯拉红色作为高亮色，用于强调关键数据或警告 */
    }
    .highlight-color-stroke {
      stroke: #E31937;
    }
    .gradient-accent-fill {
      fill: url(#gradientAccent);
    }
    .gradient-primary-fill {
      fill: url(#gradientPrimary);
    }
    .icon-style {
      stroke: #3B82F6; /* 图标使用强调色 */
      stroke-width: 2;
      fill: none;
    }
    .divider-line {
      stroke: #475569;
      stroke-width: 1px;
    }
    .data-point-label {
        font-family: 'Source Han Sans CN', sans-serif;
        font-size: 20px;
        font-weight: 500;
        fill: #F8FAFC; /* 数据点标签颜色 */
    }
    .data-value-large {
        font-family: 'Microsoft YaHei', sans-serif;
        font-size: 60px;
        font-weight: 700;
        fill: #E31937; /* 超大数字使用高亮色，突出核心要点 */
    }
    .data-value-medium {
        font-family: 'Microsoft YaHei', sans-serif;
        font-size: 48px;
        font-weight: 700;
        fill: #3B82F6; /* 较大数字使用强调色 */
    }
    .data-card-value {
        font-family: 'Microsoft YaHei', sans-serif;
        font-size: 40px;
        font-weight: 700;
        fill: #F8FAFC;
    }
    .data-card-label {
        font-family: 'Source Han Sans CN', sans-serif;
        font-size: 20px;
        font-weight: 400;
        fill: #94A3B8;
    }
    .graph-line {
        stroke: #3B82F6; /* 图表线条颜色 */
        stroke-width: 3;
        fill: none;
    }
    .graph-area {
        fill: url(#chartGradient);
        opacity: 0.6;
    }
    .graph-bar {
        fill: #1E40AF;
        rx: 4px;
        ry: 4px;
    }
    .graph-bar-highlight {
        fill: #3B82F6;
    }
    .grid-line {
        stroke: #475569;
        stroke-width: 0.5px;
        opacity: 0.4;
    }
    .chart-axis-label {
        font-family: 'Source Han Sans CN', sans-serif;
        font-size: 18px;
        fill: #94A3B8;
    }
    .chart-title {
        font-family: 'Microsoft YaHei', sans-serif;
        font-size: 32px;
        font-weight: 600;
        fill: #F8FAFC;
    }
    .chart-subtitle {
        font-family: 'Source Han Sans CN', sans-serif;
        font-size: 20px;
        fill: #94A3B8;
    }

  </style>

  <defs>
    <!-- 卡片阴影效果 -->
    <filter id="cardShadow" x="-5%" y="-5%" width="110%" height="110%">
      <feOffset dx="0" dy="4"/>
      <feGaussianBlur stdDeviation="6"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 渐变定义 -->
    <linearGradient id="gradientAccent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>
    <linearGradient id="gradientPrimary" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF"/>
      <stop offset="100%" stop-color="#475569"/>
    </linearGradient>
    <linearGradient id="chartGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.8"/>
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0"/>
    </linearGradient>

    <!-- 医疗图标示例 (心跳/心电图线) -->
    <symbol id="icon-heartbeat" viewBox="0 0 24 24">
      <path d="M4 12H6L9.5 6L14.5 18L18 12H20" class="icon-style"/>
    </symbol>
    <!-- 医疗图标示例 (剪贴板/报告) -->
    <symbol id="icon-report" viewBox="0 0 24 24">
      <path d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z" class="icon-style"/>
      <polyline points="14 2 14 8 20 8" class="icon-style"/>
      <line x1="8" y1="12" x2="16" y2="12" class="icon-style"/>
      <line x1="8" y1="16" x2="12" y2="16" class="icon-style"/>
    </symbol>
  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="1920" height="1080" class="dark-background"/>

  <!-- 页面顶部区域 -->
  <g id="header-section">
    <!-- Logo 占位符 -->
    <image href="{logo_url}" x="80" y="60" width="120" height="40" preserveAspectRatio="xMidYMid meet"/>
    <!-- 标题 -->
    <text x="960" y="100" text-anchor="middle" class="text-main-title">
      <tspan x="960" y="100">{title}</tspan>
    </text>
    <!-- 副标题 -->
    <text x="960" y="140" text-anchor="middle" class="text-body">
      <tspan x="960" y="140">{subtitle}</tspan>
    </text>
  </g>

  <!-- 主内容区域 - Bento Grid 布局 -->
  <!-- 主要图表卡片 (左侧 - 较大区域) -->
  <rect x="80" y="200" width="1100" height="780" class="card-background"/>
  <g id="main-chart-card">
    <text x="120" y="250" class="chart-title">
      <tspan>临床数据分析和趋势</tspan>
    </text>
    <text x="120" y="280" class="chart-subtitle">
      <tspan>Clinical Data Analysis 和 Trends</tspan> <!-- 遵循规则：文本内容中禁止使用 和 -->
    </text>

    <!-- 模拟折线图 -->
    <g transform="translate(120, 350)">
      <!-- 图表绘制区域: 1000x550 -->
      <rect x="0" y="0" width="1000" height="550" fill="rgba(0,0,0,0.1)" rx="8" ry="8"/>

      <!-- 网格线 -->
      <line x1="0" y1="550" x2="1000" y2="550" class="grid-line"/> <!-- X轴 -->
      <line x1="0" y1="0" x2="0" y2="550" class="grid-line"/> <!-- Y轴 -->

      <!-- 水平网格线 -->
      <line x1="0" y1="440" x2="1000" y2="440" class="grid-line"/>
      <line x1="0" y1="330" x2="1000" y2="330" class="grid-line"/>
      <line x1="0" y1="220" x2="1000" y2="220" class="grid-line"/>
      <line x1="0" y1="110" x2="1000" y2="110" class="grid-line"/>

      <!-- 垂直网格线 (月份) -->
      <line x1="166" y1="0" x2="166" y2="550" class="grid-line"/>
      <line x1="332" y1="0" x2="332" y2="550" class="grid-line"/>
      <line x1="498" y1="0" x2="498" y2="550" class="grid-line"/>
      <line x1="664" y1="0" x2="664" y2="550" class="grid-line"/>
      <line x1="830" y1="0" x2="830" y2="550" class="grid-line"/>

      <!-- X轴标签 -->
      <text x="0" y="580" text-anchor="middle" class="chart-axis-label">Jan</text>
      <text x="166" y="580" text-anchor="middle" class="chart-axis-label">Feb</text>
      <text x="332" y="580" text-anchor="middle" class="chart-axis-label">Mar</text>
      <text x="498" y="580" text-anchor="middle" class="chart-axis-label">Apr</text>
      <text x="664" y="580" text-anchor="middle" class="chart-axis-label">May</text>
      <text x="830" y="580" text-anchor="middle" class="chart-axis-label">Jun</text>
      <text x="990" y="580" text-anchor="end" class="chart-axis-label">Jul</text>

      <!-- Y轴标签 -->
      <text x="-20" y="550" text-anchor="end" dominant-baseline="middle" class="chart-axis-label">0</text>
      <text x="-20" y="440" text-anchor="end" dominant-baseline="middle" class="chart-axis-label">200</text>
      <text x="-20" y="330" text-anchor="end" dominant-baseline="middle" class="chart-axis-label">400</text>
      <text x="-20" y="220" text-anchor="end" dominant-baseline="middle" class="chart-axis-label">600</text>
      <text x="-20" y="110" text-anchor="end" dominant-baseline="middle" class="chart-axis-label">800</text>
      <text x="-20" y="0" text-anchor="end" dominant-baseline="middle" class="chart-axis-label">1000</text>

      <!-- 折线图数据点和线条 -->
      <!-- 线条路径 -->
      <path d="M0 450 L166 300 L332 350 L498 200 L664 250 L830 150 L1000 100" class="graph-line"/>
      <!-- 线条下方区域填充 -->
      <path d="M0 450 L166 300 L332 350 L498 200 L664 250 L830 150 L1000 100 L1000 550 L0 550 Z" class="graph-area"/>

      <!-- 数据点和标签 (避免重叠) -->
      <circle cx="0" cy="450" r="5" class="accent-color-fill"/>
      <text x="0" y="420" text-anchor="middle" class="data-point-label">450</text>

      <circle cx="166" cy="300" r="5" class="accent-color-fill"/>
      <text x="166" y="270" text-anchor="middle" class="data-point-label">700</text>

      <circle cx="332" cy="350" r="5" class="accent-color-fill"/>
      <text x="332" y="320" text-anchor="middle" class="data-point-label">650</text>

      <circle cx="498" cy="200" r="5" class="accent-color-fill"/>
      <text x="498" y="170" text-anchor="middle" class="data-point-label">800</text>

      <circle cx="664" cy="250" r="5" class="accent-color-fill"/>
      <text x="664" y="220" text-anchor="middle" class="data-point-label">750</text>

      <circle cx="830" cy="150" r="5" class="accent-color-fill"/>
      <text x="830" y="120" text-anchor="middle" class="data-point-label">850</text>

      <circle cx="1000" cy="100" r="5" class="accent-color-fill"/>
      <text x="1000" y="70" text-anchor="end" class="data-point-label">900</text>

    </g>
  </g>

  <!-- 数据卡片区域 (右侧 - 堆叠布局) -->
  <g id="data-cards-section">
    <!-- 卡片 1: 关键指标 -->
    <rect x="1200" y="200" width="640" height="250" class="card-background"/>
    <g transform="translate(1200, 200)">
      <text x="40" y="60" class="text-content-title">
        <tspan>总患者数</tspan>
      </text>
      <text x="40" y="120" class="data-value-large">
        <tspan>12,345</tspan>
      </text>
      <text x="40" y="160" class="text-small">
        <tspan>Total Patients Recorded</tspan>
      </text>
      <use href="#icon-report" x="540" y="40" width="64" height="64" class="accent-color-stroke"/>
    </g>

    <!-- 卡片 2: 增长率 -->
    <rect x="1200" y="470" width="640" height="250" class="card-background"/>
    <g transform="translate(1200, 470)">
      <text x="40" y="60" class="text-content-title">
        <tspan>月增长率</tspan>
      </text>
      <text x="40" y="120" class="data-value-medium">
        <tspan>+15.2%</tspan>
      </text>
      <text x="40" y="160" class="text-small">
        <tspan>Month-over-Month Growth</tspan>
      </text>
      <use href="#icon-heartbeat" x="540" y="40" width="64" height="64" class="highlight-color-stroke"/>
    </g>

    <!-- 卡片 3: 研究焦点 -->
    <rect x="1200" y="740" width="640" height="240" class="card-background"/>
    <g transform="translate(1200, 740)">
      <text x="40" y="60" class="text-content-title">
        <tspan>最新研究成果</tspan>
      </text>
      <text x="40" y="100" class="text-body">
        <tspan>基因编辑技术在癌症治疗中的应用取得突破。</tspan>
        <tspan x="40" dy="30">新药临床试验进入第二阶段。</tspan> <!-- dy 确保行间距 -->
      </text>
      <text x="40" y="180" class="text-small">
        <tspan>{date} by {author}</tspan>
      </text>
    </g>
  </g>

  <!-- 底部信息 -->
  <g id="footer-section">
    <text x="960" y="1040" text-anchor="middle" class="text-caption">
      <tspan>版权所有 © {date} 医疗健康研究中心. 保留所有权利。</tspan>
    </text>
  </g>

</svg>