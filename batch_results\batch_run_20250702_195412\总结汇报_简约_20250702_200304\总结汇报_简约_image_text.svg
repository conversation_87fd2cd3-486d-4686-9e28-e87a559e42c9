<svg xmlns="http://www.w3.org/2000/svg" width="1920" height="1080" viewBox="0 0 1920 1080">
  <defs>
    <!-- Color Palette -->
    <!-- 🚨 检查：CSS样式中无 & 符号 -->
    <style type="text/css"><![CDATA[
      .bg-color { fill: #F8FAFC; }
      .primary-color { fill: #3B82F6; }
      .secondary-color { fill: #7DD3FC; }
      .accent-color { fill: #BAE6FD; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .card-background { fill: #FFFFFF; }
      .card-border { stroke: #BAE6FD; stroke-width: 1px; }

      /* Font Styles */
      .font-inter { font-family: "Inter", "Helvetica", "Arial", sans-serif; }
      .font-poppins { font-family: "Poppins", sans-serif; }

      .main-title {
        font-size: 56px;
        font-weight: 700; /* bold */
        fill: #1E293B; /* text-primary */
      }
      .section-title {
        font-size: 36px;
        font-weight: 700; /* bold */
        fill: #1E293B; /* text-primary */
      }
      .content-title {
        font-size: 28px;
        font-weight: 700; /* bold */
        fill: #1E293B; /* text-primary */
      }
      .body-text {
        font-size: 22px;
        font-weight: 400; /* normal */
        fill: #64748B; /* text-secondary */
      }
      .small-text {
        font-size: 16px;
        font-weight: 400; /* normal */
        fill: #64748B; /* text-secondary */
      }
      .caption-text {
        font-size: 14px;
        font-weight: 400; /* normal */
        fill: #94A3B8; /* text-light */
      }
      .large-number {
        font-size: 120px; /* 超大字体 */
        font-weight: 700; /* bold */
        fill: #3B82F6; /* primary-color */
      }
    ]]></style>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7DD3FC;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#BAE6FD;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color" />

  <!-- Page Header/Footer Elements -->
  <!-- Logo - Top Left -->
  <image href="{logo_url}" x="80" y="60" width="120" height="40" preserveAspectRatio="xMidYMid meet" />

  <!-- Page Info - Top Right -->
  <text x="1840" y="90" text-anchor="end" class="small-text font-inter">
    <tspan>{date}</tspan>
  </text>

  <!-- Page Number - Bottom Right -->
  <text x="1840" y="1020" text-anchor="end" class="caption-text font-inter">
    <tspan>页面 5/10</tspan>
  </text>

  <!-- Main Content Area -->
  <g id="main-content">
    <!-- Main Title - Centered at top of content area, 预留足够空间 -->
    <!-- 🚨 检查：位置精确，无重叠，大字体预留空间 -->
    <text x="960" y="160" text-anchor="middle" class="main-title font-poppins">
      <tspan>{title}</tspan>
    </text>
    <text x="960" y="210" text-anchor="middle" class="body-text font-inter">
      <tspan>{subtitle}</tspan>
    </text>

    <!-- Content Layout: Image on Left, Text on Right -->
    <!-- Image Container (宽度约 40%，区域 800x600px 建议) -->
    <!-- 🚨 检查：元素间距，至少50px -->
    <!-- 图片区域 x=168, y=290, 宽度=700, 高度=600 -->
    <rect x="168" y="290" width="700" height="600" class="card-background" stroke="#BAE6FD" stroke-width="2" />
    <image href="{image_url}" x="178" y="300" width="680" height="580" preserveAspectRatio="xMidYMid slice" />
    <!-- 图片框的装饰性渐变叠加层 -->
    <rect x="168" y="290" width="700" height="600" fill="url(#primaryGradient)" opacity="0.1" />

    <!-- Text Content (宽度约 50%) -->
    <!-- 文本区域 x=948, 宽度=892 -->
    <!-- 图文之间间距 80px: 878 (图片右边缘) + 80 = 958。这里选择948，略微紧凑但仍符合间距要求 -->
    <g id="text-block">
      <!-- Section Title -->
      <text x="948" y="340" class="section-title font-poppins">
        <tspan>项目执行亮点和分析</tspan>
      </text>

      <!-- Large Number / Key Metric (用于强调核心要点) -->
      <!-- 🚨 检查：超大字体预留垂直空间，这里从340到480，间距140px，足够 -->
      <text x="948" y="480" class="large-number font-poppins">
        <tspan>95%</tspan>
      </text>
      <text x="948" y="520" class="content-title font-inter text-primary">
        <tspan>项目完成度提升</tspan>
      </text>
      <text x="948" y="550" class="small-text font-inter text-secondary">
        <tspan>Project Completion Rate Increase</tspan>
      </text>

      <!-- Body Content -->
      <!-- 🚨 检查：文本行间距至少30px，这里使用40px和60px -->
      <text x="948" y="620" class="body-text font-inter">
        <tspan x="948" dy="0">本次项目在预定周期内高效推进，核心任务均已按计划完成。</tspan>
        <tspan x="948" dy="40">我们通过引入新的协作工具和优化沟通流程，显著提高了团队</tspan>
        <tspan x="948" dy="40">效率。客户反馈积极，对阶段性成果表示满意。但仍需关注</tspan>
        <tspan x="948" dy="40">部分遗留问题和潜在风险，以便后续改进。</tspan>
        <!-- 元素间距，这里是中文段落和英文段落之间，预留60px -->
        <tspan x="948" dy="60">This project progressed efficiently within the scheduled timeframe,</tspan>
        <tspan x="948" dy="40">with all core tasks completed as planned. By implementing new</tspan>
        <tspan x="948" dy="40">collaboration tools and streamlining communication processes,</tspan>
        <tspan x="948" dy="40">we significantly enhanced team productivity. Customer feedback</tspan>
        <tspan x="948" dy="40">has been positive, expressing satisfaction with the interim results.</tspan>
        <tspan x="948" dy="40">However, it is still necessary to address some outstanding issues</tspan>
        <tspan x="948" dy="40">and potential risks for future improvements.</tspan>
      </text>
    </g>
  </g>

  <!-- Decorative Elements -->
  <!-- Simple line divider below main title -->
  <line x1="80" y1="240" x2="1840" y2="240" stroke="#BAE6FD" stroke-width="1" />

  <!-- Geometric shapes - bottom left corner (简约风格的点缀性元素) -->
  <rect x="80" y="940" width="60" height="60" fill="#7DD3FC" opacity="0.3" />
  <circle cx="170" cy="970" r="30" fill="#BAE6FD" opacity="0.2" />

  <!-- Author Info - Bottom Left -->
  <text x="80" y="1020" class="caption-text font-inter">
    <tspan>{author}</tspan>
  </text>

  <!-- Simple outline graphic for visual interest/data placeholder (勾线图形化元素) -->
  <!-- 🚨 检查：元素间距，与上方文本块有足够间距 -->
  <g id="simple-chart" stroke="#3B82F6" stroke-width="2" fill="none">
    <!-- 简化的折线图示意 -->
    <polyline points="948,270 1000,250 1050,260 1100,240 1150,255" />
    <circle cx="948" cy="270" r="4" fill="#3B82F6" />
    <circle cx="1000" cy="250" r="4" fill="#3B82F6" />
    <circle cx="1050" cy="260" r="4" fill="#3B82F6" />
    <circle cx="1100" cy="240" r="4" fill="#3B82F6" />
    <circle cx="1150" cy="255" r="4" fill="#3B82F6" />
  </g>

</svg>