<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- CSS Styles -->
    <style type="text/css">
      <![CDATA[
        /* Colors */
        .bg-color { fill: #F8FAFC; }
        .primary-color { fill: #1E40AF; }
        .secondary-color { fill: #475569; }
        .accent-color { fill: #3B82F6; }
        .text-primary-color { fill: #1E293B; }
        .text-secondary-color { fill: #64748B; }
        .card-background-color { fill: #FFFFFF; }
        .card-border-color { stroke: #BAE6FD; }
        .container-background-color { fill: #E0F2FE; }

        /* Fonts */
        .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
        .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }

        /* Font Sizes and Weights */
        .hero-title { font-size: 72px; font-weight: 700; } /* bold */
        .main-title { font-size: 56px; font-weight: 700; } /* bold */
        .section-title { font-size: 36px; font-weight: 700; } /* bold */
        .content-title { font-size: 28px; font-weight: 700; } /* bold */
        .body-text { font-size: 22px; font-weight: 400; } /* normal */
        .small-text { font-size: 16px; font-weight: 400; } /* normal */
        .caption-text { font-size: 14px; font-weight: 400; } /* normal */

        /* Text Alignment */
        .text-align-left { text-anchor: start; }
        .text-align-end { text-anchor: end; }
        .text-align-middle { text-anchor: middle; }

        /* Card Style */
        .card-style {
          fill: #FFFFFF;
          stroke: #BAE6FD;
          stroke-width: 1px;
          rx: 12px; /* border-radius */
        }

        /* Shadow for card, applied via filter */
        .shadow-filter {
          filter: url(#drop-shadow);
        }

        /* Icon Style */
        .icon-style {
          fill: none;
          stroke: #3B82F6; /* Accent color for icons */
          stroke-width: 2px;
          stroke-linecap: round;
          stroke-linejoin: round;
        }
      ]]>
    </style>

    <!-- Drop Shadow Filter -->
    <filter id="drop-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="4"/>
      <feGaussianBlur stdDeviation="3"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Gradient for decorative elements -->
    <linearGradient id="gradientAccent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.8"/>
      <stop offset="100%" stop-color="#1E40AF" stop-opacity="0.8"/>
    </linearGradient>

    <linearGradient id="gradientPrimary" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF" stop-opacity="0.6"/>
      <stop offset="100%" stop-color="#475569" stop-opacity="0.6"/>
    </linearGradient>

    <!-- Icon: Chart Bar (for data visualization placeholder) -->
    <symbol id="icon-chart-bar" viewBox="0 0 24 24">
      <path class="icon-style" d="M12 20V10M18 20V4M6 20V16"/>
    </symbol>

    <!-- Icon: Target (for strategic goals) -->
    <symbol id="icon-target" viewBox="0 0 24 24">
      <circle class="icon-style" cx="12" cy="12" r="10"/>
      <circle class="icon-style" cx="12" cy="12" r="6"/>
      <circle class="icon-style" cx="12" cy="12" r="2"/>
    </symbol>

    <!-- Icon: Lightbulb (for innovation) -->
    <symbol id="icon-lightbulb" viewBox="0 0 24 24">
      <path class="icon-style" d="M9 18V21H15V18M12 2C17.5228 2 22 6.47715 22 12C22 14.5975 20.9388 17.0094 19.1415 18.7303C17.3441 20.4511 14.8665 21.4939 12 21.4939C9.13349 21.4939 6.65586 20.4511 4.8585 18.7303C3.06114 17.0094 2 14.5975 2 12C2 6.47715 6.47715 2 12 2Z"/>
    </symbol>
  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color"/>

  <!-- Decorative elements -->
  <rect x="0" y="0" width="400" height="400" fill="url(#gradientPrimary)" rx="50" transform="translate(-200 -200) rotate(20)"/>
  <rect x="1920" y="1080" width="300" height="300" fill="url(#gradientAccent)" rx="30" transform="translate(-100 100) rotate(160)"/>
  <circle cx="1700" cy="150" r="80" class="container-background-color" opacity="0.7"/>
  <circle cx="200" cy="950" r="60" class="container-background-color" opacity="0.7"/>

  <!-- Logo Placeholder (Top Left) -->
  <image x="80" y="30" width="120" height="40" xlink:href="{logo_url}" />

  <!-- Main Content Area -->
  <g class="font-primary text-align-left">
    <!-- Title -->
    <text x="80" y="120" class="main-title text-primary-color">{title}</text>

    <!-- Subtitle -->
    <text x="80" y="180" class="content-title text-secondary-color">{subtitle}</text> <!-- Spacing: 180 - 120 = 60px -->

    <!-- Content Card -->
    <rect x="80" y="270" width="1760" height="750" class="card-background-color shadow-filter" rx="12"/> <!-- Spacing: 270 - 180 = 90px -->

    <!-- Left Column for Main Text and List -->
    <g transform="translate(120, 320)"> <!-- Content starts inside the card, relative to card top-left + padding -->
      <text x="0" y="0" class="body-text text-primary-color">
        <tspan x="0" y="0">
          {content}
        </tspan>
        <tspan x="0" dy="30">
          我们深入分析市场趋势，识别核心机会和挑战。
        </tspan>
        <tspan x="0" dy="30">
          通过严谨的数据模型，预测未来收益和成本结构。
        </tspan>
        <tspan x="0" dy="30">
          同时，我们全面评估潜在风险，并制定应对策略。
        </tspan>
      </text>

      <!-- Bullet Points Section Title -->
      <text x="0" y="160" class="content-title text-primary-color">核心要点</text> <!-- Spacing: 160 - (30*3 + current y of 0) = 70px -->

      <!-- Bullet Points List -->
      <g class="body-text text-primary-color">
        <!-- List Item 1 -->
        <circle cx="-15" cy="225" r="5" class="accent-color"/>
        <text x="0" y="230">
          <tspan x="0" y="230">市场分析和机遇识别</tspan>
          <tspan x="0" dy="25" class="small-text text-secondary-color">深入洞察行业动态，把握增长潜力。</tspan>
        </text>

        <!-- List Item 2 -->
        <circle cx="-15" cy="300" r="5" class="accent-color"/>
        <text x="0" y="305">
          <tspan x="0" y="305">财务预测和盈利模型</tspan>
          <tspan x="0" dy="25" class="small-text text-secondary-color">构建稳健的财务基础，实现可持续发展。</tspan>
        </text>

        <!-- List Item 3 -->
        <circle cx="-15" cy="375" r="5" class="accent-color"/>
        <text x="0" y="380">
          <tspan x="0" y="380">风险评估和应对策略</tspan>
          <tspan x="0" dy="25" class="small-text text-secondary-color">识别并管理关键风险，确保项目成功。</tspan>
        </text>

        <!-- List Item 4 -->
        <circle cx="-15" cy="450" r="5" class="accent-color"/>
        <text x="0" y="455">
          <tspan x="0" y="455">团队能力和执行计划</tspan>
          <tspan x="0" dy="25" class="small-text text-secondary-color">经验丰富的团队，高效推动战略落地。</tspan>
        </text>
      </g>
    </g>

    <!-- Right Column for Visual Emphasis / Key Metric -->
    <g transform="translate(1100, 320)"> <!-- Adjusted X for right column, relative to card top-left + padding -->
      <text x="0" y="0" class="hero-title accent-color">
        <tspan x="0" y="0">250%</tspan>
      </text>
      <text x="0" y="80" class="section-title text-primary-color">
        <tspan x="0" y="80">预期增长</tspan>
      </text>
      <text x="0" y="120" class="body-text text-secondary-color">
        <tspan x="0" y="120">Projected Growth in 3 Years</tspan>
      </text>

      <!-- Placeholder for a simple chart/data visualization -->
      <g transform="translate(0, 200)">
        <rect x="0" y="0" width="400" height="250" class="container-background-color" rx="8"/>
        <text x="200" y="125" class="content-title text-secondary-color text-align-middle">
          <tspan x="200" y="125">数据图表占位符</tspan>
          <tspan x="200" dy="30" class="small-text text-secondary-color">
            <tspan x="200" y="155">（例如：市场份额增长）</tspan>
          </tspan>
        </text>
        <use xlink:href="#icon-chart-bar" x="188" y="50" width="24" height="24" class="icon-style" stroke="#1E40AF"/>
      </g>

      <!-- Small icons as decorative elements -->
      <use xlink:href="#icon-target" x="0" y="500" width="32" height="32" class="icon-style" stroke="#3B82F6"/>
      <text x="40" y="525" class="small-text text-secondary-color">战略目标</text>

      <use xlink:href="#icon-lightbulb" x="180" y="500" width="32" height="32" class="icon-style" stroke="#3B82F6"/>
      <text x="220" y="525" class="small-text text-secondary-color">创新方案</text>
    </g>
  </g>

  <!-- Footer - Page Number and Date -->
  <g class="font-secondary caption-text text-secondary-color">
    <text x="80" y="1030" class="text-align-left">
      <tspan x="80" y="1030">页面: 4/10</tspan>
    </text>
    <text x="1750" y="1030" class="text-align-end">
      <tspan x="1750" y="1030">日期: {date}</tspan>
    </text>
  </g>
</svg>