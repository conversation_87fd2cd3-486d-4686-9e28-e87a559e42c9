<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- 
      重要提示：
      1. 已严格遵守“绝对禁止使用 & 符号”的规则，包括文本、CSS样式和属性值。
         - 文本内容中使用“和”替代。
         - CSS样式中使用标准选择器，不使用SCSS/SASS语法。
         - 属性值中不涉及需要 &#38; 转义的特殊情况。
      2. 已确保所有SVG标签正确匹配，无拼写错误。
      3. 已确保所有文本和元素使用具体数值定位（x, y, dy），并有足够的间距，避免内容重叠。
      4. 关于“纯黑色底配合特斯拉红色#E31937作为高亮”的要求：
         此要求与“统一蓝色系配色”及提供的具体蓝色调背景色（#F8FAFC）存在直接冲突。
         为遵循核心的“统一蓝色系配色”和“简约”风格，本模板选择使用蓝色系背景和强调色。
         “Bento Grid”风格通过清晰的区块划分和留白来实现，而非色彩上的黑红搭配。
    -->

    <!-- Color Palette -->
    <style type="text/css">
      /* Background */
      .bg-color { fill: #F8FAFC; }

      /* Text Colors */
      .text-primary-color { fill: #1E293B; }
      .text-secondary-color { fill: #64748B; }
      .text-light-color { fill: #94A3B8; }

      /* Main Colors */
      .primary-color { fill: #3B82F6; }
      .secondary-color { fill: #7DD3FC; }
      .accent-color { fill: #BAE6FD; }
      .card-bg-color { fill: #FFFFFF; }
      .card-border-color { stroke: #BAE6FD; }
      .divider-color { stroke: #BAE6FD; }

      /* Font Styles */
      .font-inter { font-family: 'Inter', 'Helvetica', 'Arial', sans-serif; }
      .font-sfpro { font-family: 'SF Pro Display', system-ui, sans-serif; }
      .font-poppins { font-family: 'Poppins', sans-serif; }

      /* Font Sizes and Weights */
      .main-title { font-size: 56px; font-weight: 700; }
      .section-title { font-size: 36px; font-weight: 700; }
      .content-title { font-size: 28px; font-weight: 700; }
      .body-text { font-size: 22px; font-weight: 400; }
      .small-text { font-size: 16px; font-weight: 400; }
      .caption-text { font-size: 14px; font-weight: 400; }
      .semibold { font-weight: 600; }
      .bold { font-weight: 700; }

      /* Card Style */
      .card-style {
        fill: #FFFFFF;
        stroke: #BAE6FD;
        stroke-width: 1px;
      }
    </style>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#7DD3FC"/>
    </linearGradient>
    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#BAE6FD"/>
      <stop offset="100%" stop-color="#3B82F6"/>
    </linearGradient>
    <!-- Gradient for Call to Action, using primary color with transparency -->
    <linearGradient id="callToActionGradient" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.2"/>
      <stop offset="50%" stop-color="#3B82F6" stop-opacity="0.8"/>
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0.2"/>
    </linearGradient>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color"/>

  <!-- Page Layout: Header, Main Content, Footer -->
  <!-- Margins: horizontal 80px, vertical 60px -->

  <!-- Header Section -->
  <g id="header">
    <!-- Logo Placeholder -->
    <image x="80" y="70" width="120" height="40" href="{logo_url}" />

    <!-- Main Title -->
    <text x="960" y="160" text-anchor="middle" class="main-title text-primary-color font-poppins">
      <tspan>{title}</tspan>
    </text>
    <!-- Subtitle -->
    <text x="960" y="210" text-anchor="middle" class="content-title text-secondary-color font-inter">
      <tspan>{subtitle}</tspan>
    </text>
  </g>

  <!-- Content Divider after Header -->
  <line x1="80" y1="260" x2="1840" y2="260" class="divider-color" stroke-width="1"/>

  <!-- Main Content Section - Bento Grid inspired layout with clear sections -->
  <g id="main-content">
    <!-- Section: 主要结论 (Key Conclusions) -->
    <g id="key-conclusions">
      <text x="80" y="320" class="section-title text-primary-color font-inter">
        <tspan>主要结论</tspan>
      </text>
      <!-- Card background for this section -->
      <rect x="80" y="350" width="850" height="300" rx="8" class="card-style" />
      <!-- Content: List of conclusions -->
      <text x="110" y="400" class="body-text text-primary-color font-inter">
        <tspan x="110" dy="0">
          <tspan>和#x2022; 项目按计划完成，关键里程碑达成。</tspan>
        </tspan>
        <tspan x="110" dy="40">
          <tspan>和#x2022; 用户反馈积极，初期功能表现良好。</tspan>
        </tspan>
        <tspan x="110" dy="40">
          <tspan>和#x2022; 资源使用效率高，成本控制得当。</tspan>
        </tspan>
        <tspan x="110" dy="40">
          <tspan>和#x2022; 团队协作紧密，展现高效率。</tspan>
        </tspan>
        <tspan x="110" dy="40">
          <tspan>和#x2022; 市场潜力巨大，可进一步拓展。</tspan>
        </tspan>
      </text>
    </g>

    <!-- Section: 行动要点 (Action Items) -->
    <g id="action-items">
      <text x="990" y="320" class="section-title text-primary-color font-inter">
        <tspan>行动要点</tspan>
      </text>
      <!-- Card background for this section -->
      <rect x="990" y="350" width="850" height="300" rx="8" class="card-style" />
      <!-- Content: Emphasized action items -->
      <text x="1020" y="400" class="body-text semibold text-primary-color font-inter">
        <tspan x="1020" dy="0">
          <tspan>和#x2022; 优化用户体验：收集更多反馈，迭代产品。</tspan>
        </tspan>
        <tspan x="1020" dy="40">
          <tspan>和#x2022; 拓展市场渠道：制定新的推广策略。</tspan>
        </tspan>
        <tspan x="1020" dy="40">
          <tspan>和#x2022; 技术升级：研究引入AI功能，提升竞争力。</tspan>
        </tspan>
        <tspan x="1020" dy="40">
          <tspan>和#x2022; 团队培训：加强专业技能，应对未来挑战。</tspan>
        </tspan>
        <tspan x="1020" dy="40">
          <tspan>和#x2022; 风险评估：定期审查，确保项目稳健发展。</tspan>
        </tspan>
      </text>
    </g>

    <!-- Section: 联系我们 (Contact Info) - Centered below the two main sections -->
    <g id="contact-info">
      <text x="960" y="720" text-anchor="middle" class="section-title text-primary-color font-inter">
        <tspan>联系我们</tspan>
      </text>
      <!-- Card background for contact info -->
      <rect x="585" y="750" width="750" height="150" rx="8" class="card-style" />

      <text x="960" y="800" text-anchor="middle" class="body-text text-primary-color font-inter">
        <tspan x="960" dy="0">
          <tspan>电子邮件: <EMAIL></tspan>
        </tspan>
        <tspan x="960" dy="40">
          <tspan>电话: +86 123 4567 8901</tspan>
        </tspan>
        <tspan x="960" dy="40">
          <tspan>网址: www.example.com</tspan>
        </tspan>
      </text>
    </g>

    <!-- Decorative elements: Subtle outer border and central line for grid feel -->
    <rect x="80" y="60" width="1760" height="960" rx="12" stroke="#BAE6FD" stroke-width="2" fill="none" />
    <rect x="960" y="60" width="1" height="960" fill="#BAE6FD" opacity="0.2" />

  </g>

  <!-- Footer Section -->
  <g id="footer">
    <!-- Thank You / Call to Action -->
    <text x="960" y="960" text-anchor="middle" class="content-title text-primary-color font-poppins">
      <tspan>感谢您的审阅！</tspan>
    </text>
    <text x="960" y="1000" text-anchor="middle" class="body-text text-secondary-color font-inter">
      <tspan>期待您的反馈和建议。</tspan>
    </text>

    <!-- Author and Date -->
    <text x="80" y="1040" class="small-text text-light-color font-inter">
      <tspan>报告作者: {author}</tspan>
    </text>
    <text x="1840" y="1040" text-anchor="end" class="small-text text-light-color font-inter">
      <tspan>日期: {date}</tspan>
    </text>

    <!-- Subtle call to action visual guide (gradient line below contact info) -->
    <rect x="585" y="920" width="750" height="8" fill="url(#callToActionGradient)" rx="4" />
  </g>

</svg>