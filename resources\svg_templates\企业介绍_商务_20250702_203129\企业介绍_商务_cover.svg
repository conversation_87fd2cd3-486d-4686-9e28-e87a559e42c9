<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- 渐变背景定义 -->
    <linearGradient id="backgroundGradient" x1="960" y1="0" x2="960" y2="1080" gradientUnits="userSpaceOnUse">
      <stop stop-color="#F8FAFC"/>
      <stop offset="1" stop-color="#E0F2FE"/>
    </linearGradient>

    <!-- 主色渐变 (用于装饰元素) -->
    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox" gradientTransform="rotate(135)">
      <stop stop-color="#1E40AF"/>
      <stop offset="1" stop-color="#475569"/>
    </linearGradient>

    <!-- 强调色渐变 (用于装饰元素) -->
    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox" gradientTransform="rotate(45)">
      <stop stop-color="#3B82F6"/>
      <stop offset="1" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- 几何图形路径示例 (抽象的商务感) -->
    <path id="pathTriangle" d="M0 0 L100 0 L50 86.6 Z" />
    <path id="pathSquare" d="M0 0 H100 V100 H0 Z" />
    <path id="pathCircle" d="M50 0 A50 50 0 1 1 50 100 A50 50 0 1 1 50 0 Z" />
    <path id="pathAbstractLine" d="M0 50 C25 25, 75 75, 100 50 S150 25, 200 50" />
    <path id="pathConnection" d="M0 0 L100 100 M0 100 L100 0" />

  </defs>

  <style>
    /* 全局字体定义 */
    .font-primary {
      font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
    }
    .font-secondary {
      font-family: "Source Han Sans CN", "Noto Sans CJK SC", sans-serif;
    }
    .font-accent {
      font-family: "Times New Roman", serif;
    }

    /* 文本颜色 */
    .text-primary-color {
      fill: #1E293B;
    }
    .text-secondary-color {
      fill: #64748B;
    }
    .text-light-color {
      fill: #94A3B8;
    }

    /* 其他颜色 */
    .primary-color-fill {
      fill: #1E40AF;
    }
    .secondary-color-fill {
      fill: #475569;
    }
    .accent-color-fill {
      fill: #3B82F6;
    }

    /* 字体大小和粗细 */
    .hero-title {
      font-size: 72px;
      font-weight: 700; /* bold */
      line-height: 1.1; /* tight */
    }
    .main-title {
      font-size: 56px;
      font-weight: 700; /* bold */
      line-height: 1.1; /* tight */
    }
    .section-title {
      font-size: 36px;
      font-weight: 600; /* semibold */
      line-height: 1.4; /* normal */
    }
    .content-title {
      font-size: 28px;
      font-weight: 500; /* medium */
      line-height: 1.4; /* normal */
    }
    .body-text {
      font-size: 22px;
      font-weight: 400; /* normal */
      line-height: 1.6; /* relaxed */
    }
    .small-text {
      font-size: 16px;
      font-weight: 400; /* normal */
      line-height: 1.6; /* relaxed */
    }
    .caption-text {
      font-size: 14px;
      font-weight: 400; /* normal */
      line-height: 1.6; /* relaxed */
    }

    /* 边框和阴影 (用于卡片，此处仅作定义参考，本页不直接使用) */
    .card-border {
      stroke: #BAE6FD;
      stroke-width: 1px;
    }
    .card-shadow {
      filter: drop-shadow(0px 4px 6px rgba(0, 0, 0, 0.1)) drop-shadow(0px 2px 4px rgba(0, 0, 0, 0.06));
    }

    /* 装饰元素描边 */
    .outline-accent {
      stroke: #3B82F6;
      stroke-width: 2;
      fill: none;
    }
    .outline-primary {
      stroke: #1E40AF;
      stroke-width: 2;
      fill: none;
    }
  </style>

  <!-- 背景层 -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

  <!-- 顶部左侧Logo -->
  <g id="logo-section">
    <image xlink:href="{logo_url}" x="80" y="60" width="auto" height="80" />
  </g>

  <!-- 核心内容区域 (主标题和副标题) -->
  <g id="main-content" text-anchor="middle">
    <!-- 主标题 - 中文大字体粗体 -->
    <text x="960" y="470" class="font-primary hero-title text-primary-color">
      <tspan x="960" y="470">{title}</tspan>
    </text>

    <!-- 副标题 - 英文小字点缀，位于主标题下方，确保间距 -->
    <text x="960" y="540" class="font-accent content-title text-secondary-color">
      <tspan x="960" y="540">{subtitle}</tspan>
    </text>
  </g>

  <!-- 底部装饰元素 - 几何图形和渐变 -->
  <g id="decorative-elements">
    <!-- 左下角抽象形状 1 -->
    <rect x="100" y="850" width="150" height="150" rx="20" fill="url(#primaryGradient)" opacity="0.6"/>
    <path d="M100 850 L250 850 L250 1000 L100 1000 Z" class="outline-primary" stroke-dasharray="10 5" opacity="0.8"/>

    <!-- 左下角抽象形状 2 -->
    <circle cx="200" cy="900" r="60" fill="#3B82F6" opacity="0.3"/>
    <circle cx="200" cy="900" r="70" class="outline-accent" opacity="0.7"/>

    <!-- 右下角抽象形状 1 (略微透明的强调色矩形) -->
    <rect x="1500" y="900" width="300" height="100" rx="30" fill="#3B82F6" opacity="0.2"/>

    <!-- 右下角抽象形状 2 (主色调的抽象线条) -->
    <path d="M1600 850 C1650 800, 1750 950, 1800 900 S1900 800, 1900 850" class="outline-primary" stroke-width="3" opacity="0.5"/>
    <path d="M1550 950 C1600 900, 1700 1050, 1750 1000 S1850 900, 1850 950" class="outline-accent" stroke-width="2" opacity="0.6"/>

    <!-- 顶部右侧点缀 (小方块和线条) -->
    <rect x="1750" y="80" width="50" height="50" rx="10" fill="#475569" opacity="0.4"/>
    <line x1="1800" y1="105" x2="1850" y2="105" stroke="#475569" stroke-width="2" opacity="0.5"/>
    <line x1="1750" y1="130" x2="1800" y2="130" stroke="#475569" stroke-width="2" opacity="0.5"/>

    <!-- 中间区域的轻微装饰，与主标题保持距离 -->
    <circle cx="500" cy="300" r="30" fill="url(#accentGradient)" opacity="0.15"/>
    <circle cx="1420" cy="780" r="40" fill="url(#primaryGradient)" opacity="0.15"/>

    <!-- 抽象数据流线型装饰 -->
    <path d="M80 300 C200 200, 400 250, 500 350 S600 500, 550 650" class="outline-accent" stroke-width="1.5" opacity="0.3" stroke-dasharray="5 5"/>
    <path d="M1840 700 C1700 800, 1500 750, 1400 650 S1300 500, 1350 350" class="outline-primary" stroke-width="1.5" opacity="0.3" stroke-dasharray="5 5"/>

  </g>

  <!-- 确保所有元素不重叠，并有足够的间距 -->
  <!-- Logo (80,60) -->
  <!-- Main Title (960, 470) -->
  <!-- Subtitle (960, 540) - 距离标题70px，足够 -->
  <!-- Decorative elements are placed far from text -->

</svg>