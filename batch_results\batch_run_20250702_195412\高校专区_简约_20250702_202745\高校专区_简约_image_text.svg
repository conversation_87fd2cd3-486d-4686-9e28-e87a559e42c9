<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <style>
    /* Global Styles */
    svg {
      background-color: #F8FAFC; /* Background color */
      font-family: 'Inter', Helvetica, Arial, sans-serif;
    }

    /* Colors */
    .primary-color { fill: #3B82F6; }
    .secondary-color { fill: #7DD3FC; }
    .accent-color { fill: #BAE6FD; }
    .text-primary { fill: #1E293B; }
    .text-secondary { fill: #64748B; }
    .card-background { fill: #FFFFFF; }
    .card-border { stroke: #BAE6FD; }
    .line-color { stroke: #BAE6FD; }

    /* Font Styles */
    .font-hero-title {
      font-family: 'Inter', Helvetica, Arial, sans-serif;
      font-size: 72px;
      font-weight: 700; /* bold */
      line-height: 1.1; /* tight */
    }
    .font-main-title {
      font-family: 'Inter', Helvetica, Arial, sans-serif;
      font-size: 56px;
      font-weight: 700; /* bold */
      line-height: 1.1;
    }
    .font-section-title {
      font-family: 'Inter', Helvetica, Arial, sans-serif;
      font-size: 36px;
      font-weight: 600; /* semibold */
      line-height: 1.4;
    }
    .font-content-title {
      font-family: 'Inter', Helvetica, Arial, sans-serif;
      font-size: 28px;
      font-weight: 500; /* medium */
      line-height: 1.4;
    }
    .font-body-text {
      font-family: 'Inter', Helvetica, Arial, sans-serif;
      font-size: 22px;
      font-weight: 400; /* normal */
      line-height: 1.6; /* relaxed */
    }
    .font-small-text {
      font-family: 'Inter', Helvetica, Arial, sans-serif;
      font-size: 16px;
      font-weight: 400;
      line-height: 1.4;
    }
    .font-caption {
      font-family: 'Inter', Helvetica, Arial, sans-serif;
      font-size: 14px;
      font-weight: 400;
      line-height: 1.4;
    }

    /* Card Style */
    .card-style {
      fill: #FFFFFF;
      stroke: #BAE6FD;
      stroke-width: 1px;
    }

    /* Decorative Elements */
    .decorative-line {
      stroke: #7DD3FC;
      stroke-width: 2px;
      stroke-linecap: round;
    }
  </style>

  <!-- Background Layer -->
  <rect x="0" y="0" width="1920" height="1080" fill="#F8FAFC"/>

  <!-- Logo (Top Left Corner) -->
  <!-- Position: x=80 (left margin), y=60 (top margin) -->
  <image href="{logo_url}" x="80" y="60" width="160" height="40" alt="University Logo" />

  <!-- Main Content Area: Image and Text Layout (Balanced Left/Right) -->
  <g id="main-content-layout">
    <!-- Image Column (Left Side) -->
    <!-- Image area: 704px width (approx 40% of content width), 528px height (scaled from 800x600 aspect ratio) -->
    <!-- X position: 80 (left margin) -->
    <!-- Y position: 276 (vertically centered within usable height of 960px, i.e., 60 + (960-528)/2) -->
    <g id="image-section">
      <!-- Image frame with white background and light blue border -->
      <rect x="80" y="276" width="704" height="528" class="card-style"/>
      <!-- Placeholder for the actual image -->
      <image href="{image_url}" x="80" y="276" width="704" height="528" preserveAspectRatio="xMidYMid slice" />
      <!-- Overlay border for emphasis (primary color) -->
      <rect x="80" y="276" width="704" height="528" stroke="#3B82F6" stroke-width="2" fill="none" />
    </g>

    <!-- Text Column (Right Side) -->
    <!-- Text column starts after image width + 176px gap -->
    <!-- X position: 80 (image start) + 704 (image width) + 176 (gap) = 960 -->
    <g id="text-section">
      <!-- Main Title -->
      <!-- Y position: 200 (positioned slightly above image top for visual hierarchy) -->
      <text x="960" y="200" class="font-main-title text-primary">
        <tspan>{title}</tspan>
      </text>

      <!-- Subtitle -->
      <!-- Y position: 200 (title y) + 56 (title font size) + 80 (element gap) = 336 -->
      <text x="960" y="336" class="font-content-title text-secondary">
        <tspan>{subtitle}</tspan>
      </text>

      <!-- Large Number Highlight (Example: Research Success Rate) -->
      <!-- This element uses an extra large font for emphasis -->
      <!-- Y position: 336 (subtitle y) + 28 (subtitle font size) + 106 (element gap) = 470 -->
      <text x="960" y="470" class="text-primary" style="font-family: 'Inter', Helvetica, Arial, sans-serif; font-size: 120px; font-weight: 900;">
        <tspan>95%</tspan>
      </text>
      <text x="960" y="520" class="font-small-text text-secondary">
        <tspan>Research Success Rate</tspan>
      </text>

      <!-- Body Content -->
      <!-- Y position: 520 (small text y) + 16 (small text font size) + 64 (element gap) = 600 -->
      <!-- Using multiple tspan elements for line breaks, with dy >= 30px -->
      <text x="960" y="600" class="font-body-text text-primary">
        <tspan x="960" dy="0">{content}</tspan>
        <tspan x="960" dy="40">深入探讨前沿科技和基础理论，</tspan>
        <tspan x="960" dy="40">推动学术研究迈向新高度。</tspan>
        <tspan x="960" dy="40">聚焦创新实践和跨学科合作，</tspan>
        <tspan x="960" dy="40">助力培养未来科研领军人才。</tspan>
      </text>

      <!-- Date and Author Information -->
      <!-- Y position: 600 (body content start) + 4 * 40 (body content lines) + 50 (element gap) = 810 -->
      <text x="960" y="810" class="font-small-text text-secondary">
        <tspan x="960" dy="0">{date}</tspan>
        <tspan x="960" dy="30">{author}</tspan>
      </text>
    </g>
  </g>

  <!-- Decorative Elements for Minimalist Style -->
  <!-- Horizontal line below the text content for visual separation -->
  <!-- X1: 960 (text column start), Y1: 280 (aligned with image top border) -->
  <!-- X2: 1840 (right margin), Y2: 280 -->
  <line x1="960" y1="280" x2="1840" y2="280" class="line-color" stroke-width="1" />

  <!-- Simple geometric shapes with opacity for a subtle tech feel -->
  <circle cx="1800" cy="120" r="20" fill="#BAE6FD" opacity="0.6" />
  <rect x="1750" y="150" width="50" height="50" fill="#7DD3FC" opacity="0.3" />
  
  <!-- A subtle decorative line at the bottom right -->
  <line x1="1700" y1="960" x2="1840" y2="960" class="decorative-line" />

</svg>