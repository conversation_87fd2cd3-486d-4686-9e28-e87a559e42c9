{"batch_info": {"run_id": "20250702_195412", "start_time": "2025-07-02T19:54:12.885179", "end_time": "2025-07-02T20:48:30.197981", "total_time": "0:54:17.312802", "total_time_seconds": 3257.312802, "generation_mode": "concurrent_with_retry"}, "concurrent_config": {"max_concurrent_pages": 5, "max_retries_per_page": 2, "description": "每个模板集合内部并发生成页面，每页支持重试机制"}, "statistics": {"total_combinations": 13, "completed_combinations": 11, "failed_combinations": 2, "success_rate": 84.62, "average_time_per_combination": 248.71, "total_templates_generated": 109, "total_failed_templates": 1}, "successful_results": [{"combination_id": "年中总结_商务", "scenario": "年中总结", "style": "商务", "success": true, "generation_time": 285.5571789741516, "total_templates": 10, "failed_templates": 0, "save_path": "E:\\jimu-new\\resources\\svg_templates\\年中总结_商务_20250702_195858", "timestamp": "2025-07-02T19:58:58.442755", "concurrent_pages": 5, "retries_per_page": 2}, {"combination_id": "总结汇报_简约", "scenario": "总结汇报", "style": "简约", "success": true, "generation_time": 243.61620664596558, "total_templates": 10, "failed_templates": 0, "save_path": "E:\\jimu-new\\resources\\svg_templates\\总结汇报_简约_20250702_200304", "timestamp": "2025-07-02T20:03:04.071182", "concurrent_pages": 5, "retries_per_page": 2}, {"combination_id": "教育培训_插画", "scenario": "教育培训", "style": "插画", "success": true, "generation_time": 351.7640571594238, "total_templates": 10, "failed_templates": 0, "save_path": "E:\\jimu-new\\resources\\svg_templates\\教育培训_插画_20250702_200857", "timestamp": "2025-07-02T20:08:57.844956", "concurrent_pages": 5, "retries_per_page": 2}, {"combination_id": "医学医疗_商务", "scenario": "医学医疗", "style": "商务", "success": true, "generation_time": 350.24013447761536, "total_templates": 10, "failed_templates": 0, "save_path": "E:\\jimu-new\\resources\\svg_templates\\医学医疗_商务_20250702_201450", "timestamp": "2025-07-02T20:14:50.087762", "concurrent_pages": 5, "retries_per_page": 2}, {"combination_id": "营销推广_潮流", "scenario": "营销推广", "style": "潮流", "success": true, "generation_time": 158.67008924484253, "total_templates": 10, "failed_templates": 0, "save_path": "E:\\jimu-new\\resources\\svg_templates\\营销推广_潮流_20250702_201730", "timestamp": "2025-07-02T20:17:30.770695", "concurrent_pages": 5, "retries_per_page": 2}, {"combination_id": "商业计划_商务", "scenario": "商业计划", "style": "商务", "success": true, "generation_time": 352.3440158367157, "total_templates": 9, "failed_templates": 1, "save_path": "E:\\jimu-new\\resources\\svg_templates\\商业计划_商务_20250702_202325", "timestamp": "2025-07-02T20:23:25.117604", "concurrent_pages": 5, "retries_per_page": 2}, {"combination_id": "高校专区_简约", "scenario": "高校专区", "style": "简约", "success": true, "generation_time": 258.09456491470337, "total_templates": 10, "failed_templates": 0, "save_path": "E:\\jimu-new\\resources\\svg_templates\\高校专区_简约_20250702_202745", "timestamp": "2025-07-02T20:27:45.218250", "concurrent_pages": 5, "retries_per_page": 2}, {"combination_id": "企业介绍_商务", "scenario": "企业介绍", "style": "商务", "success": true, "generation_time": 222.37385749816895, "total_templates": 10, "failed_templates": 0, "save_path": "E:\\jimu-new\\resources\\svg_templates\\企业介绍_商务_20250702_203129", "timestamp": "2025-07-02T20:31:29.608566", "concurrent_pages": 5, "retries_per_page": 2}, {"combination_id": "党政宣传_政务", "scenario": "党政宣传", "style": "政务", "success": true, "generation_time": 422.88216972351074, "total_templates": 10, "failed_templates": 0, "save_path": "E:\\jimu-new\\resources\\svg_templates\\党政宣传_政务_20250702_203834", "timestamp": "2025-07-02T20:38:34.506197", "concurrent_pages": 5, "retries_per_page": 2}, {"combination_id": "自我介绍_弥散", "scenario": "自我介绍", "style": "弥散", "success": true, "generation_time": 291.3160560131073, "total_templates": 10, "failed_templates": 0, "save_path": "E:\\jimu-new\\resources\\svg_templates\\自我介绍_弥散_20250702_204327", "timestamp": "2025-07-02T20:43:27.834358", "concurrent_pages": 5, "retries_per_page": 2}, {"combination_id": "发布会_商务", "scenario": "发布会", "style": "商务", "success": true, "generation_time": 210.03708505630493, "total_templates": 10, "failed_templates": 0, "save_path": "E:\\jimu-new\\resources\\svg_templates\\发布会_商务_20250702_204721", "timestamp": "2025-07-02T20:47:21.330874", "concurrent_pages": 5, "retries_per_page": 2}], "failed_results": [{"combination_id": "分析报告_科技", "scenario": "分析报告", "style": "科技", "success": false, "error": "设计规范生成失败", "generation_time": 19.44385004043579, "timestamp": "2025-07-02T20:43:49.290728"}, {"combination_id": "公益宣传_插画", "scenario": "公益宣传", "style": "插画", "success": false, "error": "设计规范生成失败", "generation_time": 66.85803008079529, "timestamp": "2025-07-02T20:48:30.197521"}], "summary": {"scenarios_tested": ["党政宣传", "商业计划", "总结汇报", "年中总结", "发布会", "自我介绍", "高校专区", "教育培训", "医学医疗", "企业介绍", "营销推广"], "styles_tested": ["政务", "插画", "弥散", "潮流", "商务", "简约"], "fastest_combination": "营销推广_潮流", "slowest_combination": "党政宣传_政务", "most_templates": "年中总结_商务"}}