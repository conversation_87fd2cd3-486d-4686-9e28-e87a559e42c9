<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <style type="text/css"><![CDATA[
      /* Colors */
      .bg-color { fill: #F8FAFC; }
      .primary-color-fill { fill: #1E40AF; }
      .secondary-color-fill { fill: #475569; }
      .accent-color-fill { fill: #3B82F6; }
      .text-primary-color { fill: #1E293B; }
      .text-secondary-color { fill: #64748B; }
      .text-light-color { fill: #94A3B8; }

      /* Fonts */
      .font-primary { font-family: "Microsoft YaHei", "Segoe UI", sans-serif; }
      .font-accent { font-family: "Times New Roman", serif; }

      /* Font Sizes */
      .hero-title-size { font-size: 72px; }
      .main-title-size { font-size: 56px; }
      .section-title-size { font-size: 36px; }
      .content-title-size { font-size: 28px; }
      .body-text-size { font-size: 22px; }
      .small-text-size { font-size: 16px; }
      .caption-size { font-size: 14px; }

      /* Font Weights */
      .font-normal { font-weight: 400; }
      .font-semibold { font-weight: 600; }
      .font-bold { font-weight: 700; }

      /* General Text Styling */
      .title-style {
        text-anchor: start;
        dominant-baseline: hanging;
      }
      .subtitle-style {
        text-anchor: start;
        dominant-baseline: hanging;
      }
      .caption-style {
        text-anchor: start;
        dominant-baseline: hanging;
      }
      .logo-style {
        opacity: 0.95;
      }

      /* Decorative Elements */
      .shape-fill-primary-subtle { fill: #1E40AF; opacity: 0.1; }
      .shape-fill-accent-subtle { fill: #3B82F6; opacity: 0.15; }
      .line-stroke-primary { stroke: #1E40AF; stroke-width: 2; opacity: 0.2; }
      .line-stroke-accent { stroke: #3B82F6; stroke-width: 3; opacity: 0.3; }

      /* Gradient definitions for shapes */
      /* Accent color with transparency for a tech feel */
      .gradient-accent-transparent-stop1 { stop-color: #3B82F6; stop-opacity: 0.8; }
      .gradient-accent-transparent-stop2 { stop-color: #3B82F6; stop-opacity: 0.2; }
      /* Primary color with transparency */
      .gradient-primary-transparent-stop1 { stop-color: #1E40AF; stop-opacity: 0.8; }
      .gradient-primary-transparent-stop2 { stop-color: #1E40AF; stop-opacity: 0.2; }

    ]]></style>

    <!-- Gradient for large background elements -->
    <linearGradient id="accentTransparentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" class="gradient-accent-transparent-stop1" />
      <stop offset="100%" class="gradient-accent-transparent-stop2" />
    </linearGradient>

    <linearGradient id="primaryTransparentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" class="gradient-primary-transparent-stop1" />
      <stop offset="100%" class="gradient-primary-transparent-stop2" />
    </linearGradient>

  </defs>

  <!-- Background layer -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color" />

  <!-- Decorative elements - Bento Grid style with large, transparent shapes for visual impact -->
  <!-- Large abstract shape 1 (accent color, transparent) -->
  <rect x="1000" y="0" width="920" height="1080" fill="url(#accentTransparentGradient)" />
  <!-- Large abstract shape 2 (primary color, transparent) -->
  <rect x="0" y="600" width="1920" height="480" fill="url(#primaryTransparentGradient)" />

  <!-- Smaller overlapping shapes with subtle opacity -->
  <rect x="150" y="850" width="300" height="150" class="shape-fill-accent-subtle" rx="15" ry="15" />
  <rect x="400" y="800" width="200" height="200" class="shape-fill-primary-subtle" rx="15" ry="15" />
  <rect x="1300" y="100" width="400" height="200" class="shape-fill-primary-subtle" rx="20" ry="20" />
  <rect x="1450" y="250" width="250" height="150" class="shape-fill-accent-subtle" rx="20" ry="20" />

  <!-- Abstract Line Art for tech/business feel -->
  <line x1="80" y1="200" x2="600" y2="200" class="line-stroke-accent" />
  <line x1="80" y1="250" x2="500" y2="250" class="line-stroke-primary" />
  <line x1="80" y1="300" x2="400" y2="300" class="line-stroke-accent" />

  <line x1="1840" y1="800" x2="1600" y2="800" class="line-stroke-accent" />
  <line x1="1840" y1="850" x2="1700" y2="850" class="line-stroke-primary" />
  <line x1="1840" y1="900" x2="1600" y2="900" class="line-stroke-accent" />

  <!-- Small geometric accents -->
  <path d="M100 100 L 150 50 L 200 100 L 150 150 Z" class="shape-fill-accent-subtle" />
  <circle cx="1700" cy="950" r="80" class="shape-fill-primary-subtle" />

  <!-- Brand Logo - positioned top-left with generous margin -->
  <image x="80" y="60" width="200" height="80" class="logo-style" xlink:href="{logo_url}" preserveAspectRatio="xMidYMid meet" />

  <!-- Main Title - Super large font, bold, primary text color -->
  <text x="80" y="380" class="text-primary-color font-primary hero-title-size font-bold title-style">
    <tspan x="80" dy="0">{title}</tspan>
  </text>

  <!-- Subtitle - Smaller than title, secondary text color -->
  <text x="80" y="500" class="text-secondary-color font-primary section-title-size font-semibold subtitle-style">
    <tspan x="80" dy="0">{subtitle}</tspan>
  </text>

  <!-- Date and Author - Small text at bottom for context -->
  <text x="80" y="980" class="text-light-color font-primary small-text-size font-normal caption-style">
    <tspan x="80" dy="0">发布日期: {date}</tspan>
    <tspan x="80" dy="24">发布者: {author}</tspan>
  </text>

</svg>