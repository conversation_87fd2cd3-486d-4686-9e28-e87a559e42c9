<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" role="img" aria-labelledby="svgTitle svgDescription">
  <title id="svgTitle">项目总结汇报封面页模板</title>
  <desc id="svgDescription">一份高质量的SVG模板，用于项目或工作阶段性总结汇报的封面页，风格简约，采用统一蓝色系配色，突出主标题、副标题和品牌Logo。</desc>

  <defs>
    <!-- CSS样式定义 - 严格禁止使用 & 符号 -->
    <style type="text/css">
      <![CDATA[
        /* 背景色 */
        .bg-color { fill: #F8FAFC; }

        /* 主要颜色 */
        .primary-color { fill: #3B82F6; }
        .secondary-color { fill: #7DD3FC; }
        .accent-color { fill: #BAE6FD; }

        /* 文字颜色 */
        .text-primary-color { fill: #1E293B; }
        .text-secondary-color { fill: #64748B; }
        .text-light-color { fill: #94A3B8; }

        /* 字体系统 */
        .font-primary { font-family: 'Inter', 'Helvetica', 'Arial', sans-serif; }
        .font-accent { font-family: 'Poppins', sans-serif; }

        /* 标题样式 */
        .main-title {
          font-size: 56px; /* main_title */
          font-weight: 700; /* bold */
          fill: #1E293B; /* text_primary */
          line-height: 1.1; /* tight */
          letter-spacing: 0em; /* normal */
        }
        .section-title {
          font-size: 36px; /* section_title */
          font-weight: 400; /* normal */
          fill: #64748B; /* text_secondary */
          line-height: 1.4; /* normal */
          letter-spacing: 0em; /* normal */
        }
        .body-text {
          font-size: 22px; /* body_text */
          font-weight: 400;
          fill: #64748B;
          line-height: 1.4;
        }
        .small-text {
          font-size: 16px; /* small_text */
          font-weight: 400;
          fill: #94A3B8; /* text_light */
        }
        .caption-text {
          font-size: 14px; /* caption */
          font-weight: 400;
          fill: #94A3B8;
        }

        /* 装饰元素样式 */
        .decorative-line {
            stroke: #BAE6FD; /* accent_color */
            stroke-width: 1;
            opacity: 0.5;
        }
        .soft-fill {
            fill: #3B82F6; /* primary_color */
            opacity: 0.08; /* subtle transparency */
        }
        .soft-secondary-fill {
            fill: #7DD3FC; /* secondary_color */
            opacity: 0.08;
        }
      ]]>
    </style>

    <!-- 背景渐变，增加深度感 -->
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#F8FAFC;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E0F2FE;stop-opacity:0.6" />
    </linearGradient>

    <!-- 主色渐变，用于强调元素 -->
    <linearGradient id="primaryAccentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#7DD3FC;stop-opacity:0.9" />
    </linearGradient>

  </defs>

  <!-- 背景层 -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)" />

  <!-- 装饰元素 - 几何形状和线条，营造简约和专业感 -->
  <!-- 左上角大圆角矩形 -->
  <rect x="-150" y="-150" width="600" height="600" rx="150" ry="150" class="soft-fill" />
  <!-- 右下角大圆角矩形 -->
  <rect x="1570" y="770" width="400" height="400" rx="100" ry="100" class="soft-secondary-fill" />

  <!-- Bento Grid 风格的抽象线条分割 -->
  <!-- 外框线 -->
  <rect x="80" y="60" width="1760" height="960" rx="10" ry="10" stroke="#BAE6FD" stroke-width="2" fill="none" opacity="0.3" />

  <!-- 垂直分割线 -->
  <line x1="960" y1="60" x2="960" y2="1020" class="decorative-line" />
  <!-- 水平分割线 -->
  <line x1="80" y1="540" x2="1840" y2="540" class="decorative-line" />

  <!-- 左上角小方块装饰 -->
  <rect x="150" y="130" width="80" height="80" rx="10" ry="10" class="secondary-color" opacity="0.15" />
  <!-- 右下角小圆圈装饰 -->
  <circle cx="1770" cy="950" r="50" class="primary-color" opacity="0.15" />

  <!-- 主要内容区域 - 居中布局 -->
  <g transform="translate(960, 540)">
    <!-- 主标题 - 中文大字体粗体 -->
    <text x="0" y="-180" text-anchor="middle" class="main-title font-primary">
      <tspan x="0" dy="0">项目阶段性总结汇报</tspan>
    </text>

    <!-- 副标题 - 英文小字作为点缀 -->
    <text x="0" y="-100" text-anchor="middle" class="section-title font-primary">
      <tspan x="0" dy="0">Project Phase Summary Report</tspan>
    </text>

    <!-- 强调信息框 -->
    <rect x="-300" y="0" width="600" height="120" rx="20" ry="20" fill="url(#primaryAccentGradient)" opacity="0.9" />
    <text x="0" y="60" text-anchor="middle" class="body-text font-primary" fill="#FFFFFF">
      <tspan x="0" dy="0">关键执行成果和改进建议</tspan>
    </text>

    <!-- 日期和作者信息 -->
    <text x="0" y="200" text-anchor="middle" class="body-text font-primary text-secondary-color">
      <tspan x="0" dy="0">日期: {date}</tspan>
      <tspan x="0" dy="40">作者: {author}</tspan>
    </text>

  </g>

  <!-- 品牌Logo预留位置 - 左上角 -->
  <g id="logoArea">
    <image x="120" y="90" width="180" height="90" xlink:href="{logo_url}" />
    <!-- Logo占位符文本，确保内容不重叠 -->
    <text x="120" y="200" class="caption-text font-primary text-light-color">
      <tspan x="120" dy="0">{logo_url: 您的公司Logo}</tspan>
    </text>
  </g>

</svg>