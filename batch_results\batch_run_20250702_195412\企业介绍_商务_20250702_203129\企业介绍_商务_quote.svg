<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradients based on design_guidelines -->
    <linearGradient id="backgroundGradient" x1="960" y1="0" x2="960" y2="1080" gradientUnits="userSpaceOnUse">
      <stop stop-color="#F8FAFC"/>
      <stop offset="1" stop-color="#E0F2FE"/>
    </linearGradient>

    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox">
      <stop stop-color="#1E40AF"/>
      <stop offset="1" stop-color="#475569"/>
    </linearGradient>

    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox">
      <stop stop-color="#3B82F6"/>
      <stop offset="1" stop-color="#1E40AF"/>
    </linearGradient>

    <linearGradient id="quoteTextGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox">
      <stop stop-color="#1E3A8A"/>
      <stop offset="1" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- Filter for subtle shadow for cards/elements -->
    <!-- Note: feColorMatrix values ensure opacity is applied correctly for the shadow -->
    <filter id="cardShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="8"/>
      <feGaussianBlur stdDeviation="12" result="offset-blur"/>
      <feComposite operator="out" in="SourceGraphic" in2="offset-blur"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feBlend mode="normal" in="SourceGraphic"/>
    </filter>
  </defs>

  <style>
    /* Font Definitions */
    .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
    .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
    .font-accent { font-family: 'Times New Roman', serif; }

    /* Text Styles */
    /* Main quote in Chinese - large and bold for emphasis */
    .main-quote-cn {
      font-size: 56px; /* main_title */
      font-weight: 700; /* bold */
      fill: #1E293B; /* text_primary */
      line-height: 1.4; /* normal */
    }
    /* Supporting quote in English - smaller and lighter */
    .main-quote-en {
      font-size: 36px; /* section_title */
      font-weight: 300; /* light */
      fill: #64748B; /* text_secondary */
      line-height: 1.4; /* normal */
    }
    /* Quote source name */
    .quote-source {
      font-size: 22px; /* body_text */
      font-weight: 400; /* normal */
      fill: #475569; /* secondary_color */
    }
    /* Quote source role/title */
    .quote-source-role {
      font-size: 16px; /* small_text */
      font-weight: 300; /* light */
      fill: #94A3B8; /* text_light */
    }
    /* Large decorative quote marks */
    .quote-mark {
      fill: #3B82F6; /* accent_color */
      opacity: 0.2; /* subtle transparency */
    }
    /* Decorative shapes with primary color */
    .decorative-shape-primary {
      fill: #1E40AF; /* primary_color */
      opacity: 0.05; /* very subtle transparency */
    }
    /* Decorative shapes with accent color */
    .decorative-shape-accent {
      fill: #3B82F6; /* accent_color */
      opacity: 0.1; /* subtle transparency */
    }
    /* Background for the quote card */
    .card-background {
      fill: #FFFFFF; /* card_background */
      stroke: #BAE6FD; /* card_border */
      stroke-width: 1;
      filter: url(#cardShadow); /* Apply shadow filter */
    }

    /* General text alignment utilities */
    .text-center { text-anchor: middle; }
    .text-left { text-anchor: start; }
    .text-right { text-anchor: end; }
  </style>

  <!-- Background Layer - Fills the entire canvas -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

  <!-- Decorative Elements - Subtle geometric shapes for visual interest -->
  <g id="DecorativeElements">
    <!-- Large transparent circle, primary color -->
    <circle cx="200" cy="900" r="150" class="decorative-shape-primary"/>
    <!-- Large transparent circle, accent color -->
    <circle cx="1720" cy="180" r="120" class="decorative-shape-accent"/>

    <!-- Abstract curved lines, subtle -->
    <path d="M 0 400 C 300 300, 600 500, 960 450" stroke="#475569" stroke-width="2" opacity="0.1" fill="none"/>
    <path d="M 1920 700 C 1620 800, 1320 600, 960 650" stroke="#3B82F6" stroke-width="2" opacity="0.1" fill="none"/>
    
    <!-- Rounded rectangles, subtle -->
    <rect x="1500" y="800" width="250" height="100" rx="12" ry="12" class="decorative-shape-accent"/>
    <rect x="170" y="150" width="180" height="70" rx="12" ry="12" class="decorative-shape-primary"/>
  </g>

  <!-- Main Content Area - Quote Module, centered on the canvas -->
  <g id="QuoteModule" transform="translate(960, 540)">
    <!-- Quote Card Background - Provides a clean base for the quote -->
    <rect x="-700" y="-300" width="1400" height="600" rx="24" ry="24" class="card-background"/>

    <!-- Top Left Decorative Quote Mark -->
    <text x="-600" y="-150" class="quote-mark font-accent" font-size="200" text-anchor="start">“</text>

    <!-- Main Quote Text Block -->
    <text x="0" y="-100" class="font-primary text-center">
      <!-- Chinese part of the quote - bold and prominent -->
      <tspan x="0" dy="0" class="main-quote-cn">
        “创新是企业发展的核心动力，
      </tspan>
      <tspan x="0" dy="60" class="main-quote-cn">
        更是我们持续成功的基石。”
      </tspan>
      <!-- English part of the quote - smaller and lighter, below Chinese -->
      <tspan x="0" dy="100" class="main-quote-en">
        "Innovation is the core driving force of enterprise development,
      </tspan>
      <tspan x="0" dy="45" class="main-quote-en">
        and the cornerstone of our continued success."
      </tspan>
    </text>

    <!-- Bottom Right Decorative Quote Mark -->
    <text x="600" y="150" class="quote-mark font-accent" font-size="200" text-anchor="end">”</text>

    <!-- Source Information Group - Positioned below the quote -->
    <g id="QuoteSource" transform="translate(0, 200)">
      <!-- Author name -->
      <text x="0" y="0" class="quote-source font-primary text-center">
        <tspan x="0" dy="0">—— {author}</tspan>
      </text>
      <!-- Author role/title -->
      <text x="0" y="30" class="quote-source-role font-secondary text-center">
        <tspan x="0" dy="0">公司创始人 和 CEO</tspan>
      </text>
    </g>
  </g>

  <!-- Logo Placeholder - Top Left Corner -->
  <g id="Logo" transform="translate(80, 60)">
    <!-- A simple rectangle with text acting as a logo placeholder -->
    <rect x="0" y="0" width="180" height="60" fill="#E0F2FE" rx="8" ry="8"/>
    <text x="90" y="38" font-family="'Microsoft YaHei', sans-serif" font-size="24" font-weight="700" fill="#1E40AF" text-anchor="middle">
      {logo_url}
    </text>
  </g>

  <!-- Page Number - Bottom Right Corner -->
  <text x="1840" y="1020" font-family="'Segoe UI', sans-serif" font-size="22" fill="#64748B" text-anchor="end">
    9 / 10
  </text>

</svg>