<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 样式定义 -->
    <style type="text/css">
      <![CDATA[
        /* 配色方案 */
        .bg-color { fill: #F8FAFC; }
        .primary-color-fill { fill: #1E40AF; }
        .secondary-color-fill { fill: #475569; }
        .accent-color-fill { fill: #3B82F6; }
        .text-primary-color { fill: #1E293B; }
        .text-secondary-color { fill: #64748B; }
        .text-light-color { fill: #94A3B8; }
        .card-background-color { fill: #FFFFFF; }
        .card-border-color { stroke: #BAE6FD; }
        .container-background-color { fill: #E0F2FE; }

        /* 字体系统 */
        .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
        .font-secondary { font-family: 'Source <PERSON>', 'Noto Sans CJK SC', sans-serif; }
        .font-accent { font-family: 'Times New Roman', serif; }

        /* 字体大小和字重 */
        .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
        .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
        .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; }
        .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; }
        .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
        .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; }
        .caption-text { font-size: 14px; font-weight: 400; line-height: 1.6; }

        /* 文本对齐 */
        .text-center { text-anchor: middle; }
        .text-left { text-anchor: start; }
        .text-right { text-anchor: end; }

        /* 卡片样式（用于暗示结构或未来元素） */
        .card-style {
            fill: #FFFFFF;
            stroke: #BAE6FD;
            stroke-width: 1px;
            rx: 12px; /* border-radius */
            filter: url(#cardShadow); /* 应用阴影 */
        }
      ]]>
    </style>

    <!-- 渐变定义 -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#475569" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#F8FAFC" />
      <stop offset="100%" stop-color="#E0F2FE" />
    </linearGradient>

    <!-- 阴影滤镜 -->
    <filter id="cardShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="3" flood-color="rgba(0,0,0,0.1)"/>
      <feDropShadow dx="0" dy="2" stdDeviation="2" flood-color="rgba(0,0,0,0.06)"/>
    </filter>

    <!-- 医疗图标组 (简化几何图形) -->
    <g id="medicalCrossIcon">
      <rect x="-10" y="-2" width="20" height="4" rx="2" fill="url(#accentGradient)"/>
      <rect x="-2" y="-10" width="4" height="20" rx="2" fill="url(#accentGradient)"/>
    </g>

  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

  <!-- 装饰元素 - 抽象几何形状，暗示数据流或连接 -->
  <g opacity="0.3">
    <circle cx="1700" cy="150" r="80" fill="#475569" opacity="0.1"/>
    <rect x="1650" y="200" width="150" height="50" rx="10" fill="#475569" opacity="0.1" transform="rotate(15 1650 200)"/>
    <path d="M 1700 800 C 1800 700, 1900 900, 1850 1000 L 1920 1080 L 1700 1080 Z" fill="#3B82F6" opacity="0.05"/>
    <circle cx="200" cy="900" r="120" fill="#1E40AF" opacity="0.08"/>
    <rect x="50" y="800" width="200" height="80" rx="15" fill="#1E40AF" opacity="0.08" transform="rotate(-10 50 800)"/>
  </g>

  <!-- Logo 占位符 -->
  <!-- Logo 位置: 左上角，符合边距规范 -->
  <g class="logo-area">
    <image x="80" y="60" width="200" height="60" href="{logo_url}" />
    <!-- 如果图片未加载，显示占位文本 -->
    <text x="80" y="100" class="content-title text-primary-color font-primary">
        <tspan>您的品牌Logo</tspan>
    </text>
  </g>

  <!-- 主要内容区域 - 居中显示 -->
  <g class="main-content">
    <!-- 主标题 -->
    <text x="960" y="450" class="hero-title text-primary-color font-primary text-center">
      <tspan x="960" y="450">{title}</tspan>
    </text>

    <!-- 副标题 -->
    <!-- 确保与主标题之间有足够的间距 -->
    <text x="960" y="550" class="section-title text-secondary-color font-secondary text-center">
      <tspan x="960" y="550">{subtitle}</tspan>
    </text>

    <!-- 视觉冲击力强的数字或数据亮点占位符 -->
    <!-- 强调“超大字体或数字突出核心要点”的要求 -->
    <text x="960" y="750" class="accent-color-fill font-primary text-center" style="font-size: 180px; font-weight: 900; opacity: 0.15;">
        <tspan x="960" y="750">99.9%</tspan>
    </text>
     <text x="960" y="800" class="text-secondary-color font-secondary text-center small-text">
        <tspan x="960" y="800">卓越临床成果和数据支持</tspan>
    </text>

    <!-- 装饰元素，模拟数据或医疗结构 -->
    <g transform="translate(800 300)">
        <use href="#medicalCrossIcon" transform="scale(3) translate(-10 -10)" fill="url(#accentGradient)"/>
    </g>
    <g transform="translate(1000 850)">
        <rect x="-20" y="-20" width="40" height="40" rx="8" fill="#3B82F6" opacity="0.1"/>
        <line x1="-30" y1="0" x2="30" y2="0" stroke="#3B82F6" stroke-width="2" opacity="0.2"/>
        <line x1="0" y1="-30" x2="0" y2="30" stroke="#3B82F6" stroke-width="2" opacity="0.2"/>
    </g>

    <!-- 小型文本元素，提供背景信息或品牌价值 -->
    <text x="1750" y="970" class="caption-text text-light-color font-secondary text-right">
        <tspan x="1750" y="970">© {date} {author}</tspan>
    </text>
    <text x="1750" y="1000" class="caption-text text-light-color font-secondary text-right">
        <tspan x="1750" y="1000">Leading Medical Innovation</tspan>
    </text>

  </g>

  <!-- 底部装饰波浪线/渐变线 -->
  <path d="M0 1080 C 300 1000, 600 1100, 960 1050 C 1320 1000, 1620 1100, 1920 1030 L 1920 1080 L 0 1080 Z" fill="url(#primaryGradient)" opacity="0.1"/>

</svg>