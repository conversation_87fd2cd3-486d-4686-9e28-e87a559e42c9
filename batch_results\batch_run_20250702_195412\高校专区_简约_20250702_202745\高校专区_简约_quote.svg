<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- 定义颜色和字体样式 -->
    <style type="text/css">
      /* Color Palette */
      .primary-color { fill: #3B82F6; }
      .secondary-color { fill: #7DD3FC; }
      .accent-color { fill: #BAE6FD; }
      .background-color { fill: #F8FAFC; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .card-background { fill: #FFFFFF; }
      .card-border { stroke: #BAE6FD; }
      .container-background { fill: #E0F2FE; }

      /* Font System */
      .font-inter { font-family: 'Inter', Helvetica, Arial, sans-serif; }

      /* Font Sizes and Weights */
      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; } /* bold */
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; } /* bold */
      .section-title { font-size: 36px; font-weight: 600; line-height: 1.4; } /* semibold */
      .content-title { font-size: 28px; font-weight: 500; line-height: 1.4; } /* medium */
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; } /* normal */
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; } /* normal */
      .caption-text { font-size: 14px; font-weight: 400; line-height: 1.6; } /* normal */

      /* Text alignment */
      .text-center { text-anchor: middle; }
      .text-left { text-anchor: start; }
      .text-right { text-anchor: end; }

      /* Quote Mark Style (large, subtle) */
      .quote-mark-decorative {
        font-size: 280px; /* 超大字体 */
        font-weight: 900; /* 超粗 */
        opacity: 0.15; /* 透明度制造科技感 */
        fill: #3B82F6; /* 主色 */
      }

      /* Subtle gradient for content background */
      .content-gradient-fill {
        fill: url(#mainContentGradient);
      }
    </style>

    <!-- 定义渐变 -->
    <linearGradient id="mainContentGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#E0F2FE" stop-opacity="0.5"/>
      <stop offset="100%" stop-color="#BAE6FD" stop-opacity="0.3"/>
    </linearGradient>
  </defs>

  <!-- 背景层 -->
  <rect x="0" y="0" width="1920" height="1080" class="background-color" />

  <!-- 装饰性元素 (角落的抽象形状) -->
  <!-- 左上角装饰 -->
  <path d="M0 0 H200 C200 100, 100 200, 0 200 Z" class="accent-color" opacity="0.3"/>
  <!-- 右下角装饰 -->
  <path d="M1920 1080 H1720 C1720 980, 1820 880, 1920 880 Z" class="secondary-color" opacity="0.3"/>

  <!-- 内容区域的微妙背景矩形 (具有渐变效果) -->
  <!-- 位置计算: 围绕中心文本区域，预留足够间距 -->
  <rect x="360" y="320" width="1200" height="440" class="content-gradient-fill" rx="40" ry="40" />

  <!-- 装饰性引号符号 -->
  <text x="180" y="320" class="font-inter quote-mark-decorative">“</text>
  <text x="1740" y="800" class="font-inter quote-mark-decorative text-right">”</text>

  <!-- 主要引用内容区域 -->
  <g transform="translate(960, 540)"> <!-- 将组的中心点移动到画布中心 -->
    <!-- 引用内容 -->
    <text x="0" y="0" class="main-title text-primary font-inter text-center">
      <tspan x="0" y="0">求知若饥，虚心若愚。</tspan>
      <!-- 中文大字体粗体，英文小字点缀 -->
      <tspan x="0" y="80" class="section-title text-secondary">Stay hungry, stay foolish.</tspan>
    </text>

    <!-- 来源信息 -->
    <text x="0" y="180" class="content-title text-secondary font-inter text-center">
      <tspan x="0" y="180">— {author}, {date}</tspan>
      <!-- 确保行间距足够，至少30px -->
      <tspan x="0" y="220" class="small-text text-light">From Academic Conference "{title}"</tspan>
    </text>
  </g>

  <!-- 底部信息 (页码和Logo占位符) -->
  <g>
    <text x="80" y="1020" class="small-text text-light font-inter text-left">
      9/10
    </text>
    <!-- Logo占位符 -->
    <rect x="1700" y="1000" width="140" height="40" class="primary-color" opacity="0.1"/>
    <text x="1770" y="1025" class="small-text text-primary font-inter text-center">
      {logo_url}
    </text>
  </g>
</svg>