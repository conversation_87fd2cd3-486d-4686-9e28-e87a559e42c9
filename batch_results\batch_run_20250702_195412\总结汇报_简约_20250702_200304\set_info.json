{"set_name": "总结汇报_简约_20250702_200304", "scenario": "总结汇报", "style": "简约", "created_at": "2025-07-02T20:03:04.057428", "template_count": 10, "templates": [{"template_id": "总结汇报_简约_cover", "type": "封面页", "filename": "总结汇报_简约_cover.svg", "page_number": 1}, {"template_id": "总结汇报_简约_agenda", "type": "目录页", "filename": "总结汇报_简约_agenda.svg", "page_number": 2}, {"template_id": "总结汇报_简约_section_divider", "type": "章节分隔页", "filename": "总结汇报_简约_section_divider.svg", "page_number": 3}, {"template_id": "总结汇报_简约_title_content", "type": "标题内容页", "filename": "总结汇报_简约_title_content.svg", "page_number": 4}, {"template_id": "总结汇报_简约_image_text", "type": "图文混排页", "filename": "总结汇报_简约_image_text.svg", "page_number": 5}, {"template_id": "总结汇报_简约_data_display", "type": "数据展示页", "filename": "总结汇报_简约_data_display.svg", "page_number": 6}, {"template_id": "总结汇报_简约_comparison", "type": "对比分析页", "filename": "总结汇报_简约_comparison.svg", "page_number": 7}, {"template_id": "总结汇报_简约_timeline", "type": "时间线页", "filename": "总结汇报_简约_timeline.svg", "page_number": 8}, {"template_id": "总结汇报_简约_quote", "type": "引用页", "filename": "总结汇报_简约_quote.svg", "page_number": 9}, {"template_id": "总结汇报_简约_conclusion", "type": "总结页", "filename": "总结汇报_简约_conclusion.svg", "page_number": 10}], "combination_config": {"scenario": {"scenario_type": "总结汇报", "display_name": "总结汇报", "description": "项目或工作阶段性总结报告", "visual_characteristics": {"emphasis_on": "逻辑清晰、重点突出", "layout_style": "结构化布局", "decorative_elements": "图表、要点标记、流程图"}, "content_focus": ["执行情况", "问题分析", "改进建议"], "target_audience": "管理层、团队", "tone": "formal"}, "style": {"style_type": "简约", "display_name": "简约", "description": "简洁干净，注重留白和清晰度", "design_principles": {"layout": "大量留白、网格化布局", "elements": "几何形状、简单线条", "emphasis": "功能性、可读性"}, "visual_elements": {"shapes": "圆形、方形、简单几何", "lines": "细线条、清晰边界", "decorations": "最少装饰、点缀性元素"}, "typography": {"font_style": "无衬线字体", "weight": "中等粗细", "spacing": "宽松间距"}}, "colors": {"primary": "#3B82F6", "secondary": "#7DD3FC", "accent": "#BAE6FD", "background": "#F8FAFC", "text_primary": "#1E293B", "text_secondary": "#64748B"}, "fusion_rules": {"layout_priority": "scenario", "color_adaptation": "balanced", "typography_blend": "harmonious", "visual_emphasis": []}, "aesthetic_enhancements": {"typography_refinements": {"font_pairing": "complementary", "hierarchy_enhancement": true, "readability_optimization": true}, "layout_improvements": {"golden_ratio_application": true, "visual_balance_optimization": true, "spacing_harmonization": true}, "color_harmony": {"contrast_optimization": true, "accessibility_compliance": true, "emotional_resonance": true}, "visual_elements": {"icon_style_consistency": true, "decorative_element_coordination": true, "brand_element_integration": true}}}, "design_specification": {"theme_selection": {"primary_style": "简约风格", "scenario_adaptation": "总结汇报场景优化", "visual_theme": "黑底特斯拉红高亮与蓝色系融合的现代简约风格", "design_philosophy": "以极简主义为核心，通过Bento Grid布局、超大字体对比、中英文混排及线条图形，为总结汇报提供清晰、高效且视觉冲击力强的专业展示。", "fusion_strategy": "scenario优先的场景风格融合"}, "color_palette": {"primary_color": "#3B82F6", "secondary_color": "#7DD3FC", "accent_color": "#BAE6FD", "background_color": "#F8FAFC", "text_primary": "#1E293B", "text_secondary": "#64748B", "text_light": "#94A3B8", "success_color": "#10B981", "warning_color": "#F59E0B", "error_color": "#EF4444", "info_color": "#BAE6FD", "gradient_primary": "linear-gradient(135deg, #3B82F6, #7DD3FC)", "gradient_accent": "linear-gradient(45deg, #BAE6FD, #3B82F6)", "card_background": "#FFFFFF", "card_border": "#BAE6FD", "container_background": "#E0F2FE", "hover_color": "#7DD3FC", "active_color": "#3B82F6", "disabled_color": "#64748B"}, "layout_principles": {"canvas_size": {"width": 1920, "height": 1080}, "golden_ratio": 1.618, "grid_system": {"columns": 12, "gutter": 24, "margin": 80}, "spacing": {"xs": 6, "sm": 12, "md": 24, "lg": 36, "xl": 48, "2xl": 72, "3xl": 96, "4xl": 144}, "page_margins": {"horizontal": 80, "vertical": 60, "content_max_width": 1760}, "content_spacing": {"module_gap": 32, "section_gap": 48, "element_gap": 16, "text_gap": 12}, "visual_hierarchy": {"z_index_background": 0, "z_index_content": 10, "z_index_overlay": 20, "z_index_modal": 30}, "alignment": {"text_align": "left", "content_align": "center", "title_align": "center"}}, "typography_system": {"primary_font": "Inter, Helvetica, Arial, sans-serif", "secondary_font": "SF Pro Display, system-ui, sans-serif", "accent_font": "<PERSON><PERSON><PERSON>, sans-serif", "font_sizes": {"hero_title": 72, "main_title": 56, "section_title": 36, "content_title": 28, "body_text": 22, "small_text": 16, "caption": 14}, "font_weights": {"thin": 100, "light": 300, "normal": 400, "medium": 500, "semibold": 600, "bold": 700, "black": 900}, "line_heights": {"tight": 1.1, "normal": 1.4, "relaxed": 1.6, "loose": 1.8}, "letter_spacing": {"tight": "-0.025em", "normal": "0em", "wide": "0.025em", "wider": "0.05em"}}, "visual_elements": {"card_style": {"background": "#FFFFFF", "border_radius": 0, "border": "1px solid #BAE6FD", "shadow": "none", "hover_shadow": "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"}, "decorative_elements": ["简单线条", "几何形状"], "gradients": {"primary_gradient": "linear-gradient(135deg, #4A86E8, #3B82F6)", "accent_gradient": "linear-gradient(45deg, #0EA5E9, #06B6D4)", "background_gradient": "linear-gradient(180deg, #F8FAFC, #E0F2FE)", "text_gradient": "linear-gradient(135deg, #1E3A8A, #1E40AF)"}, "image_effects": {"border_radius": 8, "shadow_style": "0 4px 8px rgba(0, 0, 0, 0.1)", "overlay_style": "linear-gradient(135deg, #4A86E820, #3B82F620)", "hover_transform": "scale(1.02)", "filter_effects": "brightness(1.05) contrast(1.05)"}, "logo_effects": {"positioning": "左上角或页面顶部中央", "size_guidelines": "最大高度80px，宽度自适应", "background_treatment": "透明背景或浅色背景", "shadow": "subtle drop shadow", "hover_effect": "slight glow"}, "icon_system": {"style": "outline", "stroke_width": 2, "size_sm": 16, "size_md": 24, "size_lg": 32, "color": "#4A86E8"}, "dividers": {"horizontal_line": "1px solid #BAE6FD", "vertical_line": "1px solid #BAE6FD", "decorative_divider": "波浪线或装饰性分割", "gradient_divider": "linear-gradient(90deg, transparent, #7DD3FC, transparent)"}}, "template_consistency": {"scenario_coherence": "所有模板围绕'总结汇报'的核心目标进行设计，确保内容流线清晰、重点突出，有效支撑管理层决策。", "style_unity": "严格统一的简约设计语言，包括配色、字体、间距、卡片样式和装饰元素的运用，确保10张模板视觉风格高度一致。", "color_harmony": "黑底特斯拉红高亮配色体系贯穿始终，辅以蓝色系的巧妙运用，形成独特且专业的视觉标识。", "visual_rhythm": "通过Bento Grid的动态布局、超大字体的冲击力以及留白的运用，创建有节奏感的视觉流，引导用户视线。", "brand_consistency": "强化特斯拉红作为品牌高亮色，确保所有视觉元素都服务于提升汇报的专业性和现代感，与企业形象保持一致。"}, "created_at": "2025-07-02T19:59:43.140157", "scenario": "总结汇报", "style": "简约", "canvas_size": {"width": 1920, "height": 1080}, "raw_response": "```json\n{\n    \"theme_selection\": {\n        \"primary_style\": \"简约风格\",\n        \"scenario_adaptation\": \"总结汇报场景优化\",\n        \"visual_theme\": \"黑底特斯拉红高亮与蓝色系融合的现代简约风格\",\n        \"design_philosophy\": \"以极简主义为核心，通过Bento Grid布局、超大字体对比、中英文混排及线条图形，为总结汇报提供清晰、高效且视觉冲击力强的专业展示。\",\n        \"fusion_strategy\": \"scenario优先的场景风格融合\"\n    },\n    \"color_palette\": {\n        \"background_color\": \"#000000\",\n        \"highlight_color\": \"#E31937\",\n        \"highlight_transparent\": \"rgba(227, 25, 55, 0.7)\",\n        \"highlight_gradient_start\": \"#E31937\",\n        \"highlight_gradient_end\": \"rgba(227, 25, 55, 0.3)\",\n        \"primary_color\": \"#3B82F6\",\n        \"secondary_color\": \"#7DD3FC\",\n        \"text_primary\": \"#FFFFFF\",\n        \"text_secondary\": \"#86868B\",\n        \"text_subtle\": \"#F5F5F7\",\n        \"success_color\": \"#10B981\",\n        \"warning_color\": \"#F59E0B\",\n        \"error_color\": \"#EF4444\",\n        \"card_background\": \"#0D0D0D\",\n        \"card_border\": \"#1D1D1F\",\n        \"hover_color\": \"#E31937\"\n    },\n    \"layout_principles\": {\n        \"canvas_size\": {\"width\": 1920, \"height\": 1080},\n        \"golden_ratio_application\": true,\n        \"grid_system\": {\"columns\": 12, \"gutter\": 24, \"margin\": 80},\n        \"page_margins\": {\"horizontal\": 80, \"vertical\": 60},\n        \"content_spacing\": {\"module_gap\": 32, \"section_gap\": 48, \"element_gap\": 16},\n        \"visual_hierarchy\": \"通过Bento Grid布局、超大字体、色彩对比和留白，构建清晰、动态的视觉层次，突出总结报告的核心洞察。\",\n        \"alignment_system\": \"严格遵循网格系统，采用左对齐为主，确保所有元素的清晰对齐与视觉秩序。\"\n    },\n    \"typography_system\": {\n        \"primary_font\": \"系统UI字体栈 (如 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans SC', 'PingFang SC', 'sans-serif')\",\n        \"font_sizes\": {\n            \"hero_title\": 120,\n            \"main_title\": 72,\n            \"section_title\": 48,\n            \"content_title\": 32,\n            \"body_text\": 24,\n            \"small_text\": 16,\n            \"accent_number\": 180\n        },\n        \"font_weights\": {\n            \"title\": \"bold\",\n            \"content\": \"normal\",\n            \"emphasis\": \"600\",\n            \"chinese\": \"bold\",\n            \"english\": \"300\"\n        },\n        \"line_heights\": {\n            \"title\": 1.2,\n            \"content\": 2.0,\n            \"dense\": 1.8,\n            \"chinese_text\": 2.2\n        },\n        \"mixed_typography\": {\n            \"chinese_style\": \"大号粗体，作为主要信息载体，具有强烈的视觉冲击力。\",\n            \"english_style\": \"小号细体，作为辅助说明、专业术语或点缀，增强国际化和设计感。\",\n            \"number_style\": \"超大号突出，结合高亮色，成为页面的核心视觉焦点。\"\n        },\n        \"readability_optimization\": \"通过高对比度文本、充足的行间距和字号，确保在深色背景下，管理层和团队能高效阅读关键信息，尤其关注中英文混排的易读性。\"\n    },\n    \"visual_elements\": {\n        \"scenario_specific_elements\": \"进度条、关键指标卡片、趋势图、问题分析图示、改进建议列表、里程碑标记等，均以简洁线条和高亮色呈现。\",\n        \"style_characteristics\": \"大量留白、清晰的网格布局、极简图形、高对比度配色、以及超大字体带来的视觉冲击力。\",\n        \"bento_grid_layout\": \"采用类似Apple官网的非对称网格布局，通过不同尺寸的卡片组合，创造动态且信息丰富的视觉体验，卡片间距严格遵循网格系统。\",\n        \"black_red_theme\": \"纯黑色(#000000)作为主背景，特斯拉红色(#E31937)作为核心高亮色，用于强调、图标、进度指示和装饰性线条，配合白色和浅灰色文本，形成强烈对比和现代感。\",\n        \"oversized_typography\": \"关键数字、百分比、核心结论等采用超大字号（如120px+），结合特斯拉红色高亮或白色粗体，成为页面的第一视觉焦点。\",\n        \"decorative_elements\": [\n            \"简洁线条图形元素: 用于图表、进度指示、背景纹理，以特斯拉红色或辅助蓝色系线条勾勒，低透明度。\",\n            \"特斯拉红色透明度渐变元素: 仅使用特斯拉红色自身进行透明度渐变（从#E31937到rgba(227, 25, 55, 0.3)），用于背景叠加、卡片边框或强调区域，创造科技感和深度。\",\n            \"中英文混排排版元素: 遵循大中文/小英文原则，通过字号、字重和颜色差异，形成独特的视觉节奏。\",\n            \"符合简约风格的装饰元素: 如细致的圆角、微妙的阴影、干净的图标，避免复杂或冗余的视觉噪音。\",\n            \"适合总结汇报场景的装饰元素: 如象征增长的箭头、连接点、数据流线等，以抽象形式融入设计。\",\n            \"蓝色系渐变元素: 巧妙运用主蓝色系（如从#3B82F6到rgba(59, 130, 246, 0.3)）作为卡片内部的微妙背景纹理或边缘装饰，增强视觉层次感而不干扰黑红主调。\"\n        ],\n        \"card_style\": {\n            \"background\": \"#0D0D0D\",\n            \"border_radius\": \"16px - 24px (根据卡片尺寸动态调整，保持现代感)\",\n            \"shadow\": \"微妙的内阴影或柔和的外阴影，增加层次感而不显突兀，如 `<filter><feDropShadow dx='0' dy='0' stdDeviation='8' flood-color='#000000' flood-opacity='0.5'/></filter>`。\",\n            \"border\": \"1.5px - 2.5px 的特斯拉红色渐变边框 (`url(#cardBorderGradient)`)，强调卡片结构和重要性。\"\n        },\n        \"image_integration\": {\n            \"border_radius\": 0,\n            \"shadow_style\": \"无阴影或极简阴影 (仅在图片需要从背景中轻微抬起时使用，如 `stdDeviation='4'`)。\",\n            \"overlay_style\": \"可选择性地应用特斯拉红色或主蓝色系半透明遮罩 (opacity 0.1-0.3)，以统一视觉风格或突出文字内容。\",\n            \"bento_grid_placement\": \"图片作为Bento Grid中的一个模块，尺寸和位置严格遵循网格系统，确保与其他内容模块的和谐统一。\"\n        },\n        \"logo_treatment\": {\n            \"positioning\": \"通常位于页面顶部左侧或右侧，或在封面页居中，确保显眼但不喧宾夺主。\",\n            \"size_guidelines\": \"尺寸适中，根据简约风格避免过大，在1920x1080画布中建议高度不超过80px。\",\n            \"integration_style\": \"白色或浅灰色单色Logo，或特斯拉红色填充，与黑底红高亮主题高度融合，保持品牌识别度。\",\n            \"animation_hint\": \"在滚动或切换页面时，Logo可有微小的淡入淡出或缩放动效，模仿Apple官网的流畅体验。\"\n        }\n    },\n    \"template_consistency\": {\n        \"scenario_coherence\": \"所有模板围绕'总结汇报'的核心目标进行设计，确保内容流线清晰、重点突出，有效支撑管理层决策。\",\n        \"style_unity\": \"严格统一的简约设计语言，包括配色、字体、间距、卡片样式和装饰元素的运用，确保10张模板视觉风格高度一致。\",\n        \"color_harmony\": \"黑底特斯拉红高亮配色体系贯穿始终，辅以蓝色系的巧妙运用，形成独特且专业的视觉标识。\",\n        \"visual_rhythm\": \"通过Bento Grid的动态布局、超大字体的冲击力以及留白的运用，创建有节奏感的视觉流，引导用户视线。\",\n        \"brand_consistency\": \"强化特斯拉红作为品牌高亮色，确保所有视觉元素都服务于提升汇报的专业性和现代感，与企业形象保持一致。\"\n    }\n}\n```"}}