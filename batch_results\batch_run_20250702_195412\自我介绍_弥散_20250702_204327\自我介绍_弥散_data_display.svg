<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 背景渐变 (弥散风格) -->
    <linearGradient id="backgroundGradient" x1="960" y1="0" x2="960" y2="1080" gradientUnits="userSpaceOnUse">
      <stop stop-color="#F8FAFC"/>
      <stop offset="1" stop-color="#E0F2FE"/>
    </linearGradient>

    <!-- 主色渐变 用于强调元素 -->
    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#3B82F6"/>
      <stop offset="1" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- 强调色渐变 用于高亮元素 -->
    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#06B6D4"/>
      <stop offset="1" stop-color="#3B82F6"/>
    </linearGradient>

    <!-- 文本渐变 (可选，用于标题) -->
    <linearGradient id="textGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#1E3A8A"/>
      <stop offset="1" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- 柔和发光滤镜 -->
    <filter id="softGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="30" result="blur"/>
      <feFlood flood-color="#3B82F6" flood-opacity="0.3" result="color"/>
      <feComposite in="color" in2="blur" operator="in" result="glow"/>
      <feMerge>
        <feMergeNode in="glow"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 卡片阴影滤镜 -->
    <filter id="cardShadow" x="-10%" y="-10%" width="120%" height="120%">
      <feDropShadow dx="0" dy="4" stdDeviation="6" flood-color="rgba(0, 0, 0, 0.1)"/>
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="rgba(0, 0, 0, 0.06)"/>
    </filter>
  </defs>

  <style>
    /* 字体定义 */
    .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
    .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
    .font-accent { font-family: 'Times New Roman', serif; }

    /* 字体大小 */
    .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
    .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
    .section-title { font-size: 36px; font-weight: 600; line-height: 1.4; }
    .content-title { font-size: 28px; font-weight: 500; line-height: 1.4; }
    .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
    .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; }
    .caption-text { font-size: 14px; font-weight: 400; line-height: 1.6; }

    /* 颜色 */
    .color-primary { fill: #3B82F6; }
    .color-secondary { fill: #1E40AF; }
    .color-accent { fill: #06B6D4; }
    .color-text-primary { fill: #1E293B; }
    .color-text-secondary { fill: #64748B; }
    .color-text-light { fill: #94A3B8; }
    .color-card-background { fill: #FFFFFF; }
    .stroke-card-border { stroke: #BAE6FD; }
    .stroke-accent { stroke: #06B6D4; }
    .stroke-primary { stroke: #3B82F6; }
    .stroke-secondary { stroke: #1E40AF; }

    /* 卡片样式 */
    .card-base {
      fill: #FFFFFF;
      stroke: #BAE6FD;
      stroke-width: 1px;
      filter: url(#cardShadow);
      rx: 12px; /* 圆角半径 */
      ry: 12px;
    }

    /* 弥散背景元素 */
    .diffuse-shape-1 { fill: url(#primaryGradient); opacity: 0.15; filter: url(#softGlow); }
    .diffuse-shape-2 { fill: url(#accentGradient); opacity: 0.1; filter: url(#softGlow); }

    /* 通用布局 */
    .text-align-left { text-anchor: start; }
    .text-align-center { text-anchor: middle; }

    /* 图表特定样式 */
    .chart-bar { fill: url(#primaryGradient); }
    .chart-line { stroke: url(#accentGradient); stroke-width: 4; fill: none; }
    .chart-donut-segment-1 { stroke: url(#primaryGradient); stroke-width: 40; fill: none; }
    .chart-donut-segment-2 { stroke: url(#accentGradient); stroke-width: 40; fill: none; }
    .chart-label { fill: #1E293B; font-size: 18px; font-weight: 500; }
    .chart-value { fill: #1E40AF; font-size: 24px; font-weight: 700; }
    .chart-axis-label { fill: #64748B; font-size: 16px; }
  </style>

  <!-- 背景层 -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

  <!-- 弥散装饰性形状 (柔和，流动) -->
  <circle cx="1700" cy="150" r="100" class="diffuse-shape-1"/>
  <circle cx="200" cy="900" r="120" class="diffuse-shape-2"/>
  <rect x="1500" y="850" width="300" height="150" rx="75" ry="75" class="diffuse-shape-1" transform="rotate(15 1500 850)"/>
  <ellipse cx="400" cy="100" rx="150" ry="80" class="diffuse-shape-2" transform="rotate(-30 400 100)"/>

  <!-- 主要内容区域 -->
  <g transform="translate(80 60)">
    <!-- 头部: 标题和副标题 -->
    <text x="0" y="56" class="main-title color-text-primary font-primary text-align-left">
      <tspan x="80" y="100">{title}</tspan>
    </text>
    <text x="0" y="120" class="body-text color-text-secondary font-primary text-align-left">
      <tspan x="80" y="150">{subtitle}</tspan>
    </text>

    <!-- Bento Grid 布局用于数据可视化 -->
    <!-- 图表区域1: 柱状图示例 (占据左上方区域) -->
    <rect x="80" y="220" width="900" height="400" class="card-base"/>
    <text x="120" y="260" class="content-title color-text-primary font-primary text-align-left">
      <tspan>项目完成度 Project Completion</tspan>
    </text>

    <!-- 柱状图元素 -->
    <g transform="translate(160 320)">
      <!-- 柱状图条 1 -->
      <rect x="0" y="0" width="80" height="200" rx="8" ry="8" class="chart-bar"/>
      <text x="40" y="230" class="chart-axis-label font-secondary text-align-center">
        <tspan>项目A Project A</tspan>
      </text>
      <text x="40" y="-10" class="chart-value font-primary text-align-center">
        <tspan>85%</tspan>
      </text>

      <!-- 柱状图条 2 -->
      <rect x="150" y="60" width="80" height="140" rx="8" ry="8" class="chart-bar"/>
      <text x="190" y="230" class="chart-axis-label font-secondary text-align-center">
        <tspan>项目B Project B</tspan>
      </text>
      <text x="190" y="50" class="chart-value font-primary text-align-center">
        <tspan>70%</tspan>
      </text>

      <!-- 柱状图条 3 -->
      <rect x="300" y="20" width="80" height="180" rx="8" ry="8" class="chart-bar"/>
      <text x="340" y="230" class="chart-axis-label font-secondary text-align-center">
        <tspan>项目C Project C</tspan>
      </text>
      <text x="340" y="10" class="chart-value font-primary text-align-center">
        <tspan>90%</tspan>
      </text>

      <!-- 柱状图条 4 -->
      <rect x="450" y="100" width="80" height="100" rx="8" ry="8" class="chart-bar"/>
      <text x="490" y="230" class="chart-axis-label font-secondary text-align-center">
        <tspan>项目D Project D</tspan>
      </text>
      <text x="490" y="90" class="chart-value font-primary text-align-center">
        <tspan>50%</tspan>
      </text>
    </g>

    <!-- 图表区域2: 折线图示例 (占据右上方区域) -->
    <rect x="1000" y="220" width="800" height="400" class="card-base"/>
    <text x="1040" y="260" class="content-title color-text-primary font-primary text-align-left">
      <tspan>技能增长趋势 Skill Growth Trend</tspan>
    </text>

    <!-- 折线图元素 -->
    <g transform="translate(1080 340)">
      <polyline points="0,150 150,100 300,120 450,80 600,50 750,20" class="chart-line"/>
      <!-- 数据点和标签 -->
      <circle cx="0" cy="150" r="6" fill="#06B6D4"/>
      <text x="0" y="175" class="chart-axis-label font-secondary text-align-center">
        <tspan>2020</tspan>
      </text>
      <text x="0" y="130" class="chart-value font-primary text-align-center">
        <tspan>入门</tspan>
      </text>

      <circle cx="150" cy="100" r="6" fill="#06B6D4"/>
      <text x="150" y="125" class="chart-value font-primary text-align-center">
        <tspan>熟练</tspan>
      </text>

      <circle cx="300" cy="120" r="6" fill="#06B6D4"/>
      <text x="300" y="145" class="chart-value font-primary text-align-center">
        <tspan>提升</tspan>
      </text>

      <circle cx="450" cy="80" r="6" fill="#06B6D4"/>
      <text x="450" y="105" class="chart-value font-primary text-align-center">
        <tspan>优化</tspan>
      </text>

      <circle cx="600" cy="50" r="6" fill="#06B6D4"/>
      <text x="600" y="75" class="chart-value font-primary text-align-center">
        <tspan>精通</tspan>
      </text>

      <circle cx="750" cy="20" r="6" fill="#06B6D4"/>
      <text x="750" y="45" class="chart-value font-primary text-align-center">
        <tspan>专家</tspan>
      </text>
      <text x="750" y="175" class="chart-axis-label font-secondary text-align-center">
        <tspan>2024</tspan>
      </text>
    </g>

    <!-- 数据卡片1: 关键指标 (左中区域) -->
    <rect x="80" y="660" width="440" height="340" class="card-base"/>
    <text x="120" y="700" class="content-title color-text-primary font-primary text-align-left">
      <tspan>核心成就 Core Achievements</tspan>
    </text>
    <text x="120" y="770" class="hero-title color-text-primary font-primary text-align-left">
      <tspan>和#x2B50; 95%</tspan>
    </text>
    <text x="120" y="830" class="body-text color-text-secondary font-primary text-align-left">
      <tspan>成功率 Success Rate</tspan>
      <tspan x="120" dy="30">关键项目交付 Key Project Delivery</tspan>
    </text>
    <!-- 装饰元素: 简单图标 -->
    <circle cx="400" cy="900" r="50" fill="#3B82F6" opacity="0.1"/>
    <path d="M400 700 L420 700 L420 720 L400 720 Z M400 700 C400 680, 420 680, 420 700 Z M400 720 C400 740, 420 740, 420 720 Z" fill="#06B6D4" opacity="0.3"/>

    <!-- 数据卡片2: 技能分布 (右中区域) -->
    <rect x="540" y="660" width="440" height="340" class="card-base"/>
    <text x="580" y="700" class="content-title color-text-primary font-primary text-align-left">
      <tspan>技能分布 Skill Distribution</tspan>
    </text>

    <!-- 甜甜圈图元素 -->
    <g transform="translate(760 830)">
      <!-- 背景圆圈 -->
      <circle r="60" fill="none" stroke="#E0F2FE" stroke-width="40"/>
      <!-- 段1: 70% (主色) -->
      <circle r="60" cx="0" cy="0" fill="none" class="chart-donut-segment-1"
              stroke-dasharray="263.89 113.09" stroke-dashoffset="0"
              transform="rotate(-90 0 0)"/>
      <!-- 段2: 30% (强调色) -->
      <circle r="60" cx="0" cy="0" fill="none" class="chart-donut-segment-2"
              stroke-dasharray="113.09 263.89" stroke-dashoffset="-263.89"
              transform="rotate(-90 0 0)"/>

      <text x="0" y="10" class="hero-title color-text-primary font-primary text-align-center" font-size="48px">
        <tspan>70%</tspan>
      </text>
      <text x="0" y="45" class="small-text color-text-secondary font-primary text-align-center">
        <tspan>核心 Core</tspan>
      </text>
    </g>
    <text x="580" y="940" class="small-text color-text-secondary font-primary text-align-left">
      <tspan>编程 Programming: 70%</tspan>
      <tspan x="580" dy="25">设计 Design: 20%</tspan>
      <tspan x="580" dy="25">其他 Others: 10%</tspan>
    </text>

    <!-- 大型统计卡片: 整体摘要 (右侧列) -->
    <rect x="1000" y="660" width="800" height="340" class="card-base"/>
    <text x="1040" y="700" class="section-title color-text-primary font-primary text-align-left">
      <tspan>个人亮点 Personal Highlights</tspan>
    </text>

    <g transform="translate(1040 760)">
      <!-- 亮点 1 -->
      <text x="0" y="0" class="hero-title color-text-primary font-primary text-align-left" font-size="64px">
        <tspan>5+</tspan>
      </text>
      <text x="0" y="60" class="body-text color-text-secondary font-primary text-align-left">
        <tspan>年经验 Years of Experience</tspan>
      </text>

      <!-- 亮点 2 -->
      <text x="300" y="0" class="hero-title color-text-primary font-primary text-align-left" font-size="64px">
        <tspan>20+</tspan>
      </text>
      <text x="300" y="60" class="body-text color-text-secondary font-primary text-align-left">
        <tspan>项目 Project Count</tspan>
      </text>

      <!-- 亮点 3 -->
      <text x="0" y="160" class="hero-title color-text-primary font-primary text-align-left" font-size="64px">
        <tspan>90%和#x2B;</tspan>
      </text>
      <text x="0" y="220" class="body-text color-text-secondary font-primary text-align-left">
        <tspan>用户满意度 User Satisfaction</tspan>
      </text>

      <!-- 亮点 4 -->
      <text x="300" y="160" class="hero-title color-text-primary font-primary text-align-left" font-size="64px">
        <tspan>4.8/5</tspan>
      </text>
      <text x="300" y="220" class="body-text color-text-secondary font-primary text-align-left">
        <tspan>技能评分 Skill Rating</tspan>
      </text>
    </g>

    <!-- 通用内容/描述占位符 -->
    <text x="80" y="1000" class="small-text color-text-light font-secondary text-align-left">
      <tspan>{content}</tspan>
    </text>

  </g>

  <!-- Logo (左上角) -->
  <g transform="translate(80 40)">
    <!-- Logo 占位符 -->
    <rect x="0" y="0" width="120" height="40" fill="#3B82F6" rx="8" ry="8" opacity="0.1"/>
    <text x="60" y="28" class="small-text font-primary color-text-primary text-align-center" font-size="20px">
      <tspan>My Logo</tspan>
    </text>
    <!-- 实际Logo将是一个 <image> 标签 -->
    <!-- <image href="{logo_url}" x="0" y="0" width="120" height="40" preserveAspectRatio="xMidYMid meet" /> -->
  </g>

</svg>