<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Define colors as CSS variables -->
    <style type="text/css">
      <![CDATA[
      :root {
        --primary-color: #3B82F6;
        --secondary-color: #7DD3FC;
        --accent-color: #BAE6FD;
        --background-color: #F8FAFC;
        --text-primary: #1E293B;
        --text-secondary: #64748B;
        --text-light: #94A3B8;
        --card-background: #FFFFFF;
        --card-border: #BAE6FD;
        --container-background: #E0F2FE;
      }

      /* Font definitions */
      .font-primary { font-family: 'Inter', 'Helvetica', 'Arial', sans-serif; }
      .font-secondary { font-family: 'SF Pro Display', system-ui, sans-serif; }
      .font-accent { font-family: 'Poppins', sans-serif; }

      /* Text styles */
      .hero-title {
        font-size: 72px;
        font-weight: 700; /* bold */
        fill: var(--text-primary);
        line-height: 1.1;
      }
      .main-title {
        font-size: 56px;
        font-weight: 700;
        fill: var(--text-primary);
        line-height: 1.1;
      }
      .section-title {
        font-size: 36px;
        font-weight: 600; /* semibold */
        fill: var(--text-primary);
        line-height: 1.4;
      }
      .content-title {
        font-size: 28px;
        font-weight: 500; /* medium */
        fill: var(--text-primary);
        line-height: 1.4;
      }
      .body-text {
        font-size: 22px;
        font-weight: 400; /* normal */
        fill: var(--text-secondary);
        line-height: 1.6;
      }
      .small-text {
        font-size: 16px;
        font-weight: 400;
        fill: var(--text-secondary);
        line-height: 1.6;
      }
      .caption {
        font-size: 14px;
        font-weight: 400;
        fill: var(--text-light);
        line-height: 1.6;
      }

      /* Specific element styles */
      .background-fill { fill: var(--background-color); }
      .container-fill { fill: var(--container-background); }
      .card-fill { fill: var(--card-background); }
      .card-border-stroke { stroke: var(--card-border); stroke-width: 1px; }
      .primary-fill { fill: var(--primary-color); }
      .secondary-fill { fill: var(--secondary-color); }
      .accent-fill { fill: var(--accent-color); }

      .call-to-action-button {
        fill: var(--primary-color);
        rx: 8; /* slight rounded corners for button aesthetic */
      }
      .call-to-action-text {
        fill: var(--card-background); /* White text on primary button */
        font-size: 24px;
        font-weight: 600;
        text-anchor: middle;
      }

      .line-divider {
        stroke: var(--accent-color);
        stroke-width: 1;
      }

      .icon-stroke {
        stroke: var(--primary-color);
        stroke-width: 2;
        fill: none;
      }
      ]]>
    </style>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#7DD3FC"/>
    </linearGradient>

    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#BAE6FD"/>
      <stop offset="100%" stop-color="#3B82F6"/>
    </linearGradient>

    <!-- Mask for super large number effect (page number) -->
    <mask id="text-mask">
      <rect x="0" y="0" width="1920" height="1080" fill="white"/>
      <text x="960" y="540" text-anchor="middle" class="font-primary" font-size="250" font-weight="900" fill="black" opacity="0.1">10</text>
    </mask>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="background-fill" />

  <!-- Decorative elements (subtle geometric shapes, aligned with minimalist blue theme) -->
  <rect x="100" y="100" width="300" height="200" class="container-fill" rx="10" ry="10" opacity="0.3"/>
  <circle cx="1700" cy="900" r="150" class="secondary-fill" opacity="0.1"/>
  <line x1="1500" y1="50" x2="1800" y2="350" class="line-divider" stroke-dasharray="5 5"/>

  <!-- Page Number overlay (super large, subtle background element) -->
  <g mask="url(#text-mask)">
    <rect x="0" y="0" width="1920" height="1080" fill="url(#primaryGradient)" opacity="0.05"/>
  </g>

  <!-- Main Content Area -->
  <g class="font-primary">
    <!-- Header: Title and Subtitle -->
    <text x="960" y="120" text-anchor="middle" class="main-title">
      {title}
    </text>
    <text x="960" y="180" text-anchor="middle" class="body-text">
      {subtitle}
    </text>

    <!-- Bento Grid Style Layout for Key Sections -->
    <!-- Section 1: Summary Points -->
    <rect x="100" y="250" width="840" height="380" class="card-fill card-border-stroke" />
    <text x="140" y="300" class="section-title">
      主要结论
    </text>
    <text x="140" y="360" class="body-text">
      <tspan x="140" dy="0em">· {content} (研究方法创新和数据分析严谨性)</tspan>
      <tspan x="140" dy="35px">· {content} (理论模型验证和实践应用潜力)</tspan>
      <tspan x="140" dy="35px">· {content} (跨学科融合和未来研究方向展望)</tspan>
      <tspan x="140" dy="35px">· {content} (学术贡献和行业影响力)</tspan>
    </text>

    <!-- Section 2: Action Recommendations -->
    <rect x="980" y="250" width="840" height="380" class="card-fill card-border-stroke" />
    <text x="1020" y="300" class="section-title">
      行动建议
    </text>
    <text x="1020" y="360" class="body-text">
      <tspan x="1020" dy="0em">· {content} (深化合作，拓展研究广度)</tspan>
      <tspan x="1020" dy="35px">· {content} (成果转化，推动实际应用)</tspan>
      <tspan x="1020" dy="35px">· {content} (人才培养，构建高水平团队)</tspan>
      <tspan x="1020" dy="35px">· {content} (国际交流，提升全球影响力)</tspan>
    </text>

    <!-- Section 3: Key Takeaway / Call to Action -->
    <rect x="100" y="660" width="840" height="320" class="card-fill card-border-stroke" />
    <text x="140" y="710" class="section-title">
      核心启示
    </text>
    <text x="140" y="770" class="body-text">
      <tspan x="140" dy="0em">本研究强调了创新思维和跨领域协作</tspan>
      <tspan x="140" dy="35px">的重要性，为未来学术发展奠定坚实基础。</tspan>
    </text>
    <!-- Call to Action Button Placeholder -->
    <rect x="140" y="870" width="280" height="60" class="call-to-action-button" />
    <text x="280" y="907" class="call-to-action-text">
      了解更多研究
    </text>

    <!-- Section 4: Contact Information 和 Thank You -->
    <rect x="980" y="660" width="840" height="320" class="card-fill card-border-stroke" />
    <text x="1020" y="710" class="section-title">
      联系我们
    </text>
    <text x="1020" y="770" class="body-text">
      <tspan x="1020" dy="0em">邮箱: {author}@university.edu</tspan>
      <tspan x="1020" dy="35px">网站: www.university.edu/research</tspan>
      <tspan x="1020" dy="35px">电话: +86-123-4567-890</tspan>
    </text>
    <text x="1020" y="900" class="content-title" fill="var(--primary-color)">
      感谢您的聆听和关注！
    </text>

    <!-- Logo Placeholder -->
    <rect x="1750" y="40" width="120" height="60" fill="url(#accentGradient)" rx="5" ry="5" />
    <text x="1810" y="75" text-anchor="middle" class="small-text" fill="white">
      {logo_url}
    </text>

    <!-- Footer - Date and Author -->
    <text x="100" y="50" class="caption" fill="var(--text-light)">
      {date}
    </text>
    <text x="100" y="70" class="caption" fill="var(--text-light)">
      作者: {author}
    </text>

  </g>
</svg>