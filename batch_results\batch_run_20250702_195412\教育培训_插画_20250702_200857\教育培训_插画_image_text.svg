<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <style>
    /* Base Styles */
    svg {
      background-color: #F8FAFC; /* background_color */
    }

    /* Fonts */
    .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
    .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }

    /* Colors */
    .color-primary { fill: #4A86E8; } /* primary_color */
    .color-secondary { fill: #3B82F6; } /* secondary_color */
    .color-accent { fill: #0EA5E9; } /* accent_color */
    .color-text-primary { fill: #1E293B; } /* text_primary */
    .color-text-secondary { fill: #64748B; } /* text_secondary */
    .color-card-background { fill: #FFFFFF; } /* card_background */
    .color-container-background { fill: #E0F2FE; } /* container_background */
    .color-border { stroke: #BAE6FD; } /* card_border */

    /* Text Styles */
    .hero-title {
      font-size: 72px; /* hero_title */
      font-weight: 700; /* bold */
      fill: #1E293B; /* text_primary */
      font-family: 'Microsoft YaHei', sans-serif;
    }
    .main-title {
      font-size: 56px; /* main_title */
      font-weight: 700; /* bold */
      fill: #1E293B; /* text_primary */
      font-family: 'Microsoft YaHei', sans-serif;
    }
    .section-title {
      font-size: 36px; /* section_title */
      font-weight: 700; /* bold */
      fill: #1E293B; /* text_primary */
      font-family: 'Microsoft YaHei', sans-serif;
    }
    .content-title {
      font-size: 28px; /* content_title */
      font-weight: 700; /* bold */
      fill: #1E293B; /* text_primary */
      font-family: 'Microsoft YaHei', sans-serif;
    }
    .body-text {
      font-size: 22px; /* body_text */
      font-weight: 400; /* normal */
      fill: #64748B; /* text_secondary */
      font-family: 'Source Han Sans CN', sans-serif;
      /* Note: line-height is controlled by dy attribute on tspan for SVG text */
    }
    .small-text {
      font-size: 16px; /* small_text */
      font-weight: 400; /* normal */
      fill: #64748B; /* text_secondary */
      font-family: 'Source Han Sans CN', sans-serif;
    }
    .caption-text {
      font-size: 14px; /* caption */
      font-weight: 400; /* normal */
      fill: #94A3B8; /* text_light */
      font-family: 'Source Han Sans CN', sans-serif;
    }
    .large-number {
      font-size: 180px; /* Custom large size for emphasis */
      font-weight: 900; /* black */
      fill: url(#gradientPrimary); /* Using gradient for emphasis */
      font-family: 'Microsoft YaHei', sans-serif;
      opacity: 0.1; /* High-light color transparency */
    }
    .large-number-accent {
      font-size: 180px; /* Custom large size for emphasis */
      font-weight: 900; /* black */
      fill: #0EA5E9; /* accent_color */
      font-family: 'Microsoft YaHei', sans-serif;
      opacity: 0.1; /* High-light color transparency */
    }

    /* Card Styles */
    .card {
      fill: #FFFFFF; /* card_background */
      stroke: #BAE6FD; /* card_border */
      stroke-width: 1px;
      rx: 12px; /* border_radius */
      ry: 12px; /* border_radius */
      filter: drop-shadow(0px 4px 6px rgba(0, 0, 0, 0.1)) drop-shadow(0px 2px 4px rgba(0, 0, 0, 0.06)); /* shadow */
    }

    /* Image Frame */
    .image-frame {
      stroke: #BAE6FD;
      stroke-width: 2px;
      filter: drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.1)); /* shadow_style */
    }

    /* Decorative elements */
    .decorative-shape {
      fill: #E0F2FE; /* container_background */
      opacity: 0.6;
    }
    .accent-element {
      fill: #0EA5E9; /* accent_color */
      opacity: 0.2;
    }
    .primary-element {
      fill: #4A86E8; /* primary_color */
      opacity: 0.2;
    }
    .icon-style {
      stroke: #4A86E8; /* icon_system color */
      stroke-width: 2; /* icon_system stroke_width */
      fill: none;
    }

    /* Gradients */
    .gradient-primary-fill {
      fill: url(#gradientPrimary);
    }
    .gradient-accent-fill {
      fill: url(#gradientAccent);
    }
  </style>

  <!-- Definitions for Gradients -->
  <defs>
    <linearGradient id="gradientPrimary" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4A86E8" />
      <stop offset="100%" stop-color="#3B82F6" />
    </linearGradient>
    <linearGradient id="gradientAccent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#0EA5E9" />
      <stop offset="100%" stop-color="#4A86E8" />
    </linearGradient>
  </defs>

  <!-- Background Layer - Subtle pattern/gradient -->
  <rect x="0" y="0" width="1920" height="1080" fill="#F8FAFC" />
  <circle cx="1800" cy="100" r="150" class="decorative-shape" />
  <circle cx="100" cy="980" r="120" class="decorative-shape" />
  <rect x="1700" y="900" width="200" height="200" rx="20" ry="20" class="accent-element" />
  <rect x="20" y="20" width="180" height="180" rx="20" ry="20" class="primary-element" />

  <!-- Main Content Area -->
  <!-- Logo Placeholder -->
  <g>
    <rect x="80" y="60" width="120" height="40" fill="#E0F2FE" rx="8" ry="8" />
    <text x="95" y="87" class="small-text color-primary" font-weight="700">LOGO</text>
  </g>


  <!-- Page Title -->
  <text x="960" y="140" text-anchor="middle" class="main-title">
    <tspan>{title}</tspan>
  </text>
  <text x="960" y="200" text-anchor="middle" class="content-title color-text-secondary">
    <tspan>{subtitle}</tspan>
  </text>


  <!-- Left Column: Image Area -->
  <rect x="80" y="240" width="704" height="600" rx="12" ry="12" class="card image-frame" />
  <!-- Image Placeholder - actual image will be placed by href -->
  <image href="{image_url}" x="90" y="250" width="684" height="580" preserveAspectRatio="xMidYMid slice" />
  <!-- Text overlay for image placeholder if no image_url -->
  <text x="432" y="540" text-anchor="middle" class="section-title color-text-secondary" opacity="0.4">
    <tspan>图片占位符</tspan>
  </text>
  <text x="432" y="580" text-anchor="middle" class="body-text color-text-secondary" opacity="0.4">
    <tspan>建议尺寸: 800x600px</tspan>
  </text>


  <!-- Right Column: Text Content Area -->
  <g transform="translate(960, 280)"> <!-- Base x for text column (960), y adjusted for content (280) -->
    <!-- Section Title -->
    <text x="0" y="0" class="section-title">
      <tspan>知识传递和技能培养</tspan>
    </text>

    <!-- Large number emphasis (background elements) -->
    <!-- Positioned to the right of the text block (880px width), so 800px from its own origin -->
    <text x="800" y="100" text-anchor="end" class="large-number">
        <tspan>100</tspan>
    </text>
    <text x="800" y="200" text-anchor="end" class="large-number-accent">
        <tspan>0%</tspan>
    </text>

    <!-- Content - first paragraph -->
    <text x="0" y="60" class="body-text">
      <tspan x="0" dy="0">我们致力于提供高质量的教育培训，</tspan>
      <tspan x="0" dy="30">帮助学生和学员掌握前沿知识和实用技能。</tspan>
      <tspan x="0" dy="30">通过互动式教学，我们激发学习兴趣，</tspan>
      <tspan x="0" dy="30">培养创新思维和解决问题的能力。</tspan>
      <tspan x="0" dy="30">确保每位学员都能充分参与和受益。</tspan>
    </text>

    <!-- Content - second paragraph -->
    <text x="0" y="250" class="body-text">
      <tspan x="0" dy="0">我们的课程内容深入浅出，结合案例分析和</tspan>
      <tspan x="0" dy="30">实践操作，让复杂概念变得易于理解。</tspan>
      <tspan x="0" dy="30">专业的导师团队将全程指导，提供个性化</tspan>
      <tspan x="0" dy="30">反馈，助力您的学习旅程。</tspan>
    </text>

    <!-- Placeholder for content -->
    <text x="0" y="400" class="body-text color-text-secondary" opacity="0.6">
      <tspan x="0" dy="0">{content}</tspan>
      <tspan x="0" dy="30">此处是正文内容的占位符，您可以根据需要填写</tspan>
      <tspan x="0" dy="30">详细的课程介绍、教学方法或学员成功案例。</tspan>
      <tspan x="0" dy="30">确保内容简洁明了，易于阅读。</tspan>
    </text>

    <!-- Icon/Graphic element (simple outline) -->
    <path d="M 0 500 L 50 500 L 50 550 L 100 550 L 100 600 L 150 600" class="icon-style" />
    <circle cx="150" cy="600" r="8" class="color-accent" />
    <text x="170" y="605" class="small-text color-text-secondary">学习路径示意</text>

    <path d="M 400 500 L 450 500 L 450 550 L 500 550 L 500 600 L 550 600" class="icon-style" />
    <circle cx="550" cy="600" r="8" class="color-primary" />
    <text x="570" y="605" class="small-text color-text-secondary">知识增长曲线</text>

  </g>

  <!-- Footer/Page Number -->
  <text x="1840" y="1040" text-anchor="end" class="caption-text color-text-secondary">
    <tspan>页面 5/10</tspan>
  </text>
  <text x="80" y="1040" class="caption-text color-text-secondary">
    <tspan>{date}</tspan>
  </text>

</svg>