{"set_name": "自我介绍_弥散_20250702_204327", "scenario": "自我介绍", "style": "弥散", "created_at": "2025-07-02T20:43:27.820697", "template_count": 10, "templates": [{"template_id": "自我介绍_弥散_cover", "type": "封面页", "filename": "自我介绍_弥散_cover.svg", "page_number": 1}, {"template_id": "自我介绍_弥散_agenda", "type": "目录页", "filename": "自我介绍_弥散_agenda.svg", "page_number": 2}, {"template_id": "自我介绍_弥散_section_divider", "type": "章节分隔页", "filename": "自我介绍_弥散_section_divider.svg", "page_number": 3}, {"template_id": "自我介绍_弥散_title_content", "type": "标题内容页", "filename": "自我介绍_弥散_title_content.svg", "page_number": 4}, {"template_id": "自我介绍_弥散_image_text", "type": "图文混排页", "filename": "自我介绍_弥散_image_text.svg", "page_number": 5}, {"template_id": "自我介绍_弥散_data_display", "type": "数据展示页", "filename": "自我介绍_弥散_data_display.svg", "page_number": 6}, {"template_id": "自我介绍_弥散_comparison", "type": "对比分析页", "filename": "自我介绍_弥散_comparison.svg", "page_number": 7}, {"template_id": "自我介绍_弥散_timeline", "type": "时间线页", "filename": "自我介绍_弥散_timeline.svg", "page_number": 8}, {"template_id": "自我介绍_弥散_quote", "type": "引用页", "filename": "自我介绍_弥散_quote.svg", "page_number": 9}, {"template_id": "自我介绍_弥散_conclusion", "type": "总结页", "filename": "自我介绍_弥散_conclusion.svg", "page_number": 10}], "combination_config": {"scenario": {"scenario_type": "自我介绍", "display_name": "自我介绍", "description": "个人简历、求职面试、个人展示", "visual_characteristics": {"emphasis_on": "个人特色、能力突出", "layout_style": "个性化设计", "decorative_elements": "个人照片、技能图标、成就标记"}, "content_focus": ["个人经历", "专业技能", "个人优势"], "target_audience": "面试官、同事", "tone": "personal"}, "style": {"style_type": "弥散", "display_name": "弥散", "description": "柔和渐变，温和的视觉效果", "design_principles": {"layout": "流动布局、柔和过渡", "elements": "渐变元素、柔和图形", "emphasis": "柔和性、流动性"}, "visual_elements": {"shapes": "柔和圆形、渐变图形", "lines": "柔和边缘、模糊边界", "decorations": "渐变背景、柔光效果"}, "typography": {"font_style": "柔和字体", "weight": "轻薄", "spacing": "舒适间距"}}, "colors": {"primary": "#3B82F6", "secondary": "#1E40AF", "accent": "#06B6D4", "background": "#F8FAFC", "text_primary": "#1E293B", "text_secondary": "#64748B"}, "fusion_rules": {"layout_priority": "scenario", "color_adaptation": "balanced", "typography_blend": "harmonious", "visual_emphasis": []}, "aesthetic_enhancements": {"typography_refinements": {"font_pairing": "complementary", "hierarchy_enhancement": true, "readability_optimization": true}, "layout_improvements": {"golden_ratio_application": true, "visual_balance_optimization": true, "spacing_harmonization": true}, "color_harmony": {"contrast_optimization": true, "accessibility_compliance": true, "emotional_resonance": true}, "visual_elements": {"icon_style_consistency": true, "decorative_element_coordination": true, "brand_element_integration": true}}}, "design_specification": {"theme_selection": {"primary_style": "弥散风格", "scenario_adaptation": "自我介绍场景优化", "visual_theme": "黑底红高亮弥散风格自我介绍模板", "design_philosophy": "以柔和渐变、温和视觉效果为核心，结合面试官和同事的观看需求，强调个人经历、技能与优势的清晰呈现和专业感。通过流动性、半透明材质和微妙光效，营造沉浸且富有科技感的个人展示空间。", "fusion_strategy": "scenario优先的场景风格融合"}, "color_palette": {"primary_color": "#3B82F6", "secondary_color": "#1E40AF", "accent_color": "#06B6D4", "background_color": "#F8FAFC", "text_primary": "#1E293B", "text_secondary": "#64748B", "text_light": "#94A3B8", "success_color": "#10B981", "warning_color": "#F59E0B", "error_color": "#EF4444", "info_color": "#06B6D4", "gradient_primary": "linear-gradient(135deg, #3B82F6, #1E40AF)", "gradient_accent": "linear-gradient(45deg, #06B6D4, #3B82F6)", "card_background": "#FFFFFF", "card_border": "#BAE6FD", "container_background": "#E0F2FE", "hover_color": "#7DD3FC", "active_color": "#3B82F6", "disabled_color": "#64748B"}, "layout_principles": {"canvas_size": {"width": 1920, "height": 1080}, "golden_ratio": 1.618, "grid_system": {"columns": 12, "gutter": 24, "margin": 80}, "spacing": {"xs": 4, "sm": 8, "md": 16, "lg": 24, "xl": 32, "2xl": 48, "3xl": 64, "4xl": 96}, "page_margins": {"horizontal": 80, "vertical": 60, "content_max_width": 1760}, "content_spacing": {"module_gap": 32, "section_gap": 48, "element_gap": 16, "text_gap": 12}, "visual_hierarchy": {"z_index_background": 0, "z_index_content": 10, "z_index_overlay": 20, "z_index_modal": 30}, "alignment": {"text_align": "left", "content_align": "center", "title_align": "center"}}, "typography_system": {"primary_font": "Microsoft YaHei, Segoe UI, sans-serif", "secondary_font": "Source <PERSON> CN, Noto Sans CJK SC, sans-serif", "accent_font": "Times New Roman, serif", "font_sizes": {"hero_title": 72, "main_title": 56, "section_title": 36, "content_title": 28, "body_text": 22, "small_text": 16, "caption": 14}, "font_weights": {"thin": 100, "light": 300, "normal": 400, "medium": 500, "semibold": 600, "bold": 700, "black": 900}, "line_heights": {"tight": 1.1, "normal": 1.4, "relaxed": 1.6, "loose": 1.8}, "letter_spacing": {"tight": "-0.025em", "normal": "0em", "wide": "0.025em", "wider": "0.05em"}}, "visual_elements": {"card_style": {"background": "#FFFFFF", "border_radius": 12, "border": "1px solid #BAE6FD", "shadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)", "hover_shadow": "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"}, "decorative_elements": ["几何图形装饰", "渐变背景", "图标点缀", "分割线", "阴影效果"], "gradients": {"primary_gradient": "linear-gradient(135deg, #4A86E8, #3B82F6)", "accent_gradient": "linear-gradient(45deg, #0EA5E9, #06B6D4)", "background_gradient": "linear-gradient(180deg, #F8FAFC, #E0F2FE)", "text_gradient": "linear-gradient(135deg, #1E3A8A, #1E40AF)"}, "image_effects": {"border_radius": 8, "shadow_style": "0 4px 8px rgba(0, 0, 0, 0.1)", "overlay_style": "linear-gradient(135deg, #4A86E820, #3B82F620)", "hover_transform": "scale(1.02)", "filter_effects": "brightness(1.05) contrast(1.05)"}, "logo_effects": {"positioning": "左上角或页面顶部中央", "size_guidelines": "最大高度80px，宽度自适应", "background_treatment": "透明背景或浅色背景", "shadow": "subtle drop shadow", "hover_effect": "slight glow"}, "icon_system": {"style": "outline", "stroke_width": 2, "size_sm": 16, "size_md": 24, "size_lg": 32, "color": "#4A86E8"}, "dividers": {"horizontal_line": "1px solid #BAE6FD", "vertical_line": "1px solid #BAE6FD", "decorative_divider": "波浪线或装饰性分割", "gradient_divider": "linear-gradient(90deg, transparent, #7DD3FC, transparent)"}}, "template_consistency": {"scenario_coherence": "确保所有模板内容和设计元素都紧密围绕自我介绍这一核心主题，便于面试官快速获取关键信息，并展现个人专业度。", "style_unity": "所有模板统一采用弥散风格，通过柔和渐变、半透明材质和流动线条等特征保持视觉语言的一致性，确保整体呈现的和谐与专业。", "color_harmony": "严格遵循黑底特斯拉红高亮的现代对比配色方案，确保色彩运用的统一性、高辨识度，并在细节处通过蓝色系进行微妙的层次丰富。", "visual_rhythm": "通过Bento Grid布局、超大字体和中英文混排等元素，创造富有节奏感的视觉流，引导用户视线，使信息传递高效且引人入胜。", "brand_consistency": "即使是个人介绍，也要体现出一致的专业性和设计感，形成独特的个人品牌印象，增强求职或展示的竞争力。"}, "created_at": "2025-07-02T20:39:17.319928", "scenario": "自我介绍", "style": "弥散", "canvas_size": {"width": 1920, "height": 1080}, "raw_response": "好的，我将作为您的AI演示文稿设计师，基于您提供的详细要求，生成一份针对“自我介绍”场景和“弥散”设计风格的增强版设计规范。这份规范将严格遵循黑底特斯拉红高亮的主题，并融入Bento Grid布局、超大字体等高级设计理念。\n\n---\n\n```json\n{\n    \"theme_selection\": {\n        \"primary_style\": \"弥散风格\",\n        \"scenario_adaptation\": \"自我介绍场景优化\",\n        \"visual_theme\": \"黑底红高亮弥散风格自我介绍模板\",\n        \"design_philosophy\": \"以柔和渐变、温和视觉效果为核心，结合面试官和同事的观看需求，强调个人经历、技能与优势的清晰呈现和专业感。通过流动性、半透明材质和微妙光效，营造沉浸且富有科技感的个人展示空间。\",\n        \"fusion_strategy\": \"scenario优先的场景风格融合\"\n    },\n    \"color_palette\": {\n        \"background_color\": \"#000000\",\n        \"highlight_color\": \"#E31937\",\n        \"highlight_transparent\": \"rgba(227, 25, 55, 0.7)\",\n        \"highlight_gradient_start\": \"#E31937\",\n        \"highlight_gradient_end\": \"rgba(227, 25, 55, 0.3)\",\n        \"primary_color\": \"#3B82F6\",\n        \"secondary_color\": \"#1E40AF\",\n        \"text_primary\": \"#FFFFFF\",\n        \"text_secondary\": \"#86868B\",\n        \"text_subtle\": \"#F5F5F7\",\n        \"success_color\": \"#10B981\",\n        \"warning_color\": \"#F59E0B\",\n        \"error_color\": \"#EF4444\",\n        \"card_background\": \"#0D0D0D\",\n        \"card_border\": \"#1D1D1F\",\n        \"hover_color\": \"#E31937\"\n    },\n    \"layout_principles\": {\n        \"canvas_size\": {\"width\": 1920, \"height\": 1080},\n        \"golden_ratio_application\": true,\n        \"grid_system\": {\"columns\": 12, \"gutter\": 24, \"margin\": 80},\n        \"page_margins\": {\"horizontal\": 80, \"vertical\": 60},\n        \"content_spacing\": {\"module_gap\": 32, \"section_gap\": 48, \"element_gap\": 16},\n        \"visual_hierarchy\": \"通过Bento Grid布局、超大字体和高亮色，构建清晰、动态的视觉层次，引导面试官快速聚焦关键信息。\",\n        \"alignment_system\": \"基于弥散风格的柔和对齐原则，注重视觉流动性和平衡，通过模块化的卡片组合实现视觉上的和谐与秩序。\"\n    },\n    \"typography_system\": {\n        \"primary_font\": \"系统UI字体栈（如：system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Sans SC', 'PingFang SC', 'Microsoft YaHei'），确保中英文显示兼容性和高可读性。\",\n        \"font_sizes\": {\n            \"hero_title\": 120,\n            \"main_title\": 72,\n            \"section_title\": 48,\n            \"content_title\": 32,\n            \"body_text\": 24,\n            \"small_text\": 16,\n            \"accent_number\": 180\n        },\n        \"font_weights\": {\n            \"title\": \"bold\",\n            \"content\": \"normal\",\n            \"emphasis\": \"600\",\n            \"chinese\": \"bold\",\n            \"english\": \"300\"\n        },\n        \"line_heights\": {\n            \"title\": 1.2,\n            \"content\": 2.0,\n            \"dense\": 1.8\n        },\n        \"mixed_typography\": {\n            \"chinese_style\": \"大号粗体，行高建议为字号的2.2-2.5倍，确保中文文本在视觉上的力量感和清晰度。\",\n            \"english_style\": \"小号细体，作为点缀、补充说明或专业术语，与中文形成对比，增强国际化视觉效果。\",\n            \"number_style\": \"超大号突出，使用高亮色或渐变效果，创造强烈视觉冲击力，尤其适用于关键数据和成就。\"\n        },\n        \"readability_optimization\": \"针对面试官、同事优化的可读性：确保黑底与白（或浅灰）文字之间的高对比度，所有文本使用充足的字号和行间距，避免内容拥挤，尤其关注弥散风格下的文字清晰度。\"\n    },\n    \"visual_elements\": {\n        \"scenario_specific_elements\": \"个人成就图表、技能点图标（简洁线条风格）、项目时间轴（流线型）、联系方式卡片（带微光效果）、个人照片占位（柔和圆角，可带半透明遮罩）、职业发展路径示意图（简洁勾线）。\",\n        \"style_characteristics\": \"柔和的背景渐变（主要为特斯拉红的透明度渐变和蓝色系的微妙背景渐变）、卡片边缘的微光效果、内容元素的流畅过渡动画理念、半透明材质感（卡片背景）、低饱和度的装饰性纹理和光晕效果。\",\n        \"bento_grid_layout\": \"采用类似Apple官网的Bento Grid网格布局，通过不同尺寸的模块化卡片组合，实现视觉上的动态平衡和信息的高效分组。卡片间距适中，确保呼吸感。\",\n        \"black_red_theme\": \"纯黑色背景(#000000)与特斯拉红色(#E31937)形成强烈对比，红色用于高亮核心信息、强调数字、关键技能点和互动元素，营造现代、专业且充满活力的氛围。\",\n        \"oversized_typography\": \"超大字号的数字和关键短语作为页面核心焦点，通过字号差异、字重和高亮色构建清晰的视觉层次和冲击力，快速吸引注意力。\",\n        \"decorative_elements\": [\n            \"简洁线条图形元素：用于数据可视化（如进度条、技能雷达图）、流程图或作为背景纹理，线条颜色为高亮色或辅助色，透明度低（0.05-0.15）。\",\n            \"特斯拉红色透明度渐变元素：单一颜色（#E31937）的透明度渐变，用于背景、卡片边框或装饰性形状，创造科技感和弥散效果，避免多色渐变带来的视觉干扰。\",\n            \"中英文混排排版元素：通过字号、字重、颜色和间距的差异，实现中英文的视觉对比和信息层级，提升国际化和专业感。\",\n            \"符合弥散风格的装饰元素：如柔和的光晕、模糊的圆形或线条、半透明的几何形状，以极低透明度（0.03-0.1）融入背景或卡片，增加视觉深度和流动性。\",\n            \"适合自我介绍场景的装饰元素：如代表成就的星标、代表技能的齿轮、代表连接的线条、代表增长的箭头等，以极简线条风格和低透明度呈现，不喧宾夺主。\"\n        ],\n        \"card_style\": {\n            \"background\": \"#0D0D0D\",\n            \"border_radius\": \"16px-24px，根据卡片尺寸和内容密度动态调整，确保柔和且现代感，与弥散风格相符。\",\n            \"shadow\": \"微妙的内阴影或柔和的外发光效果（使用高亮色或辅助色，低透明度），以增强卡片的立体感和弥散效果，避免生硬的投影。\",\n            \"border\": \"特斯拉红色细边框（stroke-width: 2-3px），可带微弱的透明度渐变效果，增强科技感和高亮，与背景形成微妙的区分。\"\n        },\n        \"image_integration\": {\n            \"border_radius\": \"12px-20px，与卡片圆角保持一致或略小，以适应弥散风格的柔和感。\",\n            \"shadow_style\": \"柔和的外部阴影或无阴影，避免干扰图片内容，若有阴影则为扩散型，低透明度。\",\n            \"overlay_style\": \"特斯拉红色半透明遮罩（opacity: 0.1-0.3），用于图片背景或特殊效果，避免覆盖主体内容，增加科技感。\",\n            \"bento_grid_placement\": \"图片作为Bento Grid中的一个模块，根据图片内容和重要性灵活调整模块尺寸，保持与整体网格的协调性，并确保图片在视觉上融入整体设计。\"\n        },\n        \"logo_treatment\": {\n            \"positioning\": \"通常位于页面顶部或底部角落，或作为独立卡片模块的一部分，保持视觉平衡和一致性。\",\n            \"size_guidelines\": \"适中，确保可识别性而不喧宾夺主，与超大字体形成对比，建议尺寸占画布高度的5%-8%。\",\n            \"integration_style\": \"白色或高亮色单色Logo，确保在黑底上的清晰度，可有微弱的呼吸光效或弥散光晕效果。\",\n            \"animation_hint\": \"模仿Apple官网的动效设计，考虑滚动时Logo的微动效或透明度变化，增加交互感和品牌活力。\"\n        }\n    },\n    \"template_consistency\": {\n        \"scenario_coherence\": \"确保所有模板内容和设计元素都紧密围绕自我介绍这一核心主题，便于面试官快速获取关键信息，并展现个人专业度。\",\n        \"style_unity\": \"所有模板统一采用弥散风格，通过柔和渐变、半透明材质和流动线条等特征保持视觉语言的一致性，确保整体呈现的和谐与专业。\",\n        \"color_harmony\": \"严格遵循黑底特斯拉红高亮的现代对比配色方案，确保色彩运用的统一性、高辨识度，并在细节处通过蓝色系进行微妙的层次丰富。\",\n        \"visual_rhythm\": \"通过Bento Grid布局、超大字体和中英文混排等元素，创造富有节奏感的视觉流，引导用户视线，使信息传递高效且引人入胜。\",\n        \"brand_consistency\": \"即使是个人介绍，也要体现出一致的专业性和设计感，形成独特的个人品牌印象，增强求职或展示的竞争力。\"\n    }\n}\n```"}}