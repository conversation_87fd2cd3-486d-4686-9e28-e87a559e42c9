<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义颜色和渐变，严格遵循统一蓝色系配色 -->
    <style type="text/css">
      <![CDATA[
      /* 配色方案 */
      .background-color { fill: #F8FAFC; }
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .text-primary-color { fill: #1E293B; }
      .text-secondary-color { fill: #64748B; }
      .text-light-color { fill: #94A3B8; }
      .card-background-color { fill: #FFFFFF; }
      .card-border-color { stroke: #BAE6FD; }
      .container-background-color { fill: #E0F2FE; }
      .icon-stroke-color { stroke: #4A86E8; } /* 使用设计规范中的icon_system color */
      .icon-fill-color { fill: #4A86E8; } /* 使用设计规范中的icon_system color */

      /* 字体系统 */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
      .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; }
      .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; }
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; }
      .caption-text { font-size: 14px; font-weight: 400; line-height: 1.6; }

      /* 视觉元素 - 卡片样式 */
      .card-style {
        fill: #FFFFFF;
        stroke: #BAE6FD;
        stroke-width: 1px;
        filter: url(#card-shadow); /* 应用阴影滤镜 */
      }

      /* 分割线样式 */
      .divider-line {
        stroke: #BAE6FD;
        stroke-width: 1px;
      }
      ]]>
    </style>

    <!-- 滤镜定义：卡片阴影 -->
    <filter id="card-shadow" x="-5%" y="-5%" width="110%" height="110%">
      <feOffset dx="0" dy="4"/>
      <feGaussianBlur stdDeviation="3"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 渐变定义：用于装饰元素 -->
    <linearGradient id="primaryGradientBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF"/>
      <stop offset="100%" stop-color="#475569"/>
    </linearGradient>
    <linearGradient id="accentGradientBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- 图标系统：简洁勾线图形化 -->
    <!-- 市场分析 (Chart Icon) -->
    <g id="icon-chart">
      <path d="M4 16H8V8H4V16ZM10 20H14V4H10V20ZM16 12H20V4H16V12Z" class="icon-fill-color"/>
    </g>
    <!-- 商业模式 (Target Icon) -->
    <g id="icon-target">
      <circle cx="12" cy="12" r="10" class="icon-stroke-color" stroke-width="2"/>
      <circle cx="12" cy="12" r="6" class="icon-stroke-color" stroke-width="2"/>
      <circle cx="12" cy="12" r="2" class="icon-fill-color"/>
    </g>
    <!-- 财务预测 (Growth Icon) -->
    <g id="icon-growth">
      <path d="M2 16L8 10L12 14L22 4M17 4H22V9" class="icon-stroke-color" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </g>
    <!-- 风险评估 (Warning Icon) -->
    <g id="icon-risk">
      <path d="M12 2L2 22H22L12 2Z" class="icon-stroke-color" stroke-width="2" stroke-linejoin="round" stroke-linecap="round"/>
      <path d="M12 9V13" class="icon-stroke-color" stroke-width="2" stroke-linecap="round"/>
      <circle cx="12" cy="17" r="1" class="icon-fill-color"/>
    </g>
  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="1920" height="1080" class="background-color"/>

  <!-- 顶部区域：Logo 和 页码 -->
  <g id="header">
    <!-- Logo 占位符 -->
    <rect x="80" y="60" width="160" height="40" fill="#E0F2FE" rx="8"/>
    <text x="160" y="87" text-anchor="middle" class="small-text text-primary-color font-primary">
      <tspan>{logo_url}</tspan>
    </text>

    <!-- 页码信息 -->
    <text x="1760" y="87" text-anchor="end" class="small-text text-secondary-color font-primary">
      <tspan>页码: 2/10</tspan>
    </text>
  </g>

  <!-- 主内容区域 -->
  <g id="main-content">
    <!-- 主标题 -->
    <text x="960" y="200" text-anchor="middle" class="main-title text-primary-color font-primary">
      <tspan>{title}</tspan>
    </text>
    <!-- 副标题 -->
    <text x="960" y="260" text-anchor="middle" class="body-text text-secondary-color font-primary">
      <tspan>{subtitle}</tspan>
    </text>

    <!-- 目录结构和章节导航 -->
    <g id="directory-list">
      <!-- 章节 01: 市场分析和机遇 -->
      <rect x="180" y="360" width="750" height="180" rx="12" class="card-style"/>
      <text x="220" y="410" class="content-title text-primary-color font-primary">
        <tspan>01. 市场分析和机遇</tspan>
      </text>
      <text x="220" y="445" class="body-text text-secondary-color font-primary">
        <tspan>深入探究行业趋势、目标市场和竞争格局。</tspan>
        <tspan x="220" dy="30">发现潜在增长点和战略优势。</tspan>
      </text>
      <use href="#icon-chart" x="850" y="370" width="32" height="32"/>

      <!-- 章节 02: 商业模式和产品 -->
      <rect x="990" y="360" width="750" height="180" rx="12" class="card-style"/>
      <text x="1030" y="410" class="content-title text-primary-color font-primary">
        <tspan>02. 商业模式和产品</tspan>
      </text>
      <text x="1030" y="445" class="body-text text-secondary-color font-primary">
        <tspan>解析核心商业模式、价值主张和产品服务。</tspan>
        <tspan x="1030" dy="30">阐述如何创造和交付价值。</tspan>
      </text>
      <use href="#icon-target" x="1660" y="370" width="32" height="32"/>

      <!-- 章节 03: 财务预测和资金 -->
      <rect x="180" y="570" width="750" height="180" rx="12" class="card-style"/>
      <text x="220" y="620" class="content-title text-primary-color font-primary">
        <tspan>03. 财务预测和资金</tspan>
      </text>
      <text x="220" y="655" class="body-text text-secondary-color font-primary">
        <tspan>详细的财务估算、盈利能力分析和资金需求。</tspan>
        <tspan x="220" dy="30">展示投资回报潜力。</tspan>
      </text>
      <use href="#icon-growth" x="850" y="580" width="32" height="32"/>

      <!-- 章节 04: 风险评估和应对 -->
      <rect x="990" y="570" width="750" height="180" rx="12" class="card-style"/>
      <text x="1030" y="620" class="content-title text-primary-color font-primary">
        <tspan>04. 风险评估和应对</tspan>
      </text>
      <text x="1030" y="655" class="body-text text-secondary-color font-primary">
        <tspan>识别潜在风险、挑战和应对策略。</tspan>
        <tspan x="1030" dy="30">构建稳健的商业运营框架。</tspan>
      </text>
      <use href="#icon-risk" x="1660" y="580" width="32" height="32"/>

      <!-- 更多内容章节占位符 -->
      <rect x="180" y="780" width="1560" height="180" rx="12" class="card-style"/>
      <text x="960" y="870" text-anchor="middle" class="section-title text-light-color font-primary">
        <tspan>{content}</tspan>
      </text>
    </g>
  </g>

  <!-- 底部区域：进度指示器 -->
  <g id="progress-indicator">
    <text x="80" y="990" class="small-text text-secondary-color font-primary">
      <tspan>进度</tspan>
    </text>
    <!-- 完整进度条背景 -->
    <rect x="150" y="980" width="1690" height="8" rx="4" class="container-background-color"/>
    <!-- 当前进度 (2/10 = 20% of 1690 = 338) -->
    <rect x="150" y="980" width="338" height="8" rx="4" class="accent-color"/>
  </g>

  <!-- 装饰性元素 (简洁、专业、科技感) -->
  <g id="decorative-elements">
    <!-- 左上角几何图形装饰，带渐变和透明度 -->
    <path d="M0 0 H300 L250 100 L0 150 V0 Z" fill="url(#primaryGradientBg)" opacity="0.1"/>
    <!-- 右下角几何图形装饰，带渐变和透明度 -->
    <path d="M1920 1080 H1620 L1670 980 L1920 930 V1080 Z" fill="url(#accentGradientBg)" opacity="0.1"/>

    <!-- 强调色小圆点装饰 -->
    <circle cx="1800" cy="150" r="20" fill="#3B82F6" opacity="0.2"/>
    <circle cx="120" cy="900" r="15" fill="#1E40AF" opacity="0.2"/>
  </g>

</svg>