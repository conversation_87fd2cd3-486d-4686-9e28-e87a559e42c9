<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette -->
    <style type="text/css">
      :root {
        --primary-color: #4A86E8;
        --secondary-color: #3B82F6;
        --accent-color: #0EA5E9;
        --background-color: #F8FAFC;
        --text-primary: #1E293B;
        --text-secondary: #64748B;
        --card-background: #FFFFFF;
        --card-border: #BAE6FD;
        --container-background: #E0F2FE;
      }

      /* Base styles for the SVG */
      svg {
        background-color: var(--background-color);
      }

      /* Font styles */
      .font-primary {
        font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
      }
      .font-secondary {
        font-family: "Source Han Sans CN", "Noto Sans CJK SC", sans-serif;
      }
      .font-accent {
        font-family: "Times New Roman", serif;
      }

      /* Font sizes */
      .text-hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
      .text-main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
      .text-section-title { font-size: 36px; font-weight: 700; line-height: 1.4; }
      .text-content-title { font-size: 28px; font-weight: 700; line-height: 1.4; }
      .text-body { font-size: 22px; font-weight: 400; line-height: 1.6; }
      .text-small { font-size: 16px; font-weight: 400; line-height: 1.4; }
      .text-caption { font-size: 14px; font-weight: 400; line-height: 1.4; }

      /* Colors */
      .fill-primary { fill: var(--primary-color); }
      .fill-secondary { fill: var(--secondary-color); }
      .fill-accent { fill: var(--accent-color); }
      .fill-text-primary { fill: var(--text-primary); }
      .fill-text-secondary { fill: var(--text-secondary); }
      .stroke-primary { stroke: var(--primary-color); }
      .stroke-accent { stroke: var(--accent-color); }
      .stroke-text-primary { stroke: var(--text-primary); }

      /* Card/Container styles */
      .card-background { fill: var(--card-background); }
      .card-border { stroke: var(--card-border); stroke-width: 1px; }
      .container-background-fill { fill: var(--container-background); }

      /* Specific styles for this template */
      .educational-illustration-item {
          stroke: var(--primary-color);
          stroke-width: 2;
          fill: none;
      }
      .educational-illustration-fill {
          fill: var(--primary-color);
          opacity: 0.1; /* Use transparency for tech feel */
      }
      .educational-illustration-accent-fill {
          fill: var(--accent-color);
          opacity: 0.1;
      }
      .educational-illustration-detail {
          fill: var(--primary-color);
      }
    </style>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4A86E8" />
      <stop offset="100%" stop-color="#3B82F6" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#0EA5E9" />
      <stop offset="100%" stop-color="#4A86E8" />
    </linearGradient>
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#F8FAFC" />
      <stop offset="100%" stop-color="#E0F2FE" />
    </linearGradient>

    <!-- Filters for shadows -->
    <!-- Shadow for cards and containers -->
    <filter id="shadowLight" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4" />
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="4" />
      <feColorMatrix result="matrixOut" in="blurOut" type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0" />
      <feBlend in="SourceGraphic" in2="matrixOut" mode="normal" />
    </filter>

    <!-- Educational Icons (simple outline style) -->
    <g id="icon-book" class="educational-illustration-item">
      <rect x="0" y="0" width="20" height="24" rx="2" ry="2"/>
      <line x1="0" y1="4" x2="20" y2="4"/>
      <line x1="0" y1="8" x2="20" y2="8"/>
      <line x1="0" y1="12" x2="20" y2="12"/>
    </g>

    <g id="icon-lightbulb" class="educational-illustration-item">
      <path d="M12 2C8.68629 2 6 4.68629 6 8C6 10.9839 8.24354 13.4344 11 13.9189V16C11 16.5523 11.4477 17 12 17C12.5523 17 13 16.5523 13 16V13.9189C15.7565 13.4344 18 10.9839 18 8C18 4.68629 15.3137 2 12 2Z"/>
      <path d="M11 17L11 19C11 19.5523 11.4477 20 12 20C12.5523 20 13 19.5523 13 19L13 17" stroke-linecap="round"/>
      <path d="M9 20L9 22C9 22.5523 9.44772 23 10 23C10.5523 23 11 22.5523 11 22L11 20" stroke-linecap="round"/>
      <path d="M13 20L13 22C13 22.5523 13.4477 23 14 23C14.5523 23 15 22.5523 15 22L15 20" stroke-linecap="round"/>
    </g>

    <g id="icon-chart" class="educational-illustration-item">
      <polyline points="2 18 8 10 14 14 22 6" fill="none" stroke-linejoin="round" stroke-linecap="round"/>
      <line x1="2" y1="22" x2="22" y2="22"/>
      <line x1="2" y1="22" x2="2" y2="2"/>
    </g>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)" />

  <!-- Decorative elements (abstract shapes, "Bento Grid" style background) -->
  <!-- Top-left large rounded rectangle (subtle background element) -->
  <rect x="0" y="0" width="900" height="500" rx="40" ry="40" class="container-background-fill" opacity="0.6"/>
  <!-- Bottom-right large rounded rectangle (subtle background element) -->
  <rect x="1020" y="580" width="900" height="500" rx="40" ry="40" class="container-background-fill" opacity="0.6"/>

  <!-- Smaller illustrative shapes with accent color transparency -->
  <circle cx="1600" cy="180" r="100" class="educational-illustration-accent-fill"/>
  <rect x="100" y="900" width="200" height="100" rx="20" ry="20" class="educational-illustration-fill"/>
  <polygon points="1700,800 1850,850 1750,950 1600,900" class="educational-illustration-accent-fill"/>

  <!-- Main content area -->
  <g id="page-content">
    <!-- Logo placeholder (top-left) -->
    <image x="80" y="60" width="120" height="auto" xlink:href="{logo_url}" />

    <!-- Main Title -->
    <text x="960" y="160" text-anchor="middle" class="font-primary text-main-title fill-text-primary">
      <tspan>探索知识的奥秘和技能的提升</tspan>
    </text>

    <!-- Subtitle / Introduction Paragraph -->
    <!-- y coordinate for the first line of the paragraph -->
    <text x="960" y="240" text-anchor="middle" class="font-secondary text-body fill-text-secondary">
      <tspan x="960">本课程旨在提供全面深入的学习体验，帮助学员掌握核心概念</tspan>
      <!-- dy for subsequent lines within the same text element -->
      <tspan x="960" dy="35">和实践技能。我们注重理论与实践相结合，确保每位学员都能</tspan>
      <tspan x="960" dy="35">学有所获，实现个人成长和职业发展。</tspan>
    </text>

    <!-- Content Blocks (Bento Grid interpretation) -->
    <!-- Left Content Block: Main Paragraph -->
    <rect x="80" y="380" width="800" height="580" rx="20" ry="20" class="card-background card-border" filter="url(#shadowLight)"/>
    <text x="120" y="440" class="font-primary text-content-title fill-text-primary">
      <tspan>核心课程内容</tspan>
    </text>
    <text x="120" y="490" class="font-secondary text-body fill-text-secondary">
      <tspan x="120">我们的课程设计紧密围绕现代教育趋势和行业需求，</tspan>
      <tspan x="120" dy="35">涵盖了从基础理论到高级应用的各个层面。我们相信，</tspan>
      <tspan x="120" dy="35">通过系统化的学习和实践，学员将能够构建坚实的知识体系</tspan>
      <tspan x="120" dy="35">和解决实际问题的能力。课程内容定期更新，以确保其</tspan>
      <tspan x="120" dy="35">与时俱进，满足不断变化的挑战和机遇。</tspan>
      <tspan x="120" dy="35">我们鼓励互动式教学，让学员在问答和讨论中深化理解。</tspan>
    </text>

    <!-- Right Content Block: Key Points List -->
    <rect x="940" y="380" width="900" height="580" rx="20" ry="20" class="card-background card-border" filter="url(#shadowLight)"/>
    <text x="980" y="440" class="font-primary text-content-title fill-text-primary">
      <tspan>学习要点</tspan>
    </text>

    <!-- Key Points List. Each list item is a separate text element for clear spacing control. -->
    <g class="font-secondary text-body fill-text-secondary">
      <text x="980" y="500">
        <tspan>和#x2022; 深入理解核心概念和原理</tspan>
      </text>
      <text x="980" y="545"> <!-- 45px spacing -->
        <tspan>和#x2022; 掌握实用的操作技能和工具</tspan>
      </text>
      <text x="980" y="590"> <!-- 45px spacing -->
        <tspan>和#x2022; 培养解决问题的能力和创新思维</tspan>
      </text>
      <text x="980" y="635"> <!-- 45px spacing -->
        <tspan>和#x2022; 提升团队协作和沟通技巧</tspan>
      </text>
      <text x="980" y="680"> <!-- 45px spacing -->
        <tspan>和#x2022; 适应未来发展趋势和行业变化</tspan>
      </text>
      <text x="980" y="725"> <!-- 45px spacing -->
        <tspan>和#x2022; 获得专业认证和职业发展机会</tspan>
      </text>
    </g>

    <!-- Illustrative Icons within the Right Content Block - positioned carefully to not overlap text -->
    <!-- Icons scaled and translated to fit -->
    <use xlink:href="#icon-book" x="1750" y="400" width="32" height="32" class="stroke-accent" transform="scale(1.5) translate(-1750/1.5 + 1750, -400/1.5 + 400)"/>
    <use xlink:href="#icon-lightbulb" x="1780" y="600" width="32" height="32" class="stroke-primary" transform="scale(1.5) translate(-1780/1.5 + 1780, -600/1.5 + 600)"/>
    <use xlink:href="#icon-chart" x="1700" y="800" width="32" height="32" class="stroke-accent" transform="scale(1.5) translate(-1700/1.5 + 1700, -800/1.5 + 800)"/>

    <!-- Large number emphasis (educational context) -->
    <!-- This section is placed within the left content block for emphasis -->
    <text x="500" y="880" text-anchor="middle" class="font-primary text-hero-title fill-accent">
        <tspan>95%</tspan>
    </text>
    <text x="500" y="930" text-anchor="middle" class="font-secondary text-small fill-text-secondary">
        <tspan>学员满意度</tspan>
    </text>
    <text x="750" y="880" text-anchor="middle" class="font-primary text-hero-title fill-accent">
        <tspan>500+</tspan>
    </text>
    <text x="750" y="930" text-anchor="middle" class="font-secondary text-small fill-text-secondary">
        <tspan>成功案例</tspan>
    </text>

    <!-- Page Number -->
    <text x="1840" y="1020" text-anchor="end" class="font-secondary text-small fill-text-secondary">
      <tspan>4 / 10</tspan>
    </text>
  </g>

</svg>