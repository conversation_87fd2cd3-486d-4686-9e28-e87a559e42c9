<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Color Definitions -->
    <style type="text/css">
      /* --- Color Palette --- */
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .background-color { fill: #F8FAFC; }
      .text-primary-color { fill: #1E293B; }
      .text-secondary-color { fill: #64748B; }
      .text-light-color { fill: #94A3B8; }
      .card-background-color { fill: #FFFFFF; }

      /* --- Font System --- */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      .main-title {
        font-size: 56px; /* main_title */
        font-weight: 700; /* bold */
        fill: #1E293B; /* text_primary */
        line-height: 1.1; /* tight */
      }
      .section-title {
        font-size: 36px; /* section_title */
        font-weight: 700; /* bold */
        fill: #1E293B; /* text_primary */
      }
      .subtitle-text {
        font-size: 28px; /* content_title */
        font-weight: 400; /* normal */
        fill: #475569; /* secondary_color or text_secondary_color */
        line-height: 1.4; /* normal */
      }
      .small-text {
        font-size: 16px; /* small_text */
        font-weight: 400; /* normal */
        fill: #64748B; /* text_secondary */
      }

      /* --- Visual Elements --- */
      .outline-stroke {
        stroke: #3B82F6; /* accent_color */
        stroke-width: 2;
        fill: none;
      }
      .filled-shape {
        fill: #1E40AF; /* primary_color */
      }
      .filled-shape-light {
        fill: #3B82F6; /* accent_color */
      }
    </style>

    <!-- Gradients for decorative elements and background overlays -->
    <linearGradient id="gradientPrimary" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#475569" />
    </linearGradient>
    <linearGradient id="gradientAccent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>
    <linearGradient id="gradientBackgroundOverlay" x1="0%" y1="0%" x2="100%" y2="100%">
      <!-- Based on background_gradient: linear-gradient(180deg, #F8FAFC, #E0F2FE) -->
      <stop offset="0%" stop-color="#E0F2FE" stop-opacity="0.5" />
      <stop offset="100%" stop-color="#BAE6FD" stop-opacity="0.5" />
    </linearGradient>
  </defs>

  <!-- Background Layer -->
  <rect x="0" y="0" width="1920" height="1080" class="background-color" />

  <!-- Decorative Background Elements for Transition and Depth -->
  <!-- Large, subtle gradient overlay mimicking background_gradient -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#gradientBackgroundOverlay)" opacity="0.3" />

  <!-- Abstract wave/curve element 1 (top) -->
  <path d="M0 0 L1920 0 L1920 300 C1500 450, 400 450, 0 300 L0 0 Z" fill="url(#gradientPrimary)" opacity="0.15" />
  <!-- Abstract wave/curve element 2 (bottom) -->
  <path d="M1920 1080 L0 1080 L0 780 C400 630, 1500 630, 1920 780 L1920 1080 Z" fill="url(#gradientAccent)" opacity="0.15" />

  <!-- Dynamic geometric shapes -->
  <circle cx="1700" cy="150" r="100" fill="#3B82F6" opacity="0.1" />
  <circle cx="220" cy="930" r="80" fill="#1E40AF" opacity="0.1" />

  <rect x="1500" y="800" width="250" height="150" rx="20" ry="20" fill="#475569" opacity="0.1" transform="rotate(15 1625 875)" />
  <rect x="150" y="100" width="200" height="120" rx="15" ry="15" fill="#1E40AF" opacity="0.1" transform="rotate(-10 250 160)" />

  <!-- Main Content - Chapter Title -->
  <g id="ChapterTitleSection">
    <text x="960" y="480" text-anchor="middle" class="font-primary main-title">
      <tspan x="960" dy="0">{title}</tspan>
    </text>
    <text x="960" y="560" text-anchor="middle" class="font-secondary subtitle-text">
      <tspan x="960" dy="0">{subtitle}</tspan>
    </text>
  </g>

  <!-- Transitional Decorative Elements - Lines and abstract data-like patterns -->
  <!-- Horizontal line with accent color gradient, symbolizing progression -->
  <rect x="700" y="650" width="520" height="4" rx="2" ry="2" fill="url(#gradientAccent)" />

  <!-- Abstract "data points" or "progress indicators" along the line -->
  <circle cx="700" cy="652" r="10" fill="#3B82F6" />
  <circle cx="850" cy="652" r="10" fill="#3B82F6" opacity="0.7" />
  <circle cx="1000" cy="652" r="10" fill="#3B82F6" opacity="0.4" />
  <circle cx="1150" cy="652" r="10" fill="#3B82F6" opacity="0.1" />

  <!-- Abstract 'network' or 'connection' lines, subtle and flowing -->
  <path d="M100 200 C300 100, 600 100, 800 200" class="outline-stroke" opacity="0.3" />
  <path d="M1820 880 C1620 980, 1320 980, 1120 880" class="outline-stroke" opacity="0.3" />

  <!-- Small geometric accents around the title area for visual emphasis -->
  <rect x="800" y="400" width="20" height="20" rx="5" ry="5" fill="#3B82F6" opacity="0.2" transform="rotate(45 810 410)" />
  <rect x="1100" y="400" width="20" height="20" rx="5" ry="5" fill="#1E40AF" opacity="0.2" transform="rotate(45 1110 410)" />

  <rect x="800" y="600" width="20" height="20" rx="5" ry="5" fill="#1E40AF" opacity="0.2" transform="rotate(45 810 610)" />
  <rect x="1100" y="600" width="20" height="20" rx="5" ry="5" fill="#3B82F6" opacity="0.2" transform="rotate(45 1110 610)" />

  <!-- Page number indicator at bottom right -->
  <text x="1840" y="1020" text-anchor="end" class="font-primary small-text" fill="#64748B">
    <tspan x="1840" dy="0">页面 3/10</tspan>
  </text>
</svg>