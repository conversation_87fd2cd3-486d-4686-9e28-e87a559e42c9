<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <!-- 背景渐变，从纯黑到深蓝（符合商务风格的暗色调） -->
        <linearGradient id="backgroundGradient" x1="960" y1="0" x2="960" y2="1080" gradientUnits="userSpaceOnUse">
            <stop stop-color="#000000"/>
            <stop offset="1" stop-color="#1E293B"/> <!-- 使用text_primary的深色，作为背景底部融合色 -->
        </linearGradient>

        <!-- 强调色渐变：特斯拉红到透明红，制造科技感 -->
        <linearGradient id="accentGradientRed" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox">
            <stop offset="0%" stop-color="#E31937"/>
            <stop offset="100%" stop-color="#E31937" stop-opacity="0.2"/>
        </linearGradient>

        <!-- 卡片背景渐变：使用蓝色系主色和辅助色，增加深度感 -->
        <linearGradient id="cardGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox">
            <stop offset="0%" stop-color="#1E40AF" stop-opacity="0.25"/> <!-- primary_color，半透明 -->
            <stop offset="100%" stop-color="#475569" stop-opacity="0.25"/> <!-- secondary_color，半透明 -->
        </linearGradient>

        <!-- 文本或重要元素的微光效果 -->
        <filter id="glowEffect" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur in="SourceGraphic" stdDeviation="8" result="blur"/>
            <feMerge>
                <feMergeNode in="blur"/>
                <feMergeNode in="SourceGraphic"/>
            </feMerge>
        </filter>

        <!-- 商务风格的简洁勾线图标定义 -->
        <symbol id="icon-chart" viewBox="0 0 24 24">
            <path d="M4 20H20V4" stroke="#E31937" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M18 18V10" stroke="#E31937" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M12 18V6" stroke="#E31937" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M6 18V14" stroke="#E31937" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </symbol>
        <symbol id="icon-target" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="10" stroke="#E31937" stroke-width="2"/>
            <circle cx="12" cy="12" r="6" stroke="#E31937" stroke-width="2"/>
            <circle cx="12" cy="12" r="2" fill="#E31937"/>
        </symbol>
        <symbol id="icon-star" viewBox="0 0 24 24">
            <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="#E31937" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </symbol>
        <symbol id="icon-lightbulb" viewBox="0 0 24 24">
            <path d="M9 18H15" stroke="#E31937" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M12 22C14.7614 22 17 19.7614 17 17H7C7 19.7614 9.23858 22 12 22Z" stroke="#E31937" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M12 17V2" stroke="#E31937" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M18 13C19.6569 13 21 11.6569 21 10C21 8.34315 19.6569 7 18 7C16.3431 7 15 8.34315 15 10C15 11.6569 16.3431 13 18 13Z" stroke="#E31937" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M6 13C7.65685 13 9 11.6569 9 10C9 8.34315 7.65685 7 6 7C4.34315 7 3 8.34315 3 10C3 11.6569 4.34315 13 6 13Z" stroke="#E31937" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </symbol>
    </defs>

    <style>
        /* 字体定义 */
        .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
        .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
        .font-accent { font-family: 'Times New Roman', serif; }

        /* 颜色定义 (结合了蓝色系和特斯拉红) */
        .color-primary-blue { fill: #1E40AF; } /* 深蓝 */
        .color-secondary-blue { fill: #475569; } /* 灰蓝 */
        .color-accent-red { fill: #E31937; } /* 特斯拉红作为主要强调色 */
        .color-accent-blue { fill: #3B82F6; } /* 强调蓝作为次要强调色，用于图表线条等 */
        .color-text-light { fill: #F8FAFC; } /* 浅色文本，用于暗背景 */
        .color-text-secondary { fill: #94A3B8; } /* 较浅的灰色文本 */

        /* 描边颜色 */
        .stroke-red { stroke: #E31937; }
        .stroke-blue { stroke: #3B82F6; }

        /* 字体大小和粗细 */
        .hero-title { font-size: 72px; font-weight: 700; } /* 超大标题 */
        .main-title { font-size: 56px; font-weight: 700; } /* 主要标题 */
        .section-title { font-size: 36px; font-weight: 700; } /* 章节标题 */
        .content-title { font-size: 28px; font-weight: 600; } /* 内容标题 */
        .body-text { font-size: 22px; font-weight: 400; line-height: 1.4; } /* 正文文本 */
        .small-text { font-size: 16px; font-weight: 400; } /* 小文本 */
        .caption { font-size: 14px; font-weight: 300; } /* 说明文字 */
        .font-bold { font-weight: 700; }
        .font-semibold { font-weight: 600; }
        .font-normal { font-weight: 400; }

        /* 卡片样式 */
        .card-bg {
            fill: url(#cardGradient); /* 使用半透明渐变 */
            stroke: rgba(255, 255, 255, 0.1); /* 浅色边框 */
            stroke-width: 1px;
        }

        /* 勾线图形样式 */
        .outline-shape {
            stroke: #E31937;
            stroke-width: 3;
            fill: none;
        }
        .outline-shape-secondary {
            stroke: #3B82F6; /* 使用强调蓝 */
            stroke-width: 2;
            fill: none;
        }
    </style>

    <!-- 背景层：纯黑到深蓝渐变，营造科技感和专业度 -->
    <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

    <!-- 装饰性背景元素：几何图形和渐变，增加视觉深度 -->
    <g opacity="0.15">
        <rect x="150" y="100" width="400" height="700" fill="url(#accentGradientRed)" transform="rotate(25 150 100)"/>
        <circle cx="1700" cy="800" r="300" fill="url(#cardGradient)" transform="rotate(45 1700 800)"/>
        <path d="M0 850 L500 750 L1000 1080 L0 1080 Z" fill="#1E40AF" opacity="0.1"/>
        <path d="M1920 200 L1420 300 L920 0 L1920 0 Z" fill="#475569" opacity="0.1"/>
    </g>

    <!-- 左上角Logo和页面序号 -->
    <g class="header-section">
        <image x="80" y="60" width="120" height="auto" href="{logo_url}"/>
        <text x="80" y="200" class="small-text color-text-secondary font-secondary">
            <tspan>页面序号: 6/10</tspan>
        </text>
    </g>

    <!-- 主标题和副标题，居中显示，突出发布会主题 -->
    <g class="title-section">
        <text x="960" y="160" text-anchor="middle" class="hero-title color-accent-red font-primary" filter="url(#glowEffect)">
            <tspan>{title}</tspan>
        </text>
        <text x="960" y="220" text-anchor="middle" class="section-title color-text-secondary font-accent">
            <tspan>{subtitle}</tspan>
        </text>
    </g>

    <!-- 主要内容区域 - Bento Grid 布局 -->
    <!-- 左侧面板：大型数据图表区域 -->
    <g transform="translate(80, 280)" class="chart-panel">
        <rect x="0" y="0" width="1000" height="720" rx="12" class="card-bg"/>

        <!-- 图表标题 -->
        <text x="40" y="50" class="content-title color-text-light font-primary">
            <tspan>核心数据洞察</tspan>
        </text>
        <text x="40" y="85" class="small-text color-text-secondary font-accent">
            <tspan>Core Data Insights</tspan>
        </text>

        <!-- 超大数字突出关键信息 -->
        <text x="40" y="200" class="hero-title color-accent-red font-accent" filter="url(#glowEffect)">
            <tspan>99.8%</tspan>
        </text>
        <text x="40" y="245" class="content-title color-text-light font-primary">
            <tspan>增长率和市场份额</tspan>
        </text>
        <text x="40" y="280" class="small-text color-text-secondary font-secondary">
            <tspan>Growth Rate and Market Share</tspan>
        </text>

        <!-- 模拟折线图 -->
        <g transform="translate(80, 380)">
            <!-- X轴标签，确保与图表有足够间距 -->
            <text x="0" y="200" class="small-text color-text-secondary font-secondary">
                <tspan>Q1</tspan>
            </text>
            <text x="180" y="200" class="small-text color-text-secondary font-secondary">
                <tspan>Q2</tspan>
            </text>
            <text x="360" y="200" class="small-text color-text-secondary font-secondary">
                <tspan>Q3</tspan>
            </text>
            <text x="540" y="200" class="small-text color-text-secondary font-secondary">
                <tspan>Q4</tspan>
            </text>

            <!-- Y轴标签，确保与图表有足够间距 -->
            <text x="-40" y="160" text-anchor="end" class="small-text color-text-secondary font-secondary">
                <tspan>100%</tspan>
            </text>
            <text x="-40" y="110" text-anchor="end" class="small-text color-text-secondary font-secondary">
                <tspan>75%</tspan>
            </text>
            <text x="-40" y="60" text-anchor="end" class="small-text color-text-secondary font-secondary">
                <tspan>50%</tspan>
            </text>
            <text x="-40" y="10" text-anchor="end" class="small-text color-text-secondary font-secondary">
                <tspan>25%</tspan>
            </text>

            <!-- 网格线 -->
            <line x1="0" y1="170" x2="600" y2="170" class="stroke-blue" opacity="0.2"/>
            <line x1="0" y1="120" x2="600" y2="120" class="stroke-blue" opacity="0.2"/>
            <line x1="0" y1="70" x2="600" y2="70" class="stroke-blue" opacity="0.2"/>
            <line x1="0" y1="20" x2="600" y2="20" class="stroke-blue" opacity="0.2"/>

            <!-- 数据线 1 (特斯拉红) -->
            <polyline points="0,160 200,90 400,130 600,70" class="outline-shape stroke-red" fill="none" stroke-width="4"/>
            <!-- 数据线 2 (强调蓝) -->
            <polyline points="0,110 200,140 400,80 600,110" class="outline-shape-secondary stroke-blue" fill="none" stroke-width="3"/>

            <!-- 数据点 -->
            <circle cx="0" cy="160" r="6" fill="#E31937"/>
            <circle cx="200" cy="90" r="6" fill="#E31937"/>
            <circle cx="400" cy="130" r="6" fill="#E31937"/>
            <circle cx="600" cy="70" r="6" fill="#E31937"/>

            <circle cx="0" cy="110" r="6" fill="#3B82F6"/>
            <circle cx="200" cy="140" r="6" fill="#3B82F6"/>
            <circle cx="400" cy="80" r="6" fill="#3B82F6"/>
            <circle cx="600" cy="110" r="6" fill="#3B82F6"/>

            <!-- 数据标签，确保不与图表元素重叠，与图表保持40px间距 -->
            <text x="200" y="90" text-anchor="middle" class="small-text color-accent-red font-secondary">
                <tspan dy="-25">80%</tspan>
            </text>
            <text x="400" y="130" text-anchor="middle" class="small-text color-accent-red font-secondary">
                <tspan dy="-25">120%</tspan>
            </text>
            <text x="600" y="70" text-anchor="middle" class="small-text color-accent-red font-secondary">
                <tspan dy="-25">60%</tspan>
            </text>

            <text x="200" y="140" text-anchor="middle" class="small-text color-accent-blue font-secondary">
                <tspan dy="25">130%</tspan>
            </text>
            <text x="400" y="80" text-anchor="middle" class="small-text color-accent-blue font-secondary">
                <tspan dy="25">70%</tspan>
            </text>
            <text x="600" y="110" text-anchor="middle" class="small-text color-accent-blue font-secondary">
                <tspan dy="25">100%</tspan>
            </text>
        </g>
    </g>

    <!-- 右侧面板：数据卡片组 (Bento Grid 风格) -->
    <g transform="translate(1120, 280)" class="data-cards-panel">
        <!-- 卡片 1 (大尺寸) -->
        <rect x="0" y="0" width="720" height="230" rx="12" class="card-bg"/>
        <use xlink:href="#icon-chart" x="40" y="40" width="32" height="32"/>
        <text x="90" y="65" class="content-title color-text-light font-primary">
            <tspan>市场影响力</tspan>
        </text>
        <text x="40" y="140" class="main-title color-accent-red font-accent">
            <tspan>+35%</tspan>
        </text>
        <text x="40" y="180" class="small-text color-text-secondary font-secondary">
            <tspan>过去12个月的品牌认知度提升</tspan>
        </text>
        <text x="40" y="210" class="small-text color-text-secondary font-accent">
            <tspan>Brand Awareness Growth in Last 12 Months</tspan>
        </text>

        <!-- 卡片 2 (中尺寸) -->
        <rect x="0" y="250" width="350" height="230" rx="12" class="card-bg"/>
        <use xlink:href="#icon-target" x="40" y="290" width="32" height="32"/>
        <text x="90" y="315" class="content-title color-text-light font-primary">
            <tspan>用户满意度</tspan>
        </text>
        <text x="40" y="390" class="main-title color-accent-red font-accent">
            <tspan>9.5/10</tspan>
        </text>
        <text x="40" y="430" class="small-text color-text-secondary font-secondary">
            <tspan>客户净推荐值</tspan>
        </text>
        <text x="40" y="460" class="small-text color-text-secondary font-accent">
            <tspan>Net Promoter Score</tspan>
        </text>

        <!-- 卡片 3 (中尺寸) -->
        <rect x="370" y="250" width="350" height="230" rx="12" class="card-bg"/>
        <use xlink:href="#icon-star" x="410" y="290" width="32" height="32"/>
        <text x="460" y="315" class="content-title color-text-light font-primary">
            <tspan>创新成果</tspan>
        </text>
        <text x="410" y="390" class="main-title color-accent-red font-accent">
            <tspan>12+</tspan>
        </text>
        <text x="410" y="430" class="small-text color-text-secondary font-secondary">
            <tspan>核心技术专利</tspan>
        </text>
        <text x="410" y="460" class="small-text color-text-secondary font-accent">
            <tspan>Core Technology Patents</tspan>
        </text>

        <!-- 卡片 4 (大尺寸) -->
        <rect x="0" y="500" width="720" height="220" rx="12" class="card-bg"/>
        <use xlink:href="#icon-lightbulb" x="40" y="540" width="32" height="32"/>
        <text x="90" y="565" class="content-title color-text-light font-primary">
            <tspan>未来展望</tspan>
        </text>
        <text x="40" y="640" class="body-text color-text-light font-primary">
            <tspan>我们致力于持续创新，为用户提供卓越的体验，</tspan>
            <tspan x="40" dy="30">并且引领行业发展。感谢您的关注。</tspan>
        </text>
        <text x="40" y="700" class="small-text color-text-secondary font-accent">
            <tspan>Committed to continuous innovation and industry leadership.</tspan>
        </text>
    </g>

    <!-- 底部日期和作者信息 -->
    <g class="footer-section">
        <text x="1840" y="1020" text-anchor="end" class="small-text color-text-secondary font-secondary">
            <tspan>{date}</tspan>
        </text>
        <text x="1840" y="1050" text-anchor="end" class="small-text color-text-secondary font-secondary">
            <tspan>{author}</tspan>
        </text>
    </g>

    <!-- 隐藏的占位符，确保所有内容占位符被包含 -->
    <text x="0" y="0" style="opacity:0;">
        <tspan>{content}</tspan>
        <tspan>{image_url}</tspan>
    </text>

</svg>