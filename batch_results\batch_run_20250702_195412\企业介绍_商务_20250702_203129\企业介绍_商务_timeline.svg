<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- 定义CSS样式，严格避免使用 & 符号 -->
    <style type="text/css">
      <![CDATA[
        /* 配色方案 */
        .bg-color { fill: #F8FAFC; }
        .primary-color { fill: #1E40AF; }
        .secondary-color { fill: #475569; }
        .accent-color { fill: #3B82F6; }
        .text-primary { fill: #1E293B; }
        .text-secondary { fill: #64748B; }
        .card-bg { fill: #FFFFFF; }
        .card-border { stroke: #BAE6FD; }
        .divider-color { stroke: #BAE6FD; }
        .icon-color { fill: #3B82F6; } /* 使用强调色作为图标颜色 */

        /* 字体系统 */
        .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
        .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
        .font-accent { font-family: 'Times New Roman', serif; }

        .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
        .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
        .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; } /* 用于里程碑标题 */
        .content-title { font-size: 28px; font-weight: 700; line-height: 1.4; } /* 用于普通事件标题 */
        .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
        .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; }
        .caption { font-size: 14px; font-weight: 400; line-height: 1.6; }

        /* 文本对齐 */
        .text-center { text-anchor: middle; }
        .text-left { text-anchor: start; }
        .text-right { text-anchor: end; }

        /* 卡片阴影效果 */
        .card-shadow { filter: url(#dropShadow); }

        /* 时间轴特定样式 */
        .timeline-line { stroke: #BAE6FD; stroke-width: 4px; stroke-linecap: round; }
        .timeline-node { fill: #3B82F6; stroke: #FFFFFF; stroke-width: 4px; } /* 普通时间节点 */
        .timeline-milestone { fill: #1E40AF; stroke: #3B82F6; stroke-width: 6px; } /* 里程碑节点，使用主色 */
      ]]>
    </style>

    <!-- 滤镜定义：阴影效果 -->
    <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feOffset dx="0" dy="4"/>
      <feGaussianBlur stdDeviation="3"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feBlend in="SourceGraphic" in2="BackgroundImage"/>
    </filter>
  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color"/>

  <!-- 头部区域：标题和副标题 -->
  <text x="960" y="100" class="main-title font-primary text-primary text-center">
    <tspan>{title}</tspan>
  </text>
  <text x="960" y="160" class="body-text font-secondary text-secondary text-center">
    <tspan>{subtitle}</tspan>
  </text>

  <!-- 时间轴主体布局 -->
  <!-- 中央时间轴线条 -->
  <line x1="960" y1="250" x2="960" y2="1000" class="timeline-line"/>

  <!-- 时间轴事件节点 -->

  <!-- 事件1: 左侧事件 -->
  <g class="timeline-event">
    <!-- 时间节点 -->
    <circle cx="960" cy="300" r="12" class="timeline-node"/>
    <!-- 事件卡片 -->
    <rect x="180" y="250" width="600" height="150" rx="12" ry="12" class="card-bg card-border" style="stroke-width: 1px; filter: url(#dropShadow);"/>
    <!-- 事件标题（中文大字体粗体） -->
    <text x="220" y="290" class="content-title font-primary text-primary text-left">
      <tspan>{date} - {title}</tspan>
    </text>
    <!-- 事件描述（英文小字点缀） -->
    <text x="220" y="330" class="body-text font-secondary text-secondary text-left">
      <tspan>{content}</tspan>
      <tspan x="220" dy="30">{author}</tspan>
    </text>
    <!-- 装饰图标（勾线图形化） -->
    <path d="M720 285 L740 285 L740 305 L720 305 Z M730 280 L730 310" stroke="#3B82F6" stroke-width="2" fill="none" />
  </g>

  <!-- 事件2: 右侧事件 (里程碑) -->
  <g class="timeline-event">
    <!-- 时间节点（里程碑特殊标注） -->
    <circle cx="960" cy="470" r="16" class="timeline-milestone"/>
    <!-- 事件卡片 -->
    <rect x="1140" y="420" width="600" height="150" rx="12" ry="12" class="card-bg card-border" style="stroke-width: 1px; filter: url(#dropShadow);"/>
    <!-- 事件标题（中文大字体粗体，更大号） -->
    <text x="1180" y="460" class="section-title font-primary text-primary text-left">
      <tspan>{date} - {title}</tspan>
    </text>
    <!-- 事件描述（英文小字点缀） -->
    <text x="1180" y="500" class="body-text font-secondary text-secondary text-left">
      <tspan>{content}</tspan>
      <tspan x="1180" dy="30">{author}</tspan>
    </text>
    <!-- 装饰图标（勾线图形化） -->
    <path d="M1680 455 C1680 455, 1700 435, 1720 455 C1720 455, 1700 475, 1680 455 Z" stroke="#3B82F6" stroke-width="2" fill="none" />
    <circle cx="1700" cy="455" r="5" fill="#3B82F6" />
  </g>

  <!-- 事件3: 左侧事件 -->
  <g class="timeline-event">
    <!-- 时间节点 -->
    <circle cx="960" cy="640" r="12" class="timeline-node"/>
    <!-- 事件卡片 -->
    <rect x="180" y="590" width="600" height="150" rx="12" ry="12" class="card-bg card-border" style="stroke-width: 1px; filter: url(#dropShadow);"/>
    <!-- 事件标题（中文大字体粗体） -->
    <text x="220" y="630" class="content-title font-primary text-primary text-left">
      <tspan>{date} - {title}</tspan>
    </text>
    <!-- 事件描述（英文小字点缀） -->
    <text x="220" y="670" class="body-text font-secondary text-secondary text-left">
      <tspan>{content}</tspan>
      <tspan x="220" dy="30">{author}</tspan>
    </text>
    <!-- 装饰图标（勾线图形化） -->
    <path d="M720 625 L740 625 L740 645 L720 645 Z" stroke="#3B82F6" stroke-width="2" fill="none" />
    <line x1="720" y1="625" x2="740" y2="645" stroke="#3B82F6" stroke-width="2" />
    <line x1="740" y1="625" x2="720" y2="645" stroke="#3B82F6" stroke-width="2" />
  </g>

  <!-- 事件4: 右侧事件 -->
  <g class="timeline-event">
    <!-- 时间节点 -->
    <circle cx="960" cy="810" r="12" class="timeline-node"/>
    <!-- 事件卡片 -->
    <rect x="1140" y="760" width="600" height="150" rx="12" ry="12" class="card-bg card-border" style="stroke-width: 1px; filter: url(#dropShadow);"/>
    <!-- 事件标题（中文大字体粗体） -->
    <text x="1180" y="800" class="content-title font-primary text-primary text-left">
      <tspan>{date} - {title}</tspan>
    </text>
    <!-- 事件描述（英文小字点缀） -->
    <text x="1180" y="840" class="body-text font-secondary text-secondary text-left">
      <tspan>{content}</tspan>
      <tspan x="1180" dy="30">{author}</tspan>
    </text>
    <!-- 装饰图标（勾线图形化） -->
    <path d="M1680 795 C1680 795, 1700 780, 1720 795 C1720 795, 1700 810, 1680 795 Z" stroke="#3B82F6" stroke-width="2" fill="none" />
    <path d="M1700 785 L1700 805" stroke="#3B82F6" stroke-width="2" />
  </g>

  <!-- 事件5: 左侧事件 (里程碑) -->
  <g class="timeline-event">
    <!-- 时间节点（里程碑特殊标注） -->
    <circle cx="960" cy="980" r="16" class="timeline-milestone"/>
    <!-- 事件卡片 -->
    <rect x="180" y="930" width="600" height="150" rx="12" ry="12" class="card-bg card-border" style="stroke-width: 1px; filter: url(#dropShadow);"/>
    <!-- 事件标题（中文大字体粗体，更大号） -->
    <text x="220" y="970" class="section-title font-primary text-primary text-left">
      <tspan>{date} - {title}</tspan>
    </text>
    <!-- 事件描述（英文小字点缀） -->
    <text x="220" y="1010" class="body-text font-secondary text-secondary text-left">
      <tspan>{content}</tspan>
      <tspan x="220" dy="30">{author}</tspan>
    </text>
    <!-- 装饰图标（勾线图形化） -->
    <path d="M720 965 L740 965 L740 985 L720 985 Z M720 975 L740 975" stroke="#3B82F6" stroke-width="2" fill="none" />
  </g>

</svg>