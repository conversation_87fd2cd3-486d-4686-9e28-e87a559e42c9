<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Define a linear gradient for subtle background -->
    <linearGradient id="backgroundGradient" x1="960" y1="0" x2="960" y2="1080" gradientUnits="userSpaceOnUse">
      <stop stop-color="#F8FAFC"/>
      <stop offset="1" stop-color="#E0F2FE"/>
    </linearGradient>
  </defs>

  <style>
    /* Global Styles */
    :root {
      --primary-color: #1E40AF;
      --secondary-color: #475569;
      --accent-color: #3B82F6;
      --background-color: #F8FAFC;
      --text-primary: #1E293B;
      --text-secondary: #64748B;
      --card-background: #FFFFFF;
      --card-border: #BAE6FD;
      --container-background: #E0F2FE;
    }

    /* Font Definitions */
    .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
    .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
    .font-accent { font-family: 'Times New Roman', serif; }

    /* Text Styles */
    .hero-title { font-size: 72px; font-weight: 700; fill: var(--text-primary); }
    .main-title { font-size: 56px; font-weight: 700; fill: var(--text-primary); }
    .section-title { font-size: 36px; font-weight: 600; fill: var(--text-primary); }
    .content-title { font-size: 28px; font-weight: 500; fill: var(--text-primary); }
    .body-text { font-size: 22px; font-weight: 400; fill: var(--text-secondary); }
    .small-text { font-size: 16px; font-weight: 400; fill: var(--text-secondary); }
    .caption-text { font-size: 14px; font-weight: 400; fill: var(--text-secondary); }

    /* Colors */
    .fill-primary { fill: var(--primary-color); }
    .fill-secondary { fill: var(--secondary-color); }
    .fill-accent { fill: var(--accent-color); }
    .fill-background { fill: var(--background-color); }
    .fill-card-background { fill: var(--card-background); }
    .fill-container-background { fill: var(--container-background); }
    .text-primary-color { fill: var(--text-primary); }
    .text-secondary-color { fill: var(--text-secondary); }

    /* Strokes */
    .stroke-primary { stroke: var(--primary-color); }
    .stroke-accent { stroke: var(--accent-color); }
    .stroke-card-border { stroke: var(--card-border); }
    .stroke-text-primary { stroke: var(--text-primary); }

    /* Card Styling */
    .card {
      fill: var(--card-background);
      stroke: var(--card-border);
      stroke-width: 1px;
      rx: 12; /* border-radius */
      ry: 12; /* border-radius */
      filter: url(#dropShadow); /* Apply shadow */
    }

    /* Timeline Specific Styles */
    .timeline-line {
      stroke: var(--primary-color);
      stroke-width: 4px;
      stroke-linecap: round;
    }
    .timeline-node {
      fill: var(--primary-color);
      stroke: var(--card-background); /* White border around node */
      stroke-width: 4px;
    }
    .timeline-milestone-node {
      fill: var(--accent-color);
      stroke: var(--card-background);
      stroke-width: 4px;
    }
    .timeline-arrow {
      fill: var(--primary-color);
    }
    .timeline-card-title {
      font-size: 28px;
      font-weight: 600;
      fill: var(--primary-color);
    }
    .timeline-card-date {
      font-size: 22px;
      font-weight: 500;
      fill: var(--text-primary);
    }
    .timeline-card-content {
      font-size: 18px;
      font-weight: 400;
      fill: var(--text-secondary);
    }
    .timeline-milestone-label {
      font-size: 24px;
      font-weight: 700;
      fill: var(--accent-color);
    }

    /* Shadow filter definition */
    /* Note: drop-shadow is a CSS function, not a filter element. Using SVG filter for broader compatibility. */
    .shadow-filter {
      filter: url(#dropShadow);
    }

    /* Specific styles for the logo */
    .logo-container {
        filter: url(#logoShadow);
    }
  </style>

  <!-- Filters for shadows -->
  <filter id="dropShadow" x="-50%" y="-50%" width="200%" height="200%">
    <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4"/>
    <feGaussianBlur result="blurOut" in="offOut" stdDeviation="3"/>
    <feColorMatrix result="matrixOut" in="blurOut" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
    <feOffset result="offOut2" in="SourceAlpha" dx="0" dy="2"/>
    <feGaussianBlur result="blurOut2" in="offOut2" stdDeviation="2"/>
    <feColorMatrix result="matrixOut2" in="blurOut2" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0"/>
    <feMerge>
      <feMergeNode in="matrixOut"/>
      <feMergeNode in="matrixOut2"/>
      <feMergeNode in="SourceGraphic"/>
    </feMerge>
  </filter>

  <filter id="logoShadow" x="-50%" y="-50%" width="200%" height="200%">
    <feOffset result="offOut" in="SourceAlpha" dx="2" dy="2"/>
    <feGaussianBlur result="blurOut" in="offOut" stdDeviation="2"/>
    <feColorMatrix result="matrixOut" in="blurOut" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
    <feMerge>
      <feMergeNode in="matrixOut"/>
      <feMergeNode in="SourceGraphic"/>
    </feMerge>
  </filter>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

  <!-- Header -->
  <g class="header">
    <!-- Logo Placeholder -->
    <g class="logo-container">
        <rect x="80" y="60" width="160" height="40" fill="var(--primary-color)" rx="8" ry="8"/>
        <text x="160" y="87" text-anchor="middle" class="font-primary small-text" fill="white">
            <tspan>{logo_url}</tspan>
        </text>
    </g>

    <!-- Title and Subtitle -->
    <text x="960" y="160" text-anchor="middle" class="font-primary main-title text-primary-color">
      <tspan>{title}</tspan>
    </text>
    <text x="960" y="210" text-anchor="middle" class="font-secondary body-text text-secondary-color">
      <tspan>{subtitle}</tspan>
    </text>
  </g>

  <!-- Main Content - Timeline -->
  <g class="timeline-section">
    <!-- Timeline Line -->
    <line x1="960" y1="280" x2="960" y2="1000" class="timeline-line"/>

    <!-- Timeline Arrow at the end -->
    <polygon points="960,1000 950,985 970,985" class="timeline-arrow"/>

    <!-- Timeline Nodes and Events -->

    <!-- Node 1 -->
    <circle cx="960" cy="350" r="12" class="timeline-node"/>
    <g class="shadow-filter">
      <rect x="1000" y="300" width="700" height="150" class="card"/>
    </g>
    <text x="1030" y="330" class="font-primary timeline-card-date text-primary-color">
      <tspan>{date} - 临床研究启动</tspan>
    </text>
    <text x="1030" y="365" class="font-secondary timeline-card-content text-secondary-color">
      <tspan>{content}</tspan>
      <tspan x="1030" dy="30">详细描述了第一阶段的临床研究目标和初步成果。</tspan>
    </text>

    <!-- Node 2 (Milestone) -->
    <circle cx="960" cy="500" r="16" class="timeline-milestone-node"/>
    <g class="shadow-filter">
      <rect x="220" y="450" width="700" height="150" class="card"/>
    </g>
    <text x="890" y="480" text-anchor="end" class="font-primary timeline-card-date text-primary-color">
      <tspan>{date} - 重大突破</tspan>
    </text>
    <text x="890" y="515" text-anchor="end" class="font-secondary timeline-card-content text-secondary-color">
      <tspan>{content}</tspan>
      <tspan x="890" dy="30">里程碑事件，取得了关键的治疗方案进展。</tspan>
    </text>
    <!-- Milestone label slightly below the node -->
    <text x="960" y="535" text-anchor="middle" class="font-primary timeline-milestone-label">
      <tspan>里程碑</tspan>
    </text>

    <!-- Node 3 -->
    <circle cx="960" cy="650" r="12" class="timeline-node"/>
    <g class="shadow-filter">
      <rect x="1000" y="600" width="700" height="150" class="card"/>
    </g>
    <text x="1030" y="630" class="font-primary timeline-card-date text-primary-color">
      <tspan>{date} - 数据分析完成</tspan>
    </text>
    <text x="1030" y="665" class="font-secondary timeline-card-content text-secondary-color">
      <tspan>{content}</tspan>
      <tspan x="1030" dy="30">对收集到的临床数据进行了深入分析和报告。</tspan>
    </text>

    <!-- Node 4 -->
    <circle cx="960" cy="800" r="12" class="timeline-node"/>
    <g class="shadow-filter">
      <rect x="220" y="750" width="700" height="150" class="card"/>
    </g>
    <text x="890" y="780" text-anchor="end" class="font-primary timeline-card-date text-primary-color">
      <tspan>{date} - 临床应用推广</tspan>
    </text>
    <text x="890" y="815" text-anchor="end" class="font-secondary timeline-card-content text-secondary-color">
      <tspan>{content}</tspan>
      <tspan x="890" dy="30">治疗方案开始在多个医疗机构推广应用。</tspan>
    </text>

    <!-- Node 5 -->
    <circle cx="960" cy="950" r="12" class="timeline-node"/>
    <g class="shadow-filter">
      <rect x="1000" y="900" width="700" height="150" class="card"/>
    </g>
    <text x="1030" y="930" class="font-primary timeline-card-date text-primary-color">
      <tspan>{date} - 未来展望</tspan>
    </text>
    <text x="1030" y="965" class="font-secondary timeline-card-content text-secondary-color">
      <tspan>{content}</tspan>
      <tspan x="1030" dy="30">展望未来的研究方向和发展计划。</tspan>
    </text>

  </g>

  <!-- Decorative Elements / Icons -->
  <g class="decorative-elements">
    <!-- Medical Cross Icon (top left) -->
    <path d="M170 280 H190 V300 H170 V320 H150 V300 H130 V280 H150 V260 H170 Z" fill="var(--accent-color)" opacity="0.15"/>
    <path d="M170 280 H190 V300 H170 V320 H150 V300 H130 V280 H150 V260 H170 Z" fill="none" stroke="var(--accent-color)" stroke-width="2" opacity="0.3"/>

    <!-- Beaker Icon (top right) -->
    <path d="M1750 280 H1790 L1780 320 C1780 330 1770 340 1760 340 H1740 C1730 340 1720 330 1720 320 L1710 280 H1750 Z M1730 340 V360 C1730 370 1740 380 1750 380 C1760 380 1770 370 1770 360 V340" fill="var(--primary-color)" opacity="0.15"/>
    <path d="M1750 280 H1790 L1780 320 C1780 330 1770 340 1760 340 H1740 C1730 340 1720 330 1720 320 L1710 280 H1750 Z M1730 340 V360 C1730 370 1740 380 1750 380 C1760 380 1770 370 1770 360 V340" fill="none" stroke="var(--primary-color)" stroke-width="2" opacity="0.3"/>

    <!-- Abstract Wave/Data Line (bottom left) -->
    <path d="M80 800 C150 780, 200 850, 280 830 S350 750, 420 780 S500 850, 580 830" fill="none" stroke="var(--accent-color)" stroke-width="3" opacity="0.4"/>

    <!-- Abstract Grid/Bento-like element (bottom right) -->
    <rect x="1700" y="850" width="100" height="100" rx="10" ry="10" fill="var(--primary-color)" opacity="0.05"/>
    <rect x="1720" y="870" width="60" height="60" rx="8" ry="8" fill="var(--primary-color)" opacity="0.1"/>
    <rect x="1740" y="890" width="20" height="20" rx="4" ry="4" fill="var(--primary-color)" opacity="0.15"/>

  </g>

</svg>