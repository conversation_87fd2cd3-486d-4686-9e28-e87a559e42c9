<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- 样式定义 -->
    <style>
      /* 字体定义 */
      @font-face {
        font-family: "Microsoft YaHei";
        src: local("Microsoft YaHei"), local("MicrosoftYaHei");
        unicode-range: U+4E00-9FFF; /* 中文范围 */
      }
      @font-face {
        font-family: "Segoe UI";
        src: local("Segoe UI"), local("SegoeUI");
        unicode-range: U+0000-007F, U+0080-00FF, U+0100-024F, U+0250-02AF, U+02B0-02FF, U+0300-036F, U+0370-03FF, U+0400-04FF, U+0500-052F, U+0530-058F, U+0590-05FF, U+0600-06FF, U+0700-074F, U+0750-077F, U+0780-07BF, U+07C0-07FF, U+0800-083F, U+0840-085F, U+0860-087F, U+0880-08AF, U+08B0-08FF, U+0900-097F, U+0980-09FF, U+0A00-0A7F, U+0A80-0AFF, U+0B00-0B7F, U+0B80-0BFF, U+0C00-0C7F, U+0C80-0CFF, U+0D00-0D7F, U+0D80-0DFF, U+0E00-0E7F, U+0E80-0EFF, U+0F00-0FFF, U+1000-109F, U+10A0-10FF, U+1100-11FF, U+1200-137F, U+1380-139F, U+13A0-13FF, U+1400-167F, U+1680-169F, U+16A0-16FF, U+1700-171F, U+1720-173F, U+1740-175F, U+1760-177F, U+1780-17FF, U+1800-18AF, U+18B0-18FF, U+1900-194F, U+1950-197F, U+1980-19DF, U+19E0-19FF, U+1A00-1A1F, U+1A20-1AAF, U+1AB0-1AFF, U+1B00-1B7F, U+1B80-1BBF, U+1BC0-1BFF, U+1C00-1C4F, U+1C50-1C7F, U+1C80-1C8F, U+1CC0-1CCF, U+1CD0-1CFF, U+1D00-1D7F, U+1D80-1DBF, U+1DC0-1DFF, U+1E00-1EFF, U+1F00-1FFF, U+2000-206F, U+2070-209F, U+20A0-20CF, U+20D0-20FF, U+2100-214F, U+2150-218F, U+2190-21FF, U+2200-22FF, U+2300-23FF, U+2400-243F, U+2440-245F, U+2460-24FF, U+2500-257F, U+2580-259F, U+25A0-25FF, U+2600-26FF, U+2700-27BF, U+27C0-27EF, U+27F0-27FF, U+2800-28FF, U+2900-297F, U+2980-29FF, U+2A00-2AFF, U+2B00-2BFF, U+2C00-2C5F, U+2C60-2C7F, U+2C80-2CFF, U+2D00-2D2F, U+2D30-2D7F, U+2D80-2DDF, U+2DE0-2DFF, U+2E00-2E7F, U+2E80-2EFF, U+2F00-2FDF, U+2FF0-2FFF, U+3000-303F, U+3040-309F, U+30A0-30FF, U+3100-312F, U+3130-318F, U+3190-319F, U+31A0-31BF, U+31C0-31EF, U+31F0-32FF, U+3300-33FF, U+3400-4DBF, U+4DC0-4DFF, U+A000-A48F, U+A490-A4CF, U+A4D0-A4FF, U+A500-A63F, U+A640-A69F, U+A6A0-A6FF, U+A700-A71F, U+A720-A7FF, U+A800-A82F, U+A830-A83F, U+A840-A87F, U+A880-A8FF, U+A900-A92F, U+A930-A95F, U+A960-A97F, U+A980-A9DF, U+A9E0-A9FF, U+AA00-AA5F, U+AA60-AA7F, U+AA80-AADF, U+AAE0-AAFF, U+AB00-AB2F, U+AB30-AB6F, U+AB70-ABBF, U+ABC0-ABFF, U+AC00-D7AF, U+D7B0-D7FF, U+D800-DB7F, U+DB80-DBFF, U+DC00-DFFF, U+E000-F8FF, U+F900-FAFF, U+FB00-FB4F, U+FB50-FDFF, U+FE00-FE0F, U+FE10-FE1F, U+FE20-FE2F, U+FE30-FE4F, U+FE50-FE6F, U+FE70-FEFF, U+FF00-FFEF, U+FFF0-FFFF, U+10000-1007F, U+10080-100FF, U+10100-1013F, U+10140-1018F, U+10190-101CF, U+101D0-101FF, U+10280-1029F, U+102A0-102FF, U+10300-1032F, U+10330-1034F, U+10350-1037F, U+10380-1039F, U+103A0-103DF, U+10400-1044F, U+10450-1047F, U+10480-104AF, U+10500-1052F, U+10530-1056F, U+10600-1077F, U+10800-1083F, U+10840-1085F, U+10900-1091F, U+10920-1093F, U+10980-109FF, U+10A00-10A5F, U+10A60-10A7F, U+10A80-10A9F, U+10AC0-10AFF, U+10B00-10B3F, U+10B40-10B5F, U+10C00-10C4F, U+10C50-10C7F, U+10CC0-10CFF, U+10D00-10D3F, U+10E60-10E7F, U+10F00-10F2F, U+10F30-10F4F, U+10F70-10F8F, U+11000-1107F, U+11080-110CF, U+110D0-110FF, U+11100-1114F, U+11150-1117F, U+11180-111DF, U+111E0-111FF, U+11200-1124F, U+11280-112AF, U+112B0-112FF, U+11300-1132F, U+11330-1135F, U+11400-1147F, U+11480-114DF, U+11580-115FF, U+11600-1165F, U+11660-1167F, U+11680-116CF, U+11700-1173F, U+11800-118A7, U+118A8-118FF, U+11A00-11A4F, U+11A50-11AAF, U+11AB0-11AFF, U+11AC0-11AFF, U+11C00-11C6F, U+11C70-11C9F, U+11D00-11D5F, U+11D60-11DAF, U+11EE0-11EFF, U+11F00-11F5F, U+12000-123FF, U+12400-1247F, U+12480-1254F, U+13000-1342F, U+14400-1467F, U+16A40-16A6F, U+16AD0-16AFF, U+16B00-16B8F, U+16F00-16F9F, U+16FE0-16FFF, U+17000-187FF, U+18800-18AFF, U+1B000-1B0FF, U+1B100-1B12F, U+1B170-1B2FF, U+1BC00-1BC9F, U+1D000-1D0FF, U+1D100-1D1FF, U+1D200-1D24F, U+1D300-1D35F, U+1D360-1D37F, U+1D400-1D7FF, U+1D800-1DAAF, U+1DFB0-1DFFF, U+1E000-1E02F, U+1E100-1E14F, U+1E290-1E2BF, U+1E800-1E8DF, U+1E900-1E95F, U+1EE00-1EEFF, U+1F000-1F02F, U+1F030-1F09F, U+1F0A0-1F0FF, U+1F100-1F1FF, U+1F200-1F2FF, U+1F300-1F5FF, U+1F600-1F64F, U+1F650-1F67F, U+1F680-1F6FF, U+1F700-1F77F, U+1F780-1F7FF, U+1F800-1F8FF, U+1F900-1F9FF, U+1FA00-1FA6F, U+1FA70-1FAFF, U+1FB00-1FBFF, U+20000-2A6DF, U+2A700-2B73F, U+2B740-2B81F, U+2B820-2CEAF, U+2CEB0-2EBEF, U+2F800-2FA1F, U+E0000-E007F, U+E0100-E01EF; /* 英文及其他常用字符范围 */
      }

      .font-primary { font-family: "Microsoft YaHei", "Segoe UI", sans-serif; }
      .font-secondary { font-family: "Source Han Sans CN", "Noto Sans CJK SC", sans-serif; }

      /* 颜色定义 */
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .background-color { fill: #F8FAFC; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .card-background { fill: #FFFFFF; }
      .card-border { stroke: #BAE6FD; }
      .divider-color { stroke: #BAE6FD; }

      /* 字体大小和粗细 */
      .hero-title { font-size: 72px; font-weight: 700; } /* bold */
      .main-title { font-size: 56px; font-weight: 700; } /* bold */
      .section-title { font-size: 36px; font-weight: 700; } /* bold */
      .content-title { font-size: 28px; font-weight: 600; } /* semibold */
      .body-text { font-size: 22px; font-weight: 400; } /* normal */
      .small-text { font-size: 16px; font-weight: 400; } /* normal */
      .caption-text { font-size: 14px; font-weight: 400; } /* normal */
      .bold { font-weight: 700; }
      .semibold { font-weight: 600; }

      /* 卡片样式 */
      .card-style {
        fill: #FFFFFF;
        stroke: #BAE6FD;
        stroke-width: 1px;
        filter: url(#cardShadow);
      }

      /* 渐变定义 */
      <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="1">
        <stop offset="0%" stop-color="#1E40AF" />
        <stop offset="100%" stop-color="#475569" />
      </linearGradient>
      <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="0">
        <stop offset="0%" stop-color="#3B82F6" />
        <stop offset="100%" stop-color="#1E40AF" />
      </linearGradient>
      <linearGradient id="backgroundGradient" x1="0" y1="0" x2="0" y2="1">
        <stop offset="0%" stop-color="#F8FAFC" />
        <stop offset="100%" stop-color="#E0F2FE" />
      </linearGradient>
      <linearGradient id="textGradient" x1="0" y1="0" x2="1" y2="0">
        <stop offset="0%" stop-color="#1E3A8A" />
        <stop offset="100%" stop-color="#1E40AF" />
      </linearGradient>

      /* 阴影滤镜 */
      <filter id="cardShadow" x="-50%" y="-50%" width="200%" height="200%">
        <feDropShadow dx="0" dy="4" stdDeviation="6" flood-color="rgba(0, 0, 0, 0.1)"/>
        <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="rgba(0, 0, 0, 0.06)"/>
      </filter>
      <filter id="textShadow" x="-50%" y="-50%" width="200%" height="200%">
        <feGaussianBlur in="SourceAlpha" stdDeviation="2"/>
        <feOffset dx="0" dy="2"/>
        <feMerge>
          <feMergeNode/>
          <feMergeNode in="SourceGraphic"/>
        </feMerge>
      </filter>

    </style>
  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="1920" height="1080" class="background-color" />
  <!-- 或使用渐变背景 -->
  <!-- <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)" /> -->

  <!-- 顶部导航和Logo -->
  <image x="80" y="60" width="160" height="40" xlink:href="{logo_url}" />
  <text x="1760" y="85" text-anchor="end" class="text-secondary font-primary caption-text">7/10</text>

  <!-- 页面标题 -->
  <text x="960" y="160" text-anchor="middle" class="text-primary font-primary main-title">{title}</text>
  <text x="960" y="210" text-anchor="middle" class="text-secondary font-primary content-title">{subtitle}</text>

  <!-- 主要对比区域 -->
  <!-- 左侧对比卡片 A -->
  <rect x="160" y="290" width="760" height="480" rx="12" ry="12" class="card-style" />
  <text x="540" y="340" text-anchor="middle" class="text-primary font-primary section-title">产品A</text>
  <text x="540" y="385" text-anchor="middle" class="text-secondary font-primary small-text">Product A Overview</text>

  <!-- 左侧内容列表 -->
  <text x="200" y="440" class="text-primary font-primary body-text">
    <tspan x="200" dy="0">· 特点一：高性能处理，速度快</tspan>
    <tspan x="200" dy="35">· Feature 1: High-performance, fast processing</tspan>
    <tspan x="200" dy="45">· 特点二：紧凑设计，易于携带</tspan>
    <tspan x="200" dy="35">· Feature 2: Compact design, portable</tspan>
    <tspan x="200" dy="45">· 特点三：高能耗，电池续航一般</tspan>
    <tspan x="200" dy="35">· Feature 3: High power consumption, average battery</tspan>
  </text>

  <!-- 右侧对比卡片 B -->
  <rect x="1000" y="290" width="760" height="480" rx="12" ry="12" class="card-style" />
  <text x="1380" y="340" text-anchor="middle" class="text-primary font-primary section-title">产品B</text>
  <text x="1380" y="385" text-anchor="middle" class="text-secondary font-primary small-text">Product B Overview</text>

  <!-- 右侧内容列表 -->
  <text x="1040" y="440" class="text-primary font-primary body-text">
    <tspan x="1040" dy="0">· 特点一：高效能处理，能耗低</tspan>
    <tspan x="1040" dy="35">· Feature 1: High-efficiency, low power consumption</tspan>
    <tspan x="1040" dy="45">· 特点二：模块化设计，可扩展性强</tspan>
    <tspan x="1040" dy="35">· Feature 2: Modular design, strong scalability</tspan>
    <tspan x="1040" dy="45">· 特点三：长续航，绿色环保</tspan>
    <tspan x="1040" dy="35">· Feature 3: Long battery life, eco-friendly</tspan>
  </text>

  <!-- 中间差异点突出显示 -->
  <!-- 差异点几何图形 -->
  <rect x="940" y="380" width="40" height="180" rx="20" ry="20" fill="url(#accentGradient)" />
  <rect x="940" y="600" width="40" height="100" rx="20" ry="20" fill="url(#primaryGradient)" />

  <!-- 差异点文本和图标 -->
  <!-- 箭头图标 -->
  <path d="M960 410 L975 425 L960 440 M960 410 L945 425 L960 440" stroke="#FFFFFF" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
  <text x="960" y="460" text-anchor="middle" class="small-text bold" fill="#FFFFFF">性能</text>
  <text x="960" y="485" text-anchor="middle" class="caption-text" fill="#FFFFFF">Performance</text>

  <path d="M960 630 C950 630 950 640 960 640 C970 640 970 650 960 650 C950 650 950 660 960 660" stroke="#FFFFFF" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
  <text x="960" y="680" text-anchor="middle" class="small-text bold" fill="#FFFFFF">续航</text>
  <text x="960" y="705" text-anchor="middle" class="caption-text" fill="#FFFFFF">Battery</text>

  <!-- 结论区域 -->
  <rect x="160" y="820" width="1600" height="180" rx="12" ry="12" class="card-style" />
  <text x="960" y="860" text-anchor="middle" class="text-primary font-primary section-title">核心结论</text>
  <text x="960" y="905" text-anchor="middle" class="text-secondary font-primary body-text">
    <tspan x="960" dy="0">产品B在能效和模块化方面表现更优，而产品A则在极致性能上领先。</tspan>
    <tspan x="960" dy="35">Product B excels in energy efficiency and modularity, while Product A leads in ultimate performance.</tspan>
  </text>

  <!-- 底部日期和作者 -->
  <text x="80" y="1030" class="text-secondary font-primary small-text">{date}</text>
  <text x="1840" y="1030" text-anchor="end" class="text-secondary font-primary small-text">{author}</text>

</svg>