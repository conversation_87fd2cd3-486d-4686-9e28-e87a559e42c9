<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette - 统一蓝色系配色 -->
    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#1E3A8A"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>
    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E3A8A"/>
    </linearGradient>
    <linearGradient id="backgroundGradient" x1="0" y1="0" x2="0" y2="1">
      <stop offset="0%" stop-color="#F8FAFC"/>
      <stop offset="100%" stop-color="#E0F2FE"/>
    </linearGradient>
    <linearGradient id="cardBorderGradient" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#BAE6FD"/>
      <stop offset="100%" stop-color="#94A3B8"/>
    </linearGradient>

    <!-- Drop Shadow Filter for Cards -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="4"/>
      <feGaussianBlur stdDeviation="6"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Icon for comparison points: Checkmark (Success) -->
    <symbol id="checkIcon" viewBox="0 0 24 24">
      <path fill="#10B981" d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/>
    </symbol>
    <!-- Icon for comparison points: Cross (Error/Improvement Needed) -->
    <symbol id="crossIcon" viewBox="0 0 24 24">
      <path fill="#EF4444" d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
    </symbol>
    <!-- Icon for comparison points: Info (Accent) -->
    <symbol id="infoIcon" viewBox="0 0 24 24">
      <path fill="#3B82F6" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
    </symbol>
  </defs>

  <style>
    /* Global Styles */
    text {
      font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
      fill: #1E293B; /* text_primary */
    }

    /* Font System */
    .heroTitle {
      font-size: 72px;
      font-weight: 700; /* bold */
      fill: url(#primaryGradient);
    }
    .mainTitle {
      font-size: 56px;
      font-weight: 700;
      fill: #1E3A8A; /* primary_color */
    }
    .sectionTitle {
      font-size: 36px;
      font-weight: 600; /* semibold */
      fill: #1E3A8A;
    }
    .contentTitle {
      font-size: 28px;
      font-weight: 500; /* medium */
      fill: #1E293B;
    }
    .bodyText {
      font-size: 22px;
      font-weight: 400; /* normal */
      fill: #64748B; /* text_secondary */
    }
    .smallText {
      font-size: 16px;
      font-weight: 400;
      fill: #64748B;
    }
    .captionText {
      font-size: 14px;
      font-weight: 400;
      fill: #94A3B8; /* text_light */
    }
    .accentText {
      fill: #3B82F6; /* accent_color */
      font-weight: 700;
    }
    .primaryText {
      fill: #1E3A8A; /* primary_color */
      font-weight: 700;
    }

    /* Card Styles */
    .cardBackground {
      fill: #FFFFFF; /* card_background */
      stroke: url(#cardBorderGradient);
      stroke-width: 1px;
    }
    .cardShadow {
      filter: url(#shadow);
    }

    /* Divider Styles */
    .dividerLine {
      stroke: #BAE6FD;
      stroke-width: 1px;
    }

    /* Decorative Elements */
    .decorativeShapePrimary {
      fill: #1E3A8A;
      opacity: 0.05;
    }
    .decorativeShapeAccent {
      fill: #3B82F6;
      opacity: 0.05;
    }
  </style>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

  <!-- Decorative geometric elements (subtle) -->
  <rect x="1500" y="0" width="420" height="250" class="decorativeShapePrimary" rx="20"/>
  <circle cx="100" cy="980" r="120" class="decorativeShapeAccent"/>
  <path d="M0 800 Q 400 700 800 800 T 1600 800 T 1920 700 V 1080 H 0 Z" class="decorativeShapePrimary"/>


  <!-- Header Section -->
  <g id="header">
    <!-- Logo Placeholder -->
    <text x="80" y="90" class="contentTitle primaryText">
      <tspan>政务宣讲</tspan>
    </text>
    <text x="80" y="125" class="smallText">
      <tspan>GOVERNMENT PROPAGANDA</tspan>
    </text>

    <!-- Main Title -->
    <text x="960" y="100" text-anchor="middle" class="mainTitle">
      <tspan>{title}</tspan>
    </text>
    <text x="960" y="165" text-anchor="middle" class="contentTitle">
      <tspan>{subtitle}</tspan>
    </text>
  </g>

  <!-- Main Content - Comparison Section -->
  <g id="comparison_section">
    <!-- Left Column: Aspect A -->
    <g id="left_column">
      <rect x="120" y="250" width="800" height="600" rx="12" class="cardBackground cardShadow"/>
      <text x="520" y="300" text-anchor="middle" class="sectionTitle">
        <tspan>现有政策</tspan>
      </text>
      <text x="520" y="345" text-anchor="middle" class="smallText">
        <tspan>Current Policy Framework</tspan>
      </text>

      <!-- Content for Left Column -->
      <g class="bodyText">
        <text x="160" y="400">
          <tspan x="160" dy="0">1. 覆盖范围有限</tspan>
          <tspan x="160" dy="35">Limited coverage scope</tspan>
        </text>
        <text x="160" y="490">
          <tspan x="160" dy="0">2. 审批流程复杂</tspan>
          <tspan x="160" dy="35">Complex approval process</tspan>
        </text>
        <text x="160" y="580">
          <tspan x="160" dy="0">3. 资金投入不足</tspan>
          <tspan x="160" dy="35">Insufficient funding</tspan>
        </text>
        <text x="160" y="670">
          <tspan x="160" dy="0">4. 宣传渠道单一</tspan>
          <tspan x="160" dy="35">Single publicity channel</tspan>
        </text>
        <text x="160" y="760">
          <tspan x="160" dy="0">5. 基层落实困难</tspan>
          <tspan x="160" dy="35">Difficult grassroots implementation</tspan>
        </text>
      </g>
    </g>

    <!-- Right Column: Aspect B -->
    <g id="right_column">
      <rect x="1000" y="250" width="800" height="600" rx="12" class="cardBackground cardShadow"/>
      <text x="1400" y="300" text-anchor="middle" class="sectionTitle">
        <tspan>优化方案</tspan>
      </text>
      <text x="1400" y="345" text-anchor="middle" class="smallText">
        <tspan>Optimized Solution Plan</tspan>
      </text>

      <!-- Content for Right Column -->
      <g class="bodyText">
        <text x="1040" y="400">
          <tspan x="1040" dy="0">1. 扩大受益群体</tspan>
          <tspan x="1040" dy="35">Expand beneficiary groups</tspan>
        </text>
        <text x="1040" y="490">
          <tspan x="1040" dy="0">2. 简化审批流程</tspan>
          <tspan x="1040" dy="35">Streamline approval process</tspan>
        </text>
        <text x="1040" y="580">
          <tspan x="1040" dy="0">3. 增加财政投入</tspan>
          <tspan x="1040" dy="35">Increase fiscal investment</tspan>
        </text>
        <text x="1040" y="670">
          <tspan x="1040" dy="0">4. 创新宣传渠道</tspan>
          <tspan x="1040" dy="35">Innovate publicity channels</tspan>
        </text>
        <text x="1040" y="760">
          <tspan x="1040" dy="0">5. 强化基层保障</tspan>
          <tspan x="1040" dy="35">Strengthen grassroots support</tspan>
        </text>
      </g>
    </g>

    <!-- Central Difference Emphasis -->
    <g id="difference_emphasis">
      <line x1="960" y1="280" x2="960" y2="820" class="dividerLine"/>
      <text x="960" y="400" text-anchor="middle" class="accentText" style="font-size: 30px;">
        <tspan>主要差异</tspan>
      </text>
      <text x="960" y="440" text-anchor="middle" class="smallText">
        <tspan>Key Differences</tspan>
      </text>

      <!-- Icons indicating change (using info, cross, check for visual distinction) -->
      <use xlink:href="#infoIcon" x="936" y="500" width="48" height="48"/>
      <use xlink:href="#checkIcon" x="936" y="580" width="48" height="48"/>
      <use xlink:href="#infoIcon" x="936" y="660" width="48" height="48"/>
      <use xlink:href="#checkIcon" x="936" y="740" width="48" height="48"/>
    </g>
  </g>

  <!-- Conclusion Section -->
  <g id="conclusion_section">
    <rect x="80" y="890" width="1760" height="150" rx="12" class="cardBackground cardShadow"/>
    <text x="960" y="930" text-anchor="middle" class="contentTitle primaryText">
      <tspan>结论总结</tspan>
    </text>
    <text x="960" y="970" text-anchor="middle" class="smallText">
      <tspan>Conclusion Summary</tspan>
    </text>
    <text x="960" y="1015" text-anchor="middle" class="bodyText">
      <tspan>{content}</tspan>
    </text>
  </g>

  <!-- Footer -->
  <g id="footer">
    <text x="80" y="1050" class="captionText">
      <tspan>{date}</tspan>
    </text>
    <text x="1840" y="1050" text-anchor="end" class="captionText">
      <tspan>页面 7/10</tspan>
    </text>
  </g>
</svg>