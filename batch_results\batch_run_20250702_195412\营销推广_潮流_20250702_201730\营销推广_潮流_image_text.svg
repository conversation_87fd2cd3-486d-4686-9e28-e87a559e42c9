<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A86E8" />
      <stop offset="100%" style="stop-color:#3B82F6" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0EA5E9" />
      <stop offset="100%" style="stop-color:#4A86E8" />
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1E3A8A" />
      <stop offset="100%" style="stop-color:#1E40AF" />
    </linearGradient>
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#F8FAFC" />
      <stop offset="100%" style="stop-color:#E0F2FE" />
    </linearGradient>

    <!-- 阴影滤镜定义 -->
    <filter id="cardShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="6" flood-color="rgba(0, 0, 0, 0.1)" />
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="rgba(0, 0, 0, 0.06)" />
    </filter>
    <filter id="textShadow" x="-20%" y="-20%" width="140%" height="140%">
        <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="rgba(0, 0, 0, 0.15)"/>
    </filter>

    <!-- 图片裁剪路径 -->
    <clipPath id="imageClip">
      <rect x="140" y="290" width="800" height="600" rx="8" ry="8"/>
    </clipPath>

    <!-- 装饰性波浪线 -->
    <path id="wavePath" d="M0,0 C100,50 300,-50 400,0 C500,50 700,-50 800,0" />
  </defs>

  <!-- 全局样式定义 -->
  <style>
    /* 字体定义 */
    .font-primary { font-family: "Microsoft YaHei", "Segoe UI", sans-serif; }
    .font-secondary { font-family: "Source Han Sans CN", "Noto Sans CJK SC", sans-serif; }
    .font-accent { font-family: "Times New Roman", serif; }

    /* 文本颜色 */
    .text-primary-color { fill: #1E293B; }
    .text-secondary-color { fill: #64748B; }
    .text-light-color { fill: #94A3B8; }
    .text-white-color { fill: #FFFFFF; }

    /* 字体大小和粗细 */
    .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
    .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
    .section-title { font-size: 36px; font-weight: 600; line-height: 1.4; }
    .content-title { font-size: 28px; font-weight: 500; line-height: 1.4; }
    .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
    .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; }
    .caption { font-size: 14px; font-weight: 400; line-height: 1.6; }

    /* 卡片和背景样式 */
    .card-background { fill: #FFFFFF; stroke: #BAE6FD; stroke-width: 1px; filter: url(#cardShadow); }
    .container-background { fill: #E0F2FE; }

    /* 按钮样式 */
    .button-primary { fill: url(#accentGradient); cursor: pointer; }
    .button-text { fill: #FFFFFF; font-size: 22px; font-weight: 600; text-anchor: middle; }

    /* 装饰元素颜色 */
    .decorative-primary { fill: url(#primaryGradient); }
    .decorative-accent { fill: url(#accentGradient); }
    .decorative-line { stroke: #BAE6FD; stroke-width: 1px; }

    /* 确保文本不重叠的行高 */
    .line-spacing-normal { line-height: 1.4; } /* For multi-tspan, use dy */
  </style>

  <!-- 背景 -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)" />

  <!-- 顶部Logo占位符 -->
  <text x="80" y="100" class="font-primary section-title text-primary-color">
    <tspan>您的品牌 Logo</tspan>
  </text>

  <!-- 装饰性几何图形 (左上角) -->
  <circle cx="200" cy="200" r="80" fill="url(#accentGradient)" opacity="0.1" />
  <rect x="50" y="150" width="100" height="100" rx="12" ry="12" fill="url(#primaryGradient)" opacity="0.08" transform="rotate(15 100 200)" />

  <!-- 主内容区域 - 图片与文字平衡布局 -->
  <g transform="translate(80, 180)"> <!-- 整个内容块的起始点 (左边距80, 顶部偏移180) -->

    <!-- 左侧图片展示区域 -->
    <g transform="translate(50, 0)"> <!-- 图片区域内部偏移 -->
      <!-- 图片框架 -->
      <rect x="0" y="0" width="820" height="620" rx="12" ry="12" class="card-background" />
      <!-- 图片占位符 (使用clipPath裁剪) -->
      <image xlink:href="{image_url}" x="10" y="10" width="800" height="600" clip-path="url(#imageClip)" />
      <!-- 图片上的叠加层，增强视觉效果 -->
      <rect x="10" y="10" width="800" height="600" rx="8" ry="8" fill="url(#primaryGradient)" opacity="0.05" />
    </g>

    <!-- 右侧文字说明区域 -->
    <g transform="translate(1050, 0)"> <!-- 文字区域内部偏移 (820 + 180 = 1000px 间距 from image block start) -->
      <!-- 主标题 -->
      <text x="0" y="0" class="font-primary main-title text-primary-color" filter="url(#textShadow)">
        <tspan>{title}</tspan>
      </text>

      <!-- 副标题 -->
      <text x="0" y="80" class="font-secondary section-title text-secondary-color">
        <tspan>{subtitle}</tspan>
      </text>

      <!-- 正文内容 -->
      <text x="0" y="180" class="font-secondary body-text text-primary-color">
        <tspan x="0" dy="0">{content}</tspan>
        <tspan x="0" dy="30">产品特色：我们提供创新的解决方案和卓越的用户体验。</tspan>
        <tspan x="0" dy="30">市场优势：凭借领先技术和深入洞察，助您抢占先机。</tspan>
        <tspan x="0" dy="30">用户价值：为客户创造真实价值，提升效率和满意度。</tspan>
        <tspan x="0" dy="30">立即体验，感受非凡！</tspan>
      </text>

      <!-- 超大数字或标题突出核心要点 -->
      <text x="0" y="520" class="font-accent hero-title" fill="url(#textGradient)">
        <tspan>10X</tspan>
      </text>
      <text x="250" y="520" class="font-primary content-title text-secondary-color">
        <tspan>性能提升</tspan>
      </text>
      <text x="0" y="560" class="font-secondary small-text text-light-color">
        <tspan>Accelerate Your Growth</tspan>
      </text>

      <!-- CTA 按钮 -->
      <rect x="0" y="660" width="280" height="60" rx="30" ry="30" class="button-primary" />
      <text x="140" y="700" class="font-primary button-text">
        <tspan>了解更多</tspan>
      </text>
    </g>
  </g>

  <!-- 底部装饰性元素 -->
  <g transform="translate(0, 950)">
    <!-- 渐变分割线 -->
    <rect x="80" y="0" width="1760" height="2" fill="url(#accentGradient)" />
    <!-- 几何图形 -->
    <rect x="1600" y="30" width="100" height="100" fill="#0EA5E9" opacity="0.1" transform="rotate(-30 1650 80)" />
    <circle cx="1800" cy="80" r="50" fill="#4A86E8" opacity="0.08" />
  </g>

  <!-- 底部信息 -->
  <text x="80" y="1040" class="font-secondary small-text text-light-color">
    <tspan>日期: {date}</tspan>
    <tspan x="200" dy="0">作者: {author}</tspan>
  </text>
  <text x="1700" y="1040" text-anchor="end" class="font-secondary small-text text-light-color">
    <tspan>页面 5 和 10</tspan>
  </text>

</svg>