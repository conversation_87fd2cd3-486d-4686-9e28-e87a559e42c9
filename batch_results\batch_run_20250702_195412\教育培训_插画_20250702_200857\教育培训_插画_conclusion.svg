<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Colors -->
    <style type="text/css">
      <![CDATA[
      .bg-color { fill: #F8FAFC; }
      .primary-color { fill: #4A86E8; }
      .secondary-color { fill: #3B82F6; }
      .accent-color { fill: #0EA5E9; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .card-background { fill: #FFFFFF; }
      .card-border { stroke: #BAE6FD; }
      .icon-color { stroke: #4A86E8; fill: none; stroke-width: 2; }

      /* Font Styles */
      .font-primary { font-family: Microsoft YaHei, Segoe UI, sans-serif; }
      .font-secondary { font-family: Source <PERSON>, Not<PERSON> Sans C<PERSON>, sans-serif; }
      .font-accent { font-family: Times New Roman, serif; }

      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; letter-spacing: -0.025em; }
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
      .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; }
      .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; }
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; }
      .caption-text { font-size: 14px; font-weight: 400; line-height: 1.6; }

      /* Card Style */
      /* Note: SVG filters are applied directly to elements, not via CSS classes for shadow. */
      /* No SCSS & selector here, just standard CSS */
      .hover-effect:hover {
        transform: scale(1.02); /* Example for potential interactive elements, not directly visible in static SVG */
      }
      ]]>
    </style>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4A86E8" />
      <stop offset="100%" stop-color="#3B82F6" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#0EA5E9" />
      <stop offset="100%" stop-color="#4A86E8" />
    </linearGradient>
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#F8FAFC" />
      <stop offset="100%" stop-color="#E0F2FE" />
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#1E3A8A" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>

    <!-- Shadow Filter for Cards -->
    <filter id="shadowFilter" x="-5%" y="-5%" width="110%" height="110%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4" />
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="3" />
      <feColorMatrix result="matrixOut" in="blurOut" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" />
      <feMerge>
        <feMergeNode in="matrixOut" />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>

    <!-- Icon Definitions (simple outlines for educational theme) -->
    <!-- Lightbulb Icon (24x24 px) -->
    <g id="iconLightbulb" class="icon-color">
      <path d="M12 2C6.48 2 2 6.48 2 12C2 16.2 4.69 19.83 8.33 21.28C8.75 21.43 9 21.84 9 22.31V23H15V22.31C15 21.84 15.25 21.43 15.67 21.28C19.31 19.83 22 16.2 22 12C22 6.48 17.52 2 12 2Z" />
      <path d="M12 18V21" />
      <path d="M9 21H15" />
    </g>
    <!-- Book Icon (24x24 px) -->
    <g id="iconBook" class="icon-color">
      <path d="M4 19V3H20V19L12 15L4 19Z" />
      <path d="M4 19V21C4 21.5523 4.44772 22 5 22H19C19.5523 22 20 21.5523 20 21V19" />
    </g>
    <!-- Graduation Cap Icon (24x24 px) -->
    <g id="iconGraduationCap" class="icon-color">
      <path d="M22 12L12 5L2 12L12 19L22 12Z" />
      <path d="M2 12V20C2 20.5523 2.44772 21 3 21H21C21.5523 21 22 20.5523 22 20V12" />
      <path d="M12 5V21" />
    </g>
    <!-- Mail Icon (24x24 px) -->
    <g id="iconMail" class="icon-color">
      <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" />
      <path d="M22 6L12 13L2 6" />
    </g>
    <!-- Phone Icon (24x24 px) -->
    <g id="iconPhone" class="icon-color">
      <path d="M22 16.5V19C22 19.5523 21.5523 20 21 20H19C18.4477 20 18 19.5523 18 19V16.5C18 15.9477 18.4477 15.5 19 15.5H20C20.5523 15.5 21 15.9477 21 16.5V17.5C21 18.0523 20.5523 18.5 20 18.5H18" />
      <path d="M16 4.5V2C16 1.44772 15.5523 1 15 1H13C12.4477 1 12 1.44772 12 2V4.5C12 5.05228 12.4477 5.5 13 5.5H14C14.5523 5.5 15 5.05228 15 4.5V3.5C15 2.94772 15.5523 2.5 16 2.5H18" />
      <path d="M14.5 11.5C14.5 11.5 10 9 7 12C4 15 6.5 19.5 6.5 19.5" />
    </g>
  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)" />

  <!-- Decorative elements (subtle, abstract shapes) -->
  <circle cx="1700" cy="150" r="80" fill="#4A86E8" opacity="0.1" />
  <rect x="1650" y="800" width="150" height="100" rx="20" fill="#0EA5E9" opacity="0.08" />
  <path d="M0 850 C200 950 400 800 600 900 L600 1080 L0 1080 Z" fill="#3B82F6" opacity="0.05" />
  <path d="M1920 250 C1720 150 1520 300 1320 200 L1320 0 L1920 0 Z" fill="#4A86E8" opacity="0.05" />

  <!-- Main Content Area (with 80px horizontal and 60px vertical margins) -->
  <g transform="translate(80, 60)">
    <!-- Placeholder for Logo (Top Left) -->
    <g transform="translate(0, -30)">
      <!-- Example Logo Placeholder (replace with actual image if needed) -->
      <rect x="0" y="0" width="120" height="40" fill="#4A86E8" rx="8" />
      <text x="10" y="28" class="content-title font-primary" fill="#FFFFFF">
        <tspan>{logo_url}</tspan>
      </text>
    </g>

    <!-- Page Title -->
    <text x="0" y="80" class="main-title font-primary text-primary">
      <tspan>总结与展望：</tspan>
      <tspan class="font-secondary accent-color content-title" x="0" dy="50">{title}强化知识 和 激发行动</tspan>
    </text>

    <!-- Content Blocks -->
    <g transform="translate(0, 200)">
      <!-- Left Column: Main Conclusions Card -->
      <g>
        <rect x="0" y="0" width="800" height="500" rx="12" class="card-background" stroke="#BAE6FD" stroke-width="1" filter="url(#shadowFilter)" />
        <text x="40" y="60" class="section-title font-primary text-primary">
          <tspan>核心结论回顾</tspan>
        </text>

        <g transform="translate(40, 120)">
          <!-- Conclusion 1 -->
          <use xlink:href="#iconBook" x="0" y="-5" width="32" height="32" />
          <text x="50" y="0" class="body-text font-secondary text-primary">
            <tspan>1. 理论基础夯实：</tspan>
            <tspan class="text-secondary small-text" x="50" dy="30">{content}全面掌握核心概念和原理。</tspan>
          </text>
          <!-- Conclusion 2 -->
          <use xlink:href="#iconLightbulb" x="0" y="75" width="32" height="32" />
          <text x="50" y="75" class="body-text font-secondary text-primary">
            <tspan>2. 实践能力提升：</tspan>
            <tspan class="text-secondary small-text" x="50" dy="30">通过案例分析和项目实践，技能显著提高。</tspan>
          </text>
          <!-- Conclusion 3 -->
          <use xlink:href="#iconGraduationCap" x="0" y="150" width="32" height="32" />
          <text x="50" y="150" class="body-text font-secondary text-primary">
            <tspan>3. 互动参与积极：</tspan>
            <tspan class="text-secondary small-text" x="50" dy="30">讨论和分享促进了深度学习和协作。</tspan>
          </text>
          <!-- Conclusion 4 -->
          <text x="50" y="225" class="body-text font-secondary text-primary">
            <tspan>4. 持续学习展望：</tspan>
            <tspan class="text-secondary small-text" x="50" dy="30">为未来知识探索打下坚实基础。</tspan>
          </text>
        </g>
      </g>

      <!-- Right Column: Action Points Card -->
      <g transform="translate(900, 0)">
        <rect x="0" y="0" width="800" height="500" rx="12" class="card-background" stroke="#BAE6FD" stroke-width="1" filter="url(#shadowFilter)" />
        <text x="40" y="60" class="section-title font-primary text-primary">
          <tspan>行动建议</tspan>
          <tspan class="font-secondary accent-color content-title" x="40" dy="50">{subtitle}立即行动，成就未来！</tspan>
        </text>

        <g transform="translate(40, 160)">
          <!-- Action 1 -->
          <rect x="-20" y="-20" width="760" height="100" rx="8" fill="url(#accentGradient)" opacity="0.1" />
          <text x="0" y="0" class="content-title font-primary text-primary">
            <tspan>1. 巩固所学：</tspan>
            <tspan class="small-text text-secondary" x="0" dy="30">定期复习笔记和练习题。</tspan>
          </text>
          <!-- Action 2 -->
          <rect x="-20" y="100" width="760" height="100" rx="8" fill="url(#accentGradient)" opacity="0.1" />
          <text x="0" y="100" class="content-title font-primary text-primary">
            <tspan>2. 拓展实践：</tspan>
            <tspan class="small-text text-secondary" x="0" dy="30">将知识应用于实际项目或场景。</tspan>
          </text>
          <!-- Action 3 -->
          <rect x="-20" y="220" width="760" height="100" rx="8" fill="url(#accentGradient)" opacity="0.1" />
          <text x="0" y="220" class="content-title font-primary text-primary">
            <tspan>3. 持续探索：</tspan>
            <tspan class="small-text text-secondary" x="0" dy="30">关注行业动态，保持学习热情。</tspan>
          </text>
        </g>
      </g>
    </g>

    <!-- Bottom Section: Contact Info and Call to Action -->
    <g transform="translate(0, 750)">
      <rect x="0" y="0" width="1760" height="200" rx="12" class="card-background" stroke="#BAE6FD" stroke-width="1" filter="url(#shadowFilter)" />

      <!-- Contact Info -->
      <g transform="translate(80, 50)">
        <text x="0" y="0" class="content-title font-primary text-primary">
          <tspan>联系我们</tspan>
        </text>
        <g transform="translate(0, 40)">
          <use xlink:href="#iconMail" x="0" y="0" width="24" height="24" />
          <text x="40" y="18" class="body-text font-secondary text-secondary">
            <tspan>邮箱：<EMAIL></tspan>
          </text>
          <use xlink:href="#iconPhone" x="0" y="50" width="24" height="24" />
          <text x="40" y="68" class="body-text font-secondary text-secondary">
            <tspan>电话：+86 123 4567 8900</tspan>
          </text>
        </g>
      </g>

      <!-- Thank You and Call to Action -->
      <g transform="translate(900, 70)">
        <text x="0" y="0" class="section-title font-primary text-primary">
          <tspan>感谢您的参与！</tspan>
          <tspan class="accent-color hero-title" x="0" dy="80">{content}立即开始您的学习之旅！</tspan>
        </text>
      </g>
    </g>
  </g>
</svg>