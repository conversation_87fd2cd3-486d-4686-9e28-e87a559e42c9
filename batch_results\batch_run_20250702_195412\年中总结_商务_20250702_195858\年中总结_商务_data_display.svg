<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Colors from the provided palette -->
    <style type="text/css">
      <![CDATA[
        /* Background and base colors */
        .bg-color { fill: #F8FAFC; }
        .primary-color { fill: #1E40AF; }
        .secondary-color { fill: #475569; }
        .accent-color { fill: #3B82F6; }
        .card-bg { fill: #FFFFFF; }
        .card-border { stroke: #BAE6FD; }

        /* Text colors */
        .text-primary { fill: #1E293B; }
        .text-secondary { fill: #64748B; }
        .text-light { fill: #94A3B8; }
        .text-accent { fill: #3B82F6; } /* Added for accent text */
        .text-white { fill: #FFFFFF; } /* For header text */

        /* Font styles */
        .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
        .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
        .font-accent { font-family: 'Times New Roman', serif; }

        /* Font sizes and weights */
        .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; } /* bold */
        .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; } /* bold */
        .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; } /* bold */
        .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; } /* semibold */
        .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; } /* normal */
        .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; } /* normal */
        .caption { font-size: 14px; font-weight: 400; line-height: 1.6; } /* normal */
        .bold { font-weight: 700; }
        .semibold { font-weight: 600; }

        /* Card styles */
        .card-shadow { filter: url(#cardShadow); }
        .card-border-radius { rx: 12; ry: 12; }

        /* Icon styles */
        .icon-color { stroke: #4A86E8; stroke-width: 2; fill: none; } /* Using the specified icon color */

        /* Specific element styles */
        .chart-bar { fill: url(#barGradient); }
        .chart-axis-line { stroke: #94A3B8; stroke-width: 1; }
        .chart-label { fill: #64748B; font-size: 18px; font-weight: 400; }
        .chart-value { fill: #1E293B; font-size: 20px; font-weight: 600; }
      ]]>
    </style>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#475569" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>
    <linearGradient id="barGradient" x1="0" y1="0" x2="0" y2="1">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" stop-opacity="0.8" />
    </linearGradient>
    <linearGradient id="headerGradient" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#3B82F6" />
    </linearGradient>

    <!-- Filters for shadows -->
    <filter id="cardShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="4" />
      <feGaussianBlur stdDeviation="6" />
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0" />
      <feMerge>
        <feMergeNode />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>
    <filter id="textShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="2" />
      <feGaussianBlur stdDeviation="3" />
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.05 0" />
      <feMerge>
        <feMergeNode />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>

    <!-- Icon: Chart Bar (simplified) -->
    <symbol id="icon-chart-bar" viewBox="0 0 24 24">
      <path d="M4 12V20M12 4V20M20 8V20" class="icon-color" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>
    <!-- Icon: Trend Up (simplified) -->
    <symbol id="icon-trend-up" viewBox="0 0 24 24">
      <path d="M3 17L9 11L13 15L21 7M21 7H15M21 7V13" class="icon-color" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>
  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color" />

  <!-- Header Section -->
  <g id="header-section">
    <rect x="0" y="0" width="1920" height="120" fill="url(#headerGradient)" />
    <text x="80" y="75" class="main-title font-primary text-white" filter="url(#textShadow)">
      年中总结汇报
    </text>
    <text x="80" y="105" class="small-text font-primary" fill="#E0F2FE">
      Mid-Year Summary Report
    </text>
    <!-- Logo Placeholder -->
    <image x="1750" y="40" width="120" height="60" href="{logo_url}" />
  </g>

  <!-- Main Content Area -->
  <g id="main-content" transform="translate(80, 180)">
    <!-- Page Title -->
    <text x="0" y="0" class="section-title font-primary text-primary">
      数据分析和核心成果展示
    </text>
    <text x="0" y="40" class="body-text font-secondary text-secondary">
      Data Analysis and Key Achievements Showcase
    </text>

    <!-- Main Chart Area (Bento Grid style - large block) -->
    <g id="main-chart-block" transform="translate(0, 100)">
      <rect x="0" y="0" width="1100" height="600" class="card-bg card-border-radius card-shadow" stroke="#BAE6FD" stroke-width="1" />

      <!-- Chart Title -->
      <text x="40" y="50" class="content-title font-primary text-primary">
        年度业务增长趋势
      </text>
      <text x="40" y="85" class="small-text font-secondary text-secondary">
        Annual Business Growth Trend
      </text>

      <!-- Simulated Bar Chart -->
      <g id="bar-chart" transform="translate(100, 150)">
        <!-- Y-axis labels -->
        <text x="-30" y="0" class="chart-label">200K</text>
        <text x="-30" y="100" class="chart-label">150K</text>
        <text x="-30" y="200" class="chart-label">100K</text>
        <text x="-30" y="300" class="chart-label">50K</text>
        <text x="-30" y="400" class="chart-label">0</text>
        <line x1="-10" y1="0" x2="-10" y2="400" class="chart-axis-line" />

        <!-- X-axis labels and bars -->
        <!-- Bar 1: Q1 -->
        <rect x="0" y="200" width="60" height="200" class="chart-bar card-border-radius" rx="8" ry="8" />
        <text x="30" y="435" class="chart-label">Q1</text>
        <text x="30" y="180" class="chart-value">100K</text>

        <!-- Bar 2: Q2 -->
        <rect x="150" y="100" width="60" height="300" class="chart-bar card-border-radius" rx="8" ry="8" />
        <text x="180" y="435" class="chart-label">Q2</text>
        <text x="180" y="80" class="chart-value">150K</text>

        <!-- Bar 3: Q3 -->
        <rect x="300" y="50" width="60" height="350" class="chart-bar card-border-radius" rx="8" ry="8" />
        <text x="330" y="435" class="chart-label">Q3</text>
        <text x="330" y="30" class="chart-value">175K</text>

        <!-- Bar 4: Q4 -->
        <rect x="450" y="0" width="60" height="400" class="chart-bar card-border-radius" rx="8" ry="8" />
        <text x="480" y="435" class="chart-label">Q4</text>
        <text x="480" y="-20" class="chart-value">200K</text>

        <line x1="-10" y1="400" x2="800" y2="400" class="chart-axis-line" />
      </g>
    </g>

    <!-- Right Side Data Cards (Bento Grid style - smaller blocks) -->
    <g id="data-cards" transform="translate(1150, 100)">
      <!-- Card 1: Total Revenue -->
      <rect x="0" y="0" width="600" height="280" class="card-bg card-border-radius card-shadow" stroke="#BAE6FD" stroke-width="1" />
      <use xlink:href="#icon-chart-bar" x="40" y="40" width="32" height="32" />
      <text x="90" y="65" class="content-title font-primary text-secondary">
        总收入达成
      </text>
      <text x="90" y="95" class="small-text font-secondary text-light">
        Total Revenue Achieved
      </text>
      <text x="40" y="190" class="hero-title font-primary text-accent">
        $1.2M
      </text>
      <text x="40" y="235" class="body-text font-primary text-primary">
        同比增长 25%
      </text>
      <text x="40" y="265" class="small-text font-secondary text-light">
        YoY Growth 25%
      </text>

      <!-- Card 2: User Growth -->
      <rect x="0" y="320" width="600" height="280" class="card-bg card-border-radius card-shadow" stroke="#BAE6FD" stroke-width="1" />
      <use xlink:href="#icon-trend-up" x="40" y="360" width="32" height="32" />
      <text x="90" y="385" class="content-title font-primary text-secondary">
        用户增长量
      </text>
      <text x="90" y="415" class="small-text font-secondary text-light">
        User Growth Metrics
      </text>
      <text x="40" y="500" class="hero-title font-primary text-accent">
        +150K
      </text>
      <text x="40" y="545" class="body-text font-primary text-primary">
        新用户活跃度高
      </text>
      <text x="40" y="575" class="small-text font-secondary text-light">
        High New User Engagement
      </text>
    </g>
  </g>

  <!-- Footer / Page Number -->
  <g id="footer-section">
    <text x="1750" y="1020" class="small-text font-primary text-secondary">
      6/10
    </text>
    <text x="80" y="1020" class="small-text font-primary text-light">
      {date}
    </text>
    <text x="80" y="1040" class="small-text font-primary text-light">
      {author}
    </text>
  </g>

  <!-- Decorative Elements -->
  <g id="decorative-elements">
    <!-- Top-left subtle wave -->
    <path d="M0 120 C 150 80, 300 140, 450 100 L 450 0 L 0 0 Z" fill="#E0F2FE" opacity="0.5" />
    <path d="M0 120 C 100 100, 250 150, 400 110 L 400 0 L 0 0 Z" fill="#E0F2FE" opacity="0.3" />

    <!-- Bottom-right subtle wave/shape -->
    <path d="M1920 1080 L 1920 900 C 1770 940, 1620 880, 1470 920 L 1470 1080 Z" fill="#E0F2FE" opacity="0.5" />
    <path d="M1920 1080 L 1920 940 C 1820 920, 1670 970, 1520 930 L 1520 1080 Z" fill="#E0F2FE" opacity="0.3" />

    <!-- Vertical line divider with gradient -->
    <rect x="1130" y="180" width="4" height="780" fill="url(#accentGradient)" rx="2" ry="2" />
  </g>
</svg>