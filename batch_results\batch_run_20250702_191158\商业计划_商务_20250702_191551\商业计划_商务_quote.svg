<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Color Palette -->
    <style type="text/css">
      /* Basic Colors */
      .bg-color { fill: #F8FAFC; }
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .card-background { fill: #FFFFFF; }
      .card-border { stroke: #BAE6FD; }

      /* Font Styles */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* Hero Title (Chinese) */
      .hero-title {
        font-size: 72px;
        font-weight: 700; /* bold */
        fill: #1E293B; /* text-primary */
        line-height: 1.1;
      }
      /* Main Title (Chinese) */
      .main-title {
        font-size: 56px;
        font-weight: 700; /* bold */
        fill: #1E293B; /* text-primary */
        line-height: 1.2;
      }
      /* Section Title (Chinese) */
      .section-title {
        font-size: 36px;
        font-weight: 600; /* semibold */
        fill: #1E293B; /* text-primary */
        line-height: 1.3;
      }
      /* Content Title (Chinese) */
      .content-title {
        font-size: 28px;
        font-weight: 600; /* semibold */
        fill: #1E293B; /* text-primary */
        line-height: 1.4;
      }
      /* Body Text (Chinese/English) */
      .body-text {
        font-size: 22px;
        font-weight: 400; /* normal */
        fill: #1E293B; /* text-primary */
        line-height: 1.6; /* relaxed */
      }
      /* Small Text (English) */
      .small-text {
        font-size: 16px;
        font-weight: 400; /* normal */
        fill: #64748B; /* text-secondary */
        line-height: 1.4;
      }
      /* Caption (English) */
      .caption-text {
        font-size: 14px;
        font-weight: 300; /* light */
        fill: #94A3B8; /* text-light */
        line-height: 1.4;
      }

      /* Specific for Quote Page */
      .quote-text {
        font-size: 56px; /* main_title size for prominent quote */
        font-weight: 700; /* bold */
        fill: #1E293B; /* text-primary */
        line-height: 1.4; /* normal */
        text-anchor: middle; /* Center alignment */
      }
      .quote-source {
        font-size: 28px; /* content_title size */
        font-weight: 500; /* medium */
        fill: #475569; /* secondary-color for source prominence */
        line-height: 1.4;
        text-anchor: middle; /* Center alignment */
      }
      .quote-source-detail {
        font-size: 22px; /* body_text size */
        font-weight: 400; /* normal */
        fill: #64748B; /* text-secondary */
        line-height: 1.6;
        text-anchor: middle; /* Center alignment */
      }
      .quote-mark {
        font-family: 'Times New Roman', serif;
        font-size: 200px; /* Very large for decorative */
        fill: #BAE6FD; /* card_border for subtle accent */
        opacity: 0.6;
      }
      .page-number {
          font-size: 22px;
          fill: #64748B;
          font-family: 'Microsoft YaHei', sans-serif;
          font-weight: 400;
          text-anchor: end;
      }
    </style>

    <!-- Gradients -->
    <linearGradient id="gradientAccent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:0.8" />
    </linearGradient>

    <linearGradient id="gradientBackground" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#F8FAFC;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E0F2FE;stop-opacity:0.5" />
    </linearGradient>

    <!-- Filter for subtle shadow on elements (not used to simplify and avoid potential issues) -->
    <!-- <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset result="offOut" in="SourceAlpha" dx="4" dy="4" />
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="5" />
      <feBlend in="SourceGraphic" in2="blurOut" mode="normal" />
    </filter> -->
  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color" />
  <rect x="0" y="0" width="1920" height="1080" fill="url(#gradientBackground)" opacity="0.4" />

  <!-- Decorative Elements (Clean, layered, tech feel, inspired by Bento Grid structure) -->
  <!-- Top-left geometric shape -->
  <path d="M0 0 L250 0 L300 150 L50 200 Z" fill="#1E40AF" opacity="0.1" />
  <path d="M0 0 L200 0 L250 100 L0 150 Z" fill="#3B82F6" opacity="0.1" />

  <!-- Bottom-right geometric shape -->
  <path d="M1920 1080 L1670 1080 L1620 930 L1870 880 Z" fill="#1E40AF" opacity="0.1" />
  <path d="M1920 1080 L1720 1080 L1670 980 L1920 930 Z" fill="#3B82F6" opacity="0.1" />

  <!-- Central large circle with gradient (subtle emphasis) -->
  <circle cx="960" cy="540" r="400" fill="url(#gradientAccent)" opacity="0.05" />
  <circle cx="960" cy="540" r="380" stroke="#BAE6FD" stroke-width="1" opacity="0.2" />

  <!-- Main Content Area -->
  <g transform="translate(80, 60)"> <!-- Apply page margins -->

    <!-- Logo Placeholder (Top Left) -->
    <!-- Note: Ensure image_url does not contain '&' in its actual value -->
    <image href="{logo_url}" x="0" y="0" width="160" height="40" preserveAspectRatio="xMidYMid meet" />

    <!-- Page Number (Top Right) -->
    <text x="1760" y="30" class="page-number font-primary">9/10</text>

    <!-- Quote Content - Centered in the middle of the canvas -->
    <g transform="translate(0, 0)">
      <!-- Quote Marks (Decorative) -->
      <!-- Positioned relative to the center for visual balance -->
      <text x="560" y="380" class="quote-mark font-accent" text-anchor="start">“</text>
      <text x="1360" y="750" class="quote-mark font-accent" text-anchor="end">”</text>

      <!-- Main Quote Text -->
      <!-- x position is 960 (canvas center) - 80 (left margin) = 880 -->
      <text x="880" y="470" class="quote-text font-primary" text-anchor="middle">
        <tspan x="880" dy="0">{content}</tspan>
        <tspan x="880" dy="80">市场是检验真理的唯一标准</tspan>
        <tspan x="880" dy="80">和企业发展的动力</tspan>
      </text>

      <!-- Source Information -->
      <text x="880" y="780" class="quote-source font-primary" text-anchor="middle">
        <tspan x="880" dy="0">{author}</tspan>
        <tspan x="880" dy="40">Warren Buffett</tspan>
      </text>
      <text x="880" y="860" class="quote-source-detail font-secondary" text-anchor="middle">
        <tspan x="880" dy="0">{date}</tspan>
        <tspan x="880" dy="30">From: Berkshire Hathaway Annual Meeting 2023</tspan>
      </text>
    </g>

    <!-- Bottom Decorative Line (Gradient Divider) -->
    <rect x="0" y="940" width="1760" height="2" fill="url(#gradientAccent)" opacity="0.3" />

    <!-- Subtle Data Visualization/Icon (Outline style) - Bento Grid inspired small module -->
    <!-- Positioned within the right margin area -->
    <g transform="translate(1400, 80)">
        <path d="M50 0 L0 100 L100 100 Z" stroke="#3B82F6" stroke-width="2" fill="none" opacity="0.5" />
        <path d="M150 0 L100 100 L200 100 Z" stroke="#1E40AF" stroke-width="2" fill="none" opacity="0.5" />
        <path d="M100 50 L50 150 L150 150 Z" stroke="#475569" stroke-width="2" fill="none" opacity="0.3" />
        <text x="100" y="180" class="small-text font-secondary" text-anchor="middle">Market Insights</text>
    </g>

  </g>
</svg>