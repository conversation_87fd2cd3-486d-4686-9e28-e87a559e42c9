<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Gradients for visual emphasis and tech feel -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6" />
      <stop offset="100%" style="stop-color:#7DD3FC" />
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1E3A8A" />
      <stop offset="100%" style="stop-color:#1E40AF" />
    </linearGradient>
    <linearGradient id="accentGradientSubtle" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#BAE6FD;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:0.5" />
    </linearGradient>

    <!-- CSS Styles for typography, colors, and layout elements -->
    <!-- Absolutely no '&' symbols are used in CSS selectors or content -->
    <style type="text/css">
      /* Font Families */
      .font-inter { font-family: "Inter", Helvetica, Arial, sans-serif; }
      .font-sfpro { font-family: "SF Pro Display", system-ui, sans-serif; }
      .font-poppins { font-family: "Poppins", sans-serif; }

      /* Text Colors */
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .text-gradient-fill { fill: url(#textGradient); } /* Applies gradient to text */

      /* Font Sizes and Weights */
      .hero-title { font-size: 72px; font-weight: 700; } /* Bold */
      .main-title { font-size: 56px; font-weight: 700; } /* Bold */
      .section-title { font-size: 36px; font-weight: 700; } /* Bold */
      .content-title { font-size: 28px; font-weight: 600; } /* Semibold */
      .body-text { font-size: 22px; font-weight: 400; } /* Normal */
      .small-text { font-size: 16px; font-weight: 400; } /* Normal */
      .caption-text { font-size: 14px; font-weight: 400; } /* Normal */

      /* Text Alignment */
      .text-center { text-anchor: middle; }
      .text-left { text-anchor: start; }

      /* Card and Shape Styles */
      .card-bg { fill: #FFFFFF; stroke: #BAE6FD; stroke-width: 1px; }
      .primary-fill { fill: #3B82F6; }
      .secondary-fill { fill: #7DD3FC; }
      .accent-fill { fill: #BAE6FD; }
      .primary-stroke { stroke: #3B82F6; stroke-width: 2px; fill: none; }
      .secondary-stroke { stroke: #7DD3FC; stroke-width: 2px; fill: none; }
      .accent-stroke { stroke: #BAE6FD; stroke-width: 1px; fill: none; }
    </style>
  </defs>

  <!-- Canvas Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="#F8FAFC" />

  <!-- Decorative Elements: Simple lines and geometric shapes -->
  <!-- These elements are placed outside the main content group to act as background accents -->
  <path d="M0 0 L300 0 L250 150 L0 100 Z" fill="#BAE6FD" opacity="0.3" />
  <circle cx="1700" cy="100" r="80" fill="#7DD3FC" opacity="0.15" />
  <rect x="1800" y="900" width="120" height="180" fill="#3B82F6" opacity="0.1" />
  <line x1="100" y1="800" x2="400" y2="800" class="accent-stroke" stroke-width="2" />
  <line x1="1520" y1="200" x2="1820" y2="200" class="secondary-stroke" stroke-width="2" />


  <!-- Main Content Area (positioned within page margins) -->
  <!-- Horizontal margin: 80px, Vertical margin: 60px -->
  <g transform="translate(80, 60)">
    <!-- Logo Placeholder (Top Left) -->
    <rect x="0" y="0" width="120" height="40" fill="#3B82F6" opacity="0.7" />
    <text x="60" y="28" class="font-inter small-text text-center" fill="#FFFFFF">LOGO</text>

    <!-- Main Title and Subtitle -->
    <!-- Centered horizontally within the 1760px content width (1760 / 2 = 880) -->
    <text x="880" y="120" class="font-inter main-title text-primary text-center">
      <tspan x="880" y="120">学术研究成果发布会</tspan>
      <!-- Subtitle is 70px below the title, ensuring at least 60px spacing -->
      <tspan x="880" y="190" class="font-inter section-title text-secondary">深化理论和实践的融合</tspan>
    </text>

    <!-- Content Blocks: Paragraph, Data Insight, Key Points List, Emphasis -->
    <!-- This group is positioned 300px below the canvas's top margin (60px) + subtitle (190px), so absolute Y is 360px -->
    <g transform="translate(0, 300)">
      <!-- Left Column Content: Research Background and Data Insight -->
      <g transform="translate(0, 0)">
        <text x="0" y="0" class="font-inter content-title text-primary text-left">
          <tspan x="0" y="0">研究背景和目标</tspan>
        </text>
        <!-- Paragraph text, each line explicitly positioned using y-coordinates -->
        <!-- Line spacing is 35px, ensuring readability and no overlap -->
        <text x="0" y="50" class="font-inter body-text text-primary text-left">
          <tspan x="0" y="50">
            本次学术会议旨在汇聚全球顶尖学者和研究人员，共同探讨前沿科学领域的新发现和发展趋势。
          </tspan>
          <tspan x="0" y="85">
            我们致力于促进跨学科交流，推动理论创新和技术应用，为社会进步贡献力量。
          </tspan>
          <tspan x="0" y="120">
            通过深入的研讨和交流，期待能够激发新的研究思路，形成高质量的学术成果。
          </tspan>
          <tspan x="0" y="155">
            会议内容涵盖多个关键领域，旨在提供一个开放和包容的学术平台。
          </tspan>
        </text>

        <!-- Placeholder for a simple chart/data visualization -->
        <g transform="translate(0, 250)">
            <text x="0" y="0" class="font-inter content-title text-primary text-left">
                <tspan x="0" y="0">数据洞察</tspan>
            </text>
            <rect x="0" y="50" width="300" height="150" class="card-bg" />
            <!-- Simple line graph mimicking data trend -->
            <path d="M10 190 L50 140 L100 160 L150 110 L200 130 L250 80 L290 120" stroke="#3B82F6" stroke-width="3" fill="none" />
            <circle cx="290" cy="120" r="5" fill="#3B82F6" />
            <!-- Large number for emphasis -->
            <text x="150" y="100" class="font-inter hero-title text-gradient-fill text-center">
              <tspan x="150" y="100">95%</tspan>
            </text>
            <text x="150" y="140" class="font-inter small-text text-secondary text-center">
              <tspan x="150" y="140">研究成果转化率</tspan>
            </text>
        </g>
      </g>

      <!-- Right Column Content: Emphasis and Key Points List -->
      <!-- Positioned 900px to the right of the left column -->
      <g transform="translate(900, 0)">
        <!-- Emphasis: Large Chinese text with smaller English text -->
        <text x="0" y="0" class="font-inter hero-title text-gradient-fill text-left">
          <tspan x="0" y="0">未来</tspan>
          <tspan x="0" y="80">展望</tspan>
        </text>
        <text x="0" y="140" class="font-inter small-text text-secondary text-left">
          <tspan x="0" y="140">Fostering innovation and collaboration.</tspan>
        </text>

        <!-- Key Points List -->
        <!-- Positioned 200px below the emphasis text -->
        <g transform="translate(0, 200)">
          <text x="0" y="0" class="font-inter content-title text-primary text-left">
            <tspan x="0" y="0">核心要点</tspan>
          </text>

          <!-- List Items with bullet points -->
          <!-- Each list item text is explicitly positioned with y-coordinates, spacing is 40px -->
          <circle cx="10" cy="50" r="6" fill="#3B82F6" />
          <text x="30" y="55" class="font-inter body-text text-primary text-left">
            <tspan x="30" y="55">跨学科交流和合作</tspan>
          </text>

          <circle cx="10" cy="90" r="6" fill="#3B82F6" />
          <text x="30" y="95" class="font-inter body-text text-primary text-left">
            <tspan x="30" y="95">前沿理论和技术研究</tspan>
          </text>

          <circle cx="10" cy="130" r="6" fill="#3B82F6" />
          <text x="30" y="135" class="font-inter body-text text-primary text-left">
            <tspan x="30" y="135">高质量论文发表</tspan>
          </text>

          <circle cx="10" cy="170" r="6" fill="#3B82F6" />
          <text x="30" y="175" class="font-inter body-text text-primary text-left">
            <tspan x="30" y="175">培养青年科研人才</tspan>
          </text>

          <circle cx="10" cy="210" r="6" fill="#3B82F6" />
          <text x="30" y="215" class="font-inter body-text text-primary text-left">
            <tspan x="30" y="215">服务社会和经济发展</tspan>
          </text>
        </g>
      </g>
    </g>

    <!-- Footer / Page Number -->
    <!-- Centered horizontally at the bottom of the content area -->
    <text x="880" y="940" class="font-inter small-text text-secondary text-center">
      <tspan x="880" y="940">Page 4 / 10</tspan>
    </text>

  </g>
</svg>