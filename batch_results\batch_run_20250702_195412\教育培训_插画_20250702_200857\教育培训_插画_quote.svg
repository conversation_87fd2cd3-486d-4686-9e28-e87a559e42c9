<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <style>
    /* General Styles */
    .background-color { fill: #F8FAFC; }
    .primary-color { fill: #4A86E8; }
    .secondary-color { fill: #3B82F6; }
    .accent-color { fill: #0EA5E9; }
    .text-primary-color { fill: #1E293B; }
    .text-secondary-color { fill: #64748B; }
    .container-background-color { fill: #E0F2FE; }

    /* Font Styles */
    .font-primary { font-family: "Microsoft YaHei", "Segoe UI", sans-serif; }
    .font-secondary { font-family: "Source Han Sans CN", "Noto Sans CJK SC", sans-serif; }
    .font-accent { font-family: "Times New Roman", serif; }

    /* Text Sizes and Weights */
    .hero-title {
      font-size: 72px;
      font-weight: 700; /* bold */
      line-height: 1.1; /* tight */
    }
    .main-title {
      font-size: 56px;
      font-weight: 700; /* bold */
      line-height: 1.1; /* tight */
    }
    .section-title {
      font-size: 36px;
      font-weight: 600; /* semibold */
      line-height: 1.4; /* normal */
    }
    .content-title {
      font-size: 28px;
      font-weight: 500; /* medium */
      line-height: 1.4; /* normal */
    }
    .body-text {
      font-size: 22px;
      font-weight: 400; /* normal */
      line-height: 1.6; /* relaxed */
    }
    .small-text {
      font-size: 16px;
      font-weight: 400; /* normal */
      line-height: 1.4; /* normal */
    }
    .caption-text {
      font-size: 14px;
      font-weight: 400; /* normal */
      line-height: 1.4; /* normal */
    }

    /* General text alignment */
    .text-center { text-anchor: middle; }
    .text-left { text-anchor: start; }
    .text-right { text-anchor: end; }

    /* Decorative elements */
    .stroke-accent { stroke: #0EA5E9; stroke-width: 2; fill: none; }
    .stroke-primary { stroke: #4A86E8; stroke-width: 2; fill: none; }
    .fill-primary-light { fill: #4A86E8; opacity: 0.1; }
    .fill-accent-light { fill: #0EA5E9; opacity: 0.15; }
    .fill-secondary-light { fill: #3B82F6; opacity: 0.08; }
  </style>

  <!-- Background Layer -->
  <rect x="0" y="0" width="1920" height="1080" class="background-color"/>

  <!-- Decorative Shapes (Bento Grid 和 Illustration Style) -->
  <!-- Larger, soft shapes -->
  <circle cx="1600" cy="200" r="180" class="fill-primary-light"/>
  <rect x="1450" y="800" width="400" height="200" rx="40" ry="40" class="fill-accent-light"/>
  <path d="M100 100 L400 50 L300 400 L50 300 Z" class="fill-secondary-light"/> <!-- Abstract quad -->
  <path d="M1200 950 C1300 850, 1400 1050, 1500 950 L1500 1080 L1200 1080 Z" class="fill-primary-light"/> <!-- Bottom wave shape -->
  <path d="M0 600 C150 500, 300 700, 450 600 L450 0 L0 0 Z" class="fill-accent-light"/> <!-- Top-left wave shape -->

  <!-- Smaller, sharper elements -->
  <rect x="100" y="850" width="150" height="150" rx="20" ry="20" class="stroke-accent"/>
  <circle cx="1700" cy="500" r="60" class="stroke-primary"/>
  <path d="M500 50 C550 0, 650 0, 700 50 L700 100 L500 100 Z" class="fill-primary-light"/> <!-- Small top element -->

  <!-- Main Content Area -->
  <g transform="translate(960, 540)">
    <!-- Quote - Placeholder: {content} -->
    <text class="font-primary main-title text-center text-primary-color" y="-70">
      <tspan x="0" dy="0">“{content}</tspan>
      <tspan x="0" dy="70">知识是力量，</tspan>
      <tspan x="0" dy="70">但分享知识</tspan>
      <tspan x="0" dy="70">才是真正的智慧。”</tspan>
    </text>

    <!-- Source/Author - Placeholder: {author} -->
    <text class="font-secondary body-text text-center text-secondary-color" y="200">
      <tspan x="0" dy="0">—— {author}</tspan>
      <tspan x="0" dy="35">教育者和思考者</tspan>
    </text>

    <!-- Decorative Quote Marks (Custom Path) -->
    <!-- Left Quote Mark -->
    <path d="M-600 -180 C-650 -180, -680 -150, -680 -100 C-680 -50, -650 -20, -600 -20 C-550 -20, -520 -50, -520 -100 C-520 -150, -550 -180, -600 -180 Z M-600 -50 C-620 -50, -630 -60, -630 -70 C-630 -80, -620 -90, -600 -90 C-580 -90, -570 -80, -570 -70 C-570 -60, -580 -50, -600 -50 Z" class="accent-color" transform="scale(0.8) translate(-100, -100)"/>
    <!-- Right Quote Mark -->
    <path d="M600 180 C650 180, 680 150, 680 100 C680 50, 650 20, 600 20 C550 20, 520 50, 520 100 C520 150, 550 180, 600 180 Z M600 50 C620 50, 630 60, 630 70 C630 80, 620 90, 600 90 C580 90, 570 80, 570 70 C570 60, 580 50, 600 50 Z" class="accent-color" transform="scale(0.8) translate(100, 100)"/>

  </g>

  <!-- Top-left Logo Placeholder -->
  <g transform="translate(80, 60)">
    <rect x="0" y="0" width="150" height="50" rx="10" ry="10" fill="#BAE6FD" opacity="0.5"/>
    <text x="75" y="32" class="font-primary content-title text-center text-primary-color" style="font-size:24px;">{logo_url}</text>
  </g>

  <!-- Page Number/Date Placeholder -->
  <g transform="translate(1700, 60)">
    <text x="0" y="0" class="font-secondary small-text text-right text-secondary-color">
      <tspan x="0" dy="0">页面: 9/10</tspan>
      <tspan x="0" dy="20">{date}</tspan>
    </text>
  </g>

</svg>