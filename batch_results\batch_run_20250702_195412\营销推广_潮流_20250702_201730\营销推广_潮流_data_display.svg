<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette -->
    <style type="text/css">
      /* Colors */
      .primary-color { fill: #4A86E8; }
      .secondary-color { fill: #3B82F6; }
      .accent-color { fill: #0EA5E9; }
      .background-color { fill: #F8FAFC; }
      .text-primary-color { fill: #1E293B; }
      .text-secondary-color { fill: #64748B; }
      .text-light-color { fill: #94A3B8; }
      .card-background-color { fill: #FFFFFF; }
      .container-background-color { fill: #E0F2FE; }
      .stroke-primary-color { stroke: #4A86E8; }
      .stroke-card-border-color { stroke: #BAE6FD; }
      .stroke-accent-color { stroke: #0EA5E9; }

      /* Fonts */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* Font Sizes and Weights */
      .hero-title { font-size: 72px; font-weight: 700; }
      .main-title { font-size: 56px; font-weight: 700; }
      .section-title { font-size: 36px; font-weight: 700; }
      .content-title { font-size: 28px; font-weight: 600; }
      .body-text { font-size: 22px; font-weight: 400; }
      .small-text { font-size: 16px; font-weight: 400; }
      .caption-text { font-size: 14px; font-weight: 400; }
      .large-number { font-size: 96px; font-weight: 900; } /* Custom for emphasis */

      /* Shadows */
      .card-shadow { filter: url(#cardShadow); }

      /* Card Styles */
      .card-style {
        fill: #FFFFFF;
        stroke: #BAE6FD;
        stroke-width: 1px;
        rx: 12;
        ry: 12;
        filter: url(#cardShadow);
      }

      /* Bar Chart Styles */
      .bar-chart-bar {
        fill: url(#primaryGradient);
        rx: 4;
        ry: 4;
      }
      /* Decorative elements */
      .decorative-circle {
        fill: #4A86E8;
        opacity: 0.1;
      }
      .decorative-line {
        stroke: #BAE6FD;
        stroke-width: 1;
        opacity: 0.5;
      }
      .gradient-circle-accent {
        fill: url(#accentGradientTranslucent);
      }
      .gradient-rect-accent {
        fill: url(#accentGradientTranslucent);
      }
    </style>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A86E8" />
      <stop offset="100%" style="stop-color:#3B82F6" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0EA5E9" />
      <stop offset="100%" style="stop-color:#4A86E8" />
    </linearGradient>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#F8FAFC" />
      <stop offset="100%" style="stop-color:#E0F2FE" />
    </linearGradient>
    <linearGradient id="accentGradientTranslucent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0EA5E9;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#4A86E8;stop-opacity:0.2" />
    </linearGradient>

    <!-- Filters for Shadows -->
    <filter id="cardShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4" />
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="3" />
      <feColorMatrix result="matrixOut" in="blurOut" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" />
      <feMerge>
        <feMergeNode in="matrixOut" />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>

    <!-- Reusable Icons (simple path examples, stroke-width 2, color #4A86E8) -->
    <g id="icon-growth">
      <path d="M12 20L20 12L15 12L15 4L9 4L9 12L4 12L12 20Z" stroke="#4A86E8" stroke-width="2" fill="none"/>
    </g>
    <g id="icon-target">
      <circle cx="12" cy="12" r="8" stroke="#4A86E8" stroke-width="2" fill="none"/>
      <circle cx="12" cy="12" r="4" stroke="#4A86E8" stroke-width="2" fill="none"/>
      <circle cx="12" cy="12" r="1" fill="#4A86E8"/>
    </g>
    <g id="icon-star">
      <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="#4A86E8" stroke-width="2" fill="none"/>
    </g>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="background-color" />
  <rect x="0" y="0" width="1920" height="1080" fill="url(#bgGradient)" opacity="0.6" />

  <!-- Main Content Group -->
  <g id="page-content">
    <!-- Decorative Elements (Apple-like clean geometry with gradient transparency) -->
    <circle cx="1700" cy="100" r="150" class="gradient-circle-accent" />
    <rect x="100" y="800" width="200" height="200" class="gradient-rect-accent" transform="rotate(15 100 800)" />
    <path d="M0 400 Q 400 300 800 400 T 1600 400 L 1920 400 L 1920 0 L 0 0 Z" fill="#4A86E8" opacity="0.05" />
    <path d="M1920 700 Q 1500 800 1100 700 T 320 700 L 0 700 L 0 1080 L 1920 1080 Z" fill="#3B82F6" opacity="0.05" />

    <!-- Header Section -->
    <g id="header-section">
      <text x="100" y="120" class="main-title font-primary text-primary-color" text-anchor="start">
        <tspan>{title}</tspan>
      </text>
      <text x="100" y="170" class="content-title font-secondary text-secondary-color" text-anchor="start">
        <tspan>{subtitle}</tspan>
      </text>
    </g>

    <!-- Main Content Area - Bento Grid Inspired Layout -->
    <g id="data-visualization-section">
      <!-- Left Column: Main Chart Area -->
      <rect x="100" y="240" width="1000" height="740" class="card-style" />
      <text x="140" y="280" class="section-title font-primary text-primary-color">市场表现分析</text>
      <text x="140" y="325" class="body-text font-secondary text-secondary-color">
        <tspan>深入洞察产品在不同季度的销售趋势和用户增长情况。</tspan>
      </text>

      <!-- Placeholder for a Bar Chart -->
      <g id="bar-chart-placeholder">
        <text x="140" y="380" class="content-title font-primary text-primary-color">销售额趋势</text>
        <!-- Y-axis labels -->
        <text x="120" y="440" class="small-text font-secondary text-light-color" text-anchor="end">400万</text>
        <text x="120" y="540" class="small-text font-secondary text-light-color" text-anchor="end">300万</text>
        <text x="120" y="640" class="small-text font-secondary text-light-color" text-anchor="end">200万</text>
        <text x="120" y="740" class="small-text font-secondary text-light-color" text-anchor="end">100万</text>
        <text x="120" y="840" class="small-text font-secondary text-light-color" text-anchor="end">0</text>
        <!-- X-axis labels -->
        <text x="240" y="880" class="small-text font-secondary text-light-color" text-anchor="middle">Q1</text>
        <text x="440" y="880" class="small-text font-secondary text-light-color" text-anchor="middle">Q2</text>
        <text x="640" y="880" class="small-text font-secondary text-light-color" text-anchor="middle">Q3</text>
        <text x="840" y="880" class="small-text font-secondary text-light-color" text-anchor="middle">Q4</text>
        <text x="1040" y="880" class="small-text font-secondary text-light-color" text-anchor="middle">Q1</text> <!-- Next year Q1 -->

        <!-- Bars -->
        <!-- Each bar has a width of 80px. Spacing between bars is 120px (200 + 80 = 280, next starts at 400, so 120px gap) -->
        <rect x="200" y="760" width="80" height="80" class="bar-chart-bar" />
        <rect x="400" y="660" width="80" height="180" class="bar-chart-bar" />
        <rect x="600" y="500" width="80" height="340" class="bar-chart-bar" />
        <rect x="800" y="400" width="80" height="440" class="bar-chart-bar" />
        <rect x="1000" y="440" width="80" height="400" class="bar-chart-bar" />

        <!-- Data Labels on Bars (ensure 40px minimum from bar top) -->
        <text x="240" y="720" class="caption-text font-secondary text-primary-color" text-anchor="middle">120万</text>
        <text x="440" y="620" class="caption-text font-secondary text-primary-color" text-anchor="middle">200万</text>
        <text x="640" y="460" class="caption-text font-secondary text-primary-color" text-anchor="middle">340万</text>
        <text x="840" y="360" class="caption-text font-secondary text-primary-color" text-anchor="middle">420万</text>
        <text x="1040" y="400" class="caption-text font-secondary text-primary-color" text-anchor="middle">380万</text>

        <!-- Chart Grid Lines (ensure 40px spacing from labels) -->
        <line x1="150" y1="420" x2="1100" y2="420" class="decorative-line" />
        <line x1="150" y1="520" x2="1100" y2="520" class="decorative-line" />
        <line x1="150" y1="620" x2="1100" y2="620" class="decorative-line" />
        <line x1="150" y1="720" x2="1100" y2="720" class="decorative-line" />
        <line x1="150" y1="820" x2="1100" y2="820" class="decorative-line" />
        <line x1="150" y1="840" x2="1100" y2="840" stroke="#1E293B" stroke-width="2" /> <!-- X-axis line -->
        <line x1="150" y1="420" x2="150" y2="840" stroke="#1E293B" stroke-width="2" /> <!-- Y-axis line -->
      </g>

      <!-- Right Column: Data Cards and Statistics -->
      <g id="data-cards-section">
        <!-- Top Right Card: Key Metric 1 -->
        <rect x="1140" y="240" width="700" height="240" class="card-style" />
        <use xlink:href="#icon-growth" x="1180" y="280" width="32" height="32" />
        <text x="1225" y="300" class="content-title font-primary text-primary-color">总用户增长</text>
        <text x="1180" y="390" class="large-number font-accent accent-color">34.5<tspan class="section-title">%</tspan></text>
        <text x="1180" y="430" class="body-text font-secondary text-secondary-color">同比去年同期增长，活跃用户数持续攀升。</text>

        <!-- Middle Right Card: Key Metric 2 (50px gap from previous card) -->
        <rect x="1140" y="500" width="700" height="220" class="card-style" />
        <use xlink:href="#icon-target" x="1180" y="540" width="32" height="32" />
        <text x="1225" y="560" class="content-title font-primary text-primary-color">市场占有率</text>
        <text x="1180" y="650" class="large-number font-accent accent-color">18.2<tspan class="section-title">%</tspan></text>
        <text x="1180" y="690" class="body-text font-secondary text-secondary-color">在细分市场中占据领先位置。</text>

        <!-- Bottom Right Card: Key Metric 3 (50px gap from previous card) -->
        <rect x="1140" y="740" width="700" height="240" class="card-style" />
        <use xlink:href="#icon-star" x="1180" y="780" width="32" height="32" />
        <text x="1225" y="800" class="content-title font-primary text-primary-color">客户满意度</text>
        <text x="1180" y="890" class="large-number font-accent accent-color">98.7<tspan class="section-title">%</tspan></text>
        <text x="1180" y="930" class="body-text font-secondary text-secondary-color">用户反馈积极，产品口碑优良。</text>
      </g>
    </g>

    <!-- Footer / Branding Area -->
    <g id="footer-section">
      <text x="960" y="1040" class="small-text font-secondary text-light-color" text-anchor="middle">
        <tspan>{content}</tspan>
      </text>
    </g>

  </g>
</svg>