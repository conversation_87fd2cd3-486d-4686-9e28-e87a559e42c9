<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <style>
    /* CSS Declarations for a unified blue color scheme and minimalist style */
    /* Background Color */
    .bg { fill: #F8FAFC; } /* Light background as per specification */

    /* Text Colors */
    .text-primary-dark { fill: #1E293B; } /* Dark blue-grey for main text */
    .text-secondary-grey { fill: #64748B; } /* Medium grey-blue for secondary text */
    .text-light-grey { fill: #94A3B8; } /* Light grey-blue for captions */
    .text-white { fill: #FFFFFF; } /* White for contrast if needed */

    /* Main Blue Palette Colors */
    .primary-blue { fill: #3B82F6; } /* Main accent blue */
    .secondary-blue { fill: #7DD3FC; } /* Lighter accent blue */
    .accent-blue { fill: #BAE6FD; } /* Lightest accent blue, often for borders/dividers */

    /* Card Style */
    .card-background { fill: #FFFFFF; } /* White background for cards */
    .card-border { stroke: #BAE6FD; stroke-width: 1px; } /* Light blue border for cards */

    /* Font Families */
    .font-inter { font-family: 'Inter', Helvetica, Arial, sans-serif; }
    /* Note: SF Pro Display and Poppins are also defined in norms, but Inter is primary for this template. */

    /* Specific Font Sizes and Weights */
    .main-title-font { font-size: 56px; font-weight: 700; line-height: 1.1; } /* For main page title */
    .section-title-font { font-size: 36px; font-weight: 600; line-height: 1.4; } /* For section titles */
    .content-title-font { font-size: 28px; font-weight: 600; line-height: 1.4; } /* For card titles */
    .body-text-font { font-size: 22px; font-weight: 400; line-height: 1.6; } /* For general body text */
    .small-text-font { font-size: 16px; font-weight: 400; line-height: 1.6; } /* For small descriptive text */
    .caption-font { font-size: 14px; font-weight: 400; line-height: 1.6; } /* For captions or footnotes */

    /* Super Large Number Font - Emphasizes core points */
    .hero-number-font { font-size: 120px; font-weight: 700; line-height: 1.1; } /* Significantly larger for emphasis */

    /* Chart specific styles */
    .chart-label-font { font-size: 18px; font-weight: 400; fill: #64748B; } /* Labels for chart axes */
    .chart-value-font { font-size: 20px; font-weight: 600; fill: #1E293B; } /* Values displayed on chart elements */
  </style>

  <defs>
    <!-- Primary Gradient: linear-gradient(135deg, #3B82F6, #7DD3FC) -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#7DD3FC"/>
    </linearGradient>

    <!-- Accent Gradient: linear-gradient(45deg, #BAE6FD, #3B82F6) -->
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#BAE6FD"/>
      <stop offset="100%" stop-color="#3B82F6"/>
    </linearGradient>

    <!-- Gradient for Super Large Numbers: Using primary color with transparency for tech feel -->
    <linearGradient id="heroNumberGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="1"/>
      <stop offset="100%" stop-color="#7DD3FC" stop-opacity="0.8"/>
    </linearGradient>

    <!-- Simple line pattern for subtle background decoration (geometric) -->
    <pattern id="linePattern" patternUnits="userSpaceOnUse" width="20" height="20">
      <line x1="0" y1="0" x2="20" y2="20" stroke="#BAE6FD" stroke-width="0.5" stroke-opacity="0.3"/>
    </pattern>
  </defs>

  <!-- Main Background -->
  <rect x="0" y="0" width="1920" height="1080" class="bg"/>

  <!-- Decorative Background Elements (Subtle lines for minimalist feel) -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#linePattern)" opacity="0.1"/>

  <!-- Header Section -->
  <g id="header">
    <!-- Logo Placeholder (top-left as per layout principles) -->
    <image x="80" y="60" width="120" height="40" xlink:href="{logo_url}" />

    <!-- Title: Centered, large, bold Chinese text -->
    <text x="960" y="100" text-anchor="middle" class="font-inter main-title-font text-primary-dark">
      <tspan x="960" y="100">{title}</tspan>
    </text>

    <!-- Subtitle: Centered, smaller English text -->
    <text x="960" y="150" text-anchor="middle" class="font-inter body-text-font text-secondary-grey">
      <tspan x="960" y="150">{subtitle}</tspan>
    </text>
  </g>

  <!-- Main Content Area - Bento Grid Inspired Layout -->
  <g id="main-content">
    <!-- Left Section: Large Chart Area (simulated) -->
    <rect x="80" y="240" width="1000" height="700" class="card-background card-border"/>
    <text x="130" y="290" class="font-inter section-title-font text-primary-dark">
      <tspan x="130" y="290">项目进展分析 Project Progress</tspan>
    </text>

    <!-- Simulated Bar Chart for data visualization -->
    <g id="bar-chart" transform="translate(130, 360)">
      <!-- Y-Axis Grid Lines -->
      <line x1="0" y1="0" x2="850" y2="0" stroke="#BAE6FD" stroke-width="0.5"/>
      <line x1="0" y1="70" x2="850" y2="70" stroke="#BAE6FD" stroke-width="0.5"/>
      <line x1="0" y1="140" x2="850" y2="140" stroke="#BAE6FD" stroke-width="0.5"/>
      <line x1="0" y1="210" x2="850" y2="210" stroke="#BAE6FD" stroke-width="0.5"/>
      <line x1="0" y1="280" x2="850" y2="280" stroke="#BAE6FD" stroke-width="0.5"/>

      <!-- Y-Axis Labels (Values) - positioned to avoid overlap with bars -->
      <text x="-30" y="0" class="chart-label-font" text-anchor="end">100%</text>
      <text x="-30" y="70" class="chart-label-font" text-anchor="end">75%</text>
      <text x="-30" y="140" class="chart-label-font" text-anchor="end">50%</text>
      <text x="-30" y="210" class="chart-label-font" text-anchor="end">25%</text>
      <text x="-30" y="280" class="chart-label-font" text-anchor="end">0%</text>

      <!-- Bars - heights are from y=280 (base) upwards -->
      <rect x="0" y="112" width="50" height="168" class="primary-blue" rx="5"/> <!-- 60% -->
      <rect x="200" y="84" width="50" height="196" class="primary-blue" rx="5"/> <!-- 70% -->
      <rect x="400" y="56" width="50" height="224" class="primary-blue" rx="5"/> <!-- 80% -->
      <rect x="600" y="28" width="50" height="252" class="primary-blue" rx="5"/> <!-- 90% -->
      <rect x="800" y="0" width="50" height="280" class="primary-blue" rx="5"/> <!-- 100% -->

      <!-- Bar Values - positioned above bars, ensure 40px spacing from bar top to text baseline -->
      <text x="25" y="92" class="chart-value-font" text-anchor="middle" fill="#3B82F6">60%</text>
      <text x="225" y="64" class="chart-value-font" text-anchor="middle" fill="#3B82F6">70%</text>
      <text x="425" y="36" class="chart-value-font" text-anchor="middle" fill="#3B82F6">80%</text>
      <text x="625" y="8" class="chart-value-font" text-anchor="middle" fill="#3B82F6">90%</text>
      <text x="825" y="-20" class="chart-value-font" text-anchor="middle" fill="#3B82F6">100%</text>

      <!-- X-Axis Labels - positioned below bars, ensure 40px spacing -->
      <text x="25" y="320" class="chart-label-font" text-anchor="middle">Q1</text>
      <text x="225" y="320" class="chart-label-font" text-anchor="middle">Q2</text>
      <text x="425" y="320" class="chart-label-font" text-anchor="middle">Q3</text>
      <text x="625" y="320" class="chart-label-font" text-anchor="middle">Q4</text>
      <text x="825" y="320" class="chart-label-font" text-anchor="middle">Q5</text>
    </g>

    <!-- Right Section: Key Metrics and Data Cards -->
    <!-- Top Right: Super Large Number Card (Key Milestone Achievement) -->
    <rect x="1100" y="240" width="740" height="300" class="card-background card-border"/>
    <text x="1150" y="290" class="font-inter content-title-font text-primary-dark">
      <tspan x="1150" y="290">核心里程碑达成率 Key Milestone Achievement</tspan>
    </text>
    <text x="1470" y="440" text-anchor="middle" style="fill:url(#heroNumberGradient);" class="font-inter hero-number-font">
      <tspan x="1470" y="440">95%</tspan>
    </text>
    <text x="1470" y="480" text-anchor="middle" class="font-inter small-text-font text-secondary-grey">
      <tspan x="1470" y="480">Target: 90%</tspan>
    </text>

    <!-- Bottom Right: Data Card 1 (Issue Resolution Rate) -->
    <rect x="1100" y="560" width="360" height="380" class="card-background card-border"/>
    <text x="1150" y="610" class="font-inter content-title-font text-primary-dark">
      <tspan x="1150" y="610">问题解决率 Issue Resolution Rate</tspan>
    </text>
    <text x="1280" y="750" text-anchor="middle" class="font-inter hero-number-font" style="font-size: 80px; fill:url(#primaryGradient);">
      <tspan x="1280" y="750">92</tspan>
    </text>
    <text x="1280" y="800" text-anchor="middle" class="font-inter body-text-font text-secondary-grey">
      <tspan x="1280" y="800">of 100 solved</tspan>
    </text>
    <text x="1280" y="840" text-anchor="middle" class="font-inter small-text-font text-light-grey">
      <tspan x="1280" y="840">上月: 88</tspan>
    </text>

    <!-- Bottom Right: Data Card 2 (Resource Utilization) -->
    <rect x="1480" y="560" width="360" height="380" class="card-background card-border"/>
    <text x="1530" y="610" class="font-inter content-title-font text-primary-dark">
      <tspan x="1530" y="610">资源利用率 Resource Utilization</tspan>
    </text>
    <text x="1660" y="750" text-anchor="middle" class="font-inter hero-number-font" style="font-size: 80px; fill:url(#primaryGradient);">
      <tspan x="1660" y="750">85%</tspan>
    </text>
    <text x="1660" y="800" text-anchor="middle" class="font-inter body-text-font text-secondary-grey">
      <tspan x="1660" y="800">Optimal: 90%</tspan>
    </text>
    <text x="1660" y="840" text-anchor="middle" class="font-inter small-text-font text-light-grey">
      <tspan x="1660" y="840">环比增长: 5%</tspan>
    </text>

  </g>

  <!-- Footer Section -->
  <g id="footer">
    <text x="80" y="1020" class="font-inter small-text-font text-light-grey">
      <tspan x="80" y="1020">页面 6/10</tspan>
      <tspan x="200" y="1020">日期: {date}</tspan>
      <tspan x="350" y="1020">作者: {author}</tspan>
    </text>
  </g>

</svg>