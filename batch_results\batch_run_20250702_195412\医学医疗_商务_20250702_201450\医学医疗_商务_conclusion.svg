<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette -->
    <style type="text/css">
      /* Colors */
      .bg-color { fill: #F8FAFC; }
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .card-background { fill: #FFFFFF; }
      .card-border-color { stroke: #BAE6FD; }
      .icon-color { fill: #4A86E8; } /* From icon_system color */
      .divider-color { stroke: #BAE6FD; }

      /* Fonts */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* Font Sizes and Weights */
      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
      .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; }
      .content-title { font-size: 28px; font-weight: 700; line-height: 1.4; }
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.4; }
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.4; }
      .caption-text { font-size: 14px; font-weight: 400; line-height: 1.4; }

      /* Card Styles */
      .card-shadow { filter: url(#shadow); }
      .card-style {
        fill: #FFFFFF;
        stroke: #BAE6FD;
        stroke-width: 1;
        rx: 12px;
        ry: 12px;
      }
    </style>

    <!-- Shadows -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4"/>
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="3"/>
      <feColorMatrix result="colorOut" in="blurOut" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feMerge>
        <feMergeNode in="colorOut"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF"/>
      <stop offset="100%" stop-color="#475569"/>
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>
    <linearGradient id="techGradient" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.2"/>
        <stop offset="50%" stop-color="#3B82F6" stop-opacity="0.8"/>
        <stop offset="100%" stop-color="#3B82F6" stop-opacity="0.2"/>
    </linearGradient>

    <!-- Icons (Simplified for SVG template) -->
    <!-- Medical Cross Icon -->
    <g id="icon-medical-cross" fill="#4A86E8">
      <rect x="10" y="0" width="4" height="14" rx="1" ry="1"/>
      <rect x="3" y="7" width="18" height="4" rx="1" ry="1"/>
    </g>
    <!-- Chart Icon -->
    <g id="icon-chart" fill="none" stroke="#4A86E8" stroke-width="2">
      <polyline points="2 18 8 10 14 14 20 6"/>
      <line x1="2" y1="20" x2="20" y2="20"/>
    </g>
    <!-- Contact Icon -->
    <g id="icon-contact" fill="none" stroke="#4A86E8" stroke-width="2">
      <circle cx="12" cy="7" r="4"/>
      <path d="M5 20c0-3 3-5 7-5s7 2 7 5"/>
    </g>
    <!-- Checkmark Icon -->
    <g id="icon-checkmark" fill="none" stroke="#10B981" stroke-width="3">
      <polyline points="2 12 8 18 22 4"/>
    </g>
  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color"/>

  <!-- Decorative elements - Abstract geometric shapes and gradients -->
  <circle cx="1800" cy="100" r="150" fill="url(#accentGradient)" opacity="0.15"/>
  <rect x="50" y="900" width="300" height="80" fill="url(#primaryGradient)" rx="20" ry="20" opacity="0.1"/>
  <path d="M0 800 C 300 700, 600 900, 900 800 L 900 1080 L 0 1080 Z" fill="#1E40AF" opacity="0.05"/>
  <path d="M1920 200 C 1620 300, 1320 100, 1020 200 L 1020 0 L 1920 0 Z" fill="#3B82F6" opacity="0.05"/>

  <!-- Main Content Area -->
  <g transform="translate(80 60)"> <!-- Apply global margins -->
    <!-- Header / Title -->
    <text x="0" y="72" class="main-title font-primary text-primary">
      {title}
    </text>
    <text x="0" y="128" class="body-text font-secondary text-secondary">
      {subtitle}
    </text>

    <!-- Main Conclusions Section (Card 1) -->
    <g class="card-shadow">
      <rect x="0" y="200" width="800" height="300" class="card-style"/>
      <text x="40" y="240" class="content-title font-primary text-primary">
        主要结论
      </text>
      <use xlink:href="#icon-checkmark" x="40" y="275" width="24" height="24"/>
      <text x="80" y="295" class="body-text font-secondary text-secondary">
        <tspan x="80" dy="0">{content} 临床数据分析显示，治疗方案A显著优于方案B。</tspan>
        <tspan x="80" dy="30">研究成果揭示了疾病进展的关键生物标志物。</tspan>
        <tspan x="80" dy="30">个体化治疗策略在特定患者群体中效果显著。</tspan>
        <tspan x="80" dy="30">跨学科合作促进了创新疗法的快速转化。</tspan>
      </text>
    </g>

    <!-- Action Points / Recommendations Section (Card 2) -->
    <g class="card-shadow">
      <rect x="840" y="200" width="840" height="300" class="card-style"/>
      <text x="880" y="240" class="content-title font-primary text-primary">
        行动要点
      </text>
      <use xlink:href="#icon-chart" x="880" y="275" width="24" height="24"/>
      <text x="920" y="295" class="body-text font-secondary text-secondary">
        <tspan x="920" dy="0">优先推广新型治疗方案，优化临床路径。</tspan>
        <tspan x="920" dy="30">深化生物标志物研究，开发早期诊断工具。</tspan>
        <tspan x="920" dy="30">建立多中心临床试验网络，加速成果转化。</tspan>
        <tspan x="920" dy="30">加强医护人员培训，提升专业技能和应对能力。</tspan>
      </text>

      <!-- Emphasized Action / Number -->
      <text x="1260" y="380" class="hero-title font-accent accent-color" text-anchor="middle">
        +25%
      </text>
      <text x="1260" y="430" class="small-text font-secondary text-secondary" text-anchor="middle">
        治疗有效率提升
      </text>
      <line x1="1080" y1="310" x2="1080" y2="470" class="divider-color" stroke-dasharray="4 4"/>
    </g>

    <!-- Contact Information Section (Card 3) -->
    <g class="card-shadow">
      <rect x="0" y="550" width="800" height="300" class="card-style"/>
      <text x="40" y="590" class="content-title font-primary text-primary">
        联系我们
      </text>
      <use xlink:href="#icon-contact" x="40" y="625" width="24" height="24"/>
      <text x="80" y="645" class="body-text font-secondary text-secondary">
        <tspan x="80" dy="0">电子邮件: contact和#64;medicalresearch.com</tspan>
        <tspan x="80" dy="30">电话: +86 123 4567 8901</tspan>
        <tspan x="80" dy="30">网站: www.medicalsolutions.com</tspan>
        <tspan x="80" dy="30">地址: 医疗科技园A座123号</tspan>
      </text>
    </g>

    <!-- Thank You / Call to Action -->
    <g class="card-shadow">
        <rect x="840" y="550" width="840" height="300" class="card-style"/>
        <text x="880" y="590" class="section-title font-primary text-primary">
            感谢您的关注
        </text>
        <text x="880" y="640" class="body-text font-secondary text-secondary">
            本研究报告旨在提供最新的临床数据和研究成果。
            我们期待与您共同推动医疗健康事业的发展。
        </text>

        <!-- Call to Action Button -->
        <rect x="880" y="720" width="300" height="60" fill="url(#accentGradient)" rx="10" ry="10"/>
        <text x="1030" y="758" class="content-title font-primary card-background" text-anchor="middle">
            下载完整报告
        </text>

        <!-- Dynamic element: Date & Author -->
        <text x="880" y="810" class="small-text font-secondary text-light">
            报告生成日期: {date}
        </text>
        <text x="880" y="835" class="small-text font-secondary text-light">
            作者: {author}
        </text>
    </g>

    <!-- Footer / Page Number -->
    <text x="1680" y="960" class="caption-text font-secondary text-secondary" text-anchor="end">
      10 / 10
    </text>
  </g>

  <!-- Logo Placeholder (Top Left) -->
  <g transform="translate(80 30)">
    <rect x="0" y="0" width="150" height="40" fill="#1E40AF" rx="8" ry="8" opacity="0.9"/>
    <text x="75" y="28" class="small-text font-primary card-background" text-anchor="middle">
      {logo_url}
    </text>
  </g>

</svg>