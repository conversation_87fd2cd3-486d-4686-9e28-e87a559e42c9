<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景色 -->
  <rect width="1920" height="1080" fill="#F8FAFC"/>

  <!-- CSS 样式定义 -->
  <style>
    /* Color Palette */
    .primary-color { fill: #1E40AF; }
    .primary-stroke { stroke: #1E40AF; }
    .secondary-color { fill: #475569; }
    .secondary-stroke { stroke: #475569; }
    .accent-color { fill: #3B82F6; }
    .accent-stroke { stroke: #3B82F6; }
    .background-color { fill: #F8FAFC; }
    .text-primary-fill { fill: #1E293B; }
    .text-secondary-fill { fill: #64748B; }
    .card-background-fill { fill: #FFFFFF; }
    .card-border-stroke { stroke: #BAE6FD; }

    /* Font System */
    .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
    .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
    /* .font-accent { font-family: 'Times New Roman', serif; } */ /* Not used for this template */

    .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
    .section-title { font-size: 36px; font-weight: 700; line-height: 1.2; }
    .content-title { font-size: 28px; font-weight: 600; line-height: 1.3; }
    .body-text { font-size: 22px; font-weight: 400; line-height: 1.4; }
    .small-text { font-size: 16px; font-weight: 400; line-height: 1.5; }
    .caption-text { font-size: 14px; font-weight: 400; line-height: 1.6; }

    .font-bold { font-weight: 700; }
    .font-semibold { font-weight: 600; }
    .font-normal { font-weight: 400; }

    /* General Layout Helpers */
    .text-center { text-anchor: middle; }
    .text-left { text-anchor: start; }
    .text-right { text-anchor: end; }

    /* Card Style */
    .card-style {
      fill: #FFFFFF; /* card_background */
      stroke: #BAE6FD; /* card_border */
      stroke-width: 1px;
      rx: 12px; /* border-radius */
      ry: 12px;
      filter: url(#shadow); /* Apply shadow filter */
    }

    /* Timeline Specific Styles */
    .timeline-line {
      stroke: #475569; /* secondary-color */
      stroke-width: 2px;
    }
    .timeline-node {
      fill: #1E40AF; /* primary-color */
      stroke: #F8FAFC; /* background-color */
      stroke-width: 4px;
    }
    .milestone-node {
      fill: #3B82F6; /* accent-color */
      stroke: #F8FAFC; /* background-color */
      stroke-width: 5px;
      filter: url(#glow); /* Glow effect for milestones */
    }
    .timeline-date-text {
      font-size: 24px;
      font-weight: 700;
      fill: #1E293B; /* text-primary */
    }
    .timeline-event-title {
      font-size: 26px;
      font-weight: 600;
      fill: #1E293B; /* text-primary */
    }
    .timeline-event-description {
      font-size: 20px;
      font-weight: 400;
      fill: #64748B; /* text-secondary */
    }
    .timeline-icon {
      fill: #3B82F6; /* accent-color */
    }
  </style>

  <!-- 定义可复用元素和滤镜 -->
  <defs>
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4"/>
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="3"/>
      <feColorMatrix result="shadowColor" in="blurOut" type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feOffset result="offOutSmall" in="SourceAlpha" dx="0" dy="2"/>
      <feGaussianBlur result="blurOutSmall" in="offOutSmall" stdDeviation="2"/>
      <feColorMatrix result="shadowColorSmall" in="blurOutSmall" type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.06 0"/>
      <feMerge>
        <feMergeNode in="shadowColor"/>
        <feMergeNode in="shadowColorSmall"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 光晕滤镜 (用于里程碑) -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="5" result="blur"/>
      <feColorMatrix in="blur" type="matrix" values="
        1 0 0 0 0
        0 1 0 0 0
        0 1 0 0 0
        0 0 0 1 0" result="glowColor"/>
      <feMerge>
        <feMergeNode in="glowColor"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 渐变定义 (如果需要，目前未使用透明渐变) -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E40AF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#475569;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>

    <!-- 图标系统定义 (简单勾线风格) -->
    <g id="icon-chart-pie">
      <circle cx="12" cy="12" r="10" stroke="#3B82F6" stroke-width="2" />
      <path d="M12 2L12 12L20.66 16.46" stroke="#3B82F6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
      <path d="M12 12L19.07 4.93" stroke="#3B82F6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
    </g>
    <g id="icon-rocket">
      <path d="M12 2L22 12L12 22L2 12L12 2Z" stroke="#3B82F6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
      <path d="M12 12L12 22" stroke="#3B82F6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
      <path d="M12 12L22 12" stroke="#3B82F6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
    </g>
    <g id="icon-target">
      <circle cx="12" cy="12" r="10" stroke="#3B82F6" stroke-width="2" />
      <circle cx="12" cy="12" r="6" stroke="#3B82F6" stroke-width="2" />
      <circle cx="12" cy="12" r="2" fill="#3B82F6" />
    </g>
  </defs>

  <!-- 页面头部 -->
  <g id="header">
    <text x="960" y="120" class="section-title font-primary text-center text-primary-fill">
      <tspan x="960">{title}公司发展历程</tspan>
      <tspan x="960" dy="45" class="content-title font-secondary text-secondary-fill">Our Company Milestones 和 Growth Timeline</tspan>
    </text>
    <text x="1840" y="50" class="small-text font-primary text-secondary-fill text-right">页面 8/10</text>
  </g>

  <!-- 时间轴主体内容 -->
  <g id="timeline-content">
    <!-- 时间轴主线 -->
    <line x1="960" y1="280" x2="960" y2="900" class="timeline-line" />

    <!-- 时间轴节点和事件 -->
    <!-- 事件 1: 公司成立 -->
    <g id="event-1">
      <circle cx="960" cy="300" r="10" class="timeline-node" />
      <text x="920" y="305" class="timeline-date-text font-primary text-right text-primary-fill">
        <tspan>{date} 2015</tspan>
      </text>
      <g transform="translate(1000, 280)">
        <rect width="400" height="120" class="card-style" />
        <text x="20" y="40" class="timeline-event-title font-primary text-primary-fill">
          <tspan>公司成立和初期发展</tspan>
        </text>
        <text x="20" y="75" class="timeline-event-description font-secondary text-secondary-fill">
          <tspan>确立核心业务，组建核心团队。</tspan>
          <tspan x="20" dy="35">获得天使轮投资。</tspan>
        </text>
        <use href="#icon-chart-pie" x="350" y="10" width="32" height="32" class="timeline-icon" />
      </g>
    </g>

    <!-- 事件 2: 产品发布 -->
    <g id="event-2">
      <circle cx="960" cy="450" r="10" class="timeline-node" />
      <text x="920" y="455" class="timeline-date-text font-primary text-right text-primary-fill">
        <tspan>{date} 2017</tspan>
      </text>
      <g transform="translate(480, 430)">
        <rect width="400" height="120" class="card-style" />
        <text x="380" y="40" class="timeline-event-title font-primary text-right text-primary-fill">
          <tspan>核心产品发布和市场拓展</tspan>
        </text>
        <text x="380" y="75" class="timeline-event-description font-secondary text-right text-secondary-fill">
          <tspan>产品正式上线，用户量快速增长。</tspan>
          <tspan x="380" dy="35">进入多个重点市场。</tspan>
        </text>
        <use href="#icon-rocket" x="10" y="10" width="32" height="32" class="timeline-icon" />
      </g>
    </g>

    <!-- 事件 3: 里程碑 - A轮融资 -->
    <g id="event-3">
      <circle cx="960" cy="600" r="15" class="milestone-node" /> <!-- 更大的节点突出里程碑 -->
      <text x="920" y="605" class="timeline-date-text font-primary text-right text-primary-fill">
        <tspan>{date} 2020</tspan>
      </text>
      <g transform="translate(1000, 580)">
        <rect width="400" height="120" class="card-style" />
        <text x="20" y="40" class="timeline-event-title font-primary text-primary-fill">
          <tspan>里程碑：A轮融资成功</tspan>
        </text>
        <text x="20" y="75" class="timeline-event-description font-secondary text-secondary-fill">
          <tspan>获得数千万美元A轮融资，加速研发。</tspan>
          <tspan x="20" dy="35">扩大团队规模和市场份额。</tspan>
        </text>
        <use href="#icon-target" x="350" y="10" width="32" height="32" class="timeline-icon" />
      </g>
    </g>

    <!-- 事件 4: 国际拓展 -->
    <g id="event-4">
      <circle cx="960" cy="750" r="10" class="timeline-node" />
      <text x="920" y="755" class="timeline-date-text font-primary text-right text-primary-fill">
        <tspan>{date} 2022</tspan>
      </text>
      <g transform="translate(480, 730)">
        <rect width="400" height="120" class="card-style" />
        <text x="380" y="40" class="timeline-event-title font-primary text-right text-primary-fill">
          <tspan>国际市场拓展和战略合作</tspan>
        </text>
        <text x="380" y="75" class="timeline-event-description font-secondary text-right text-secondary-fill">
          <tspan>与国际伙伴建立合作，进入新市场。</tspan>
          <tspan x="380" dy="35">提升品牌国际影响力。</tspan>
        </text>
        <use href="#icon-chart-pie" x="10" y="10" width="32" height="32" class="timeline-icon" />
      </g>
    </g>

    <!-- 事件 5: 未来展望 -->
    <g id="event-5">
      <circle cx="960" cy="900" r="10" class="timeline-node" />
      <text x="920" y="905" class="timeline-date-text font-primary text-right text-primary-fill">
        <tspan>未来展望</tspan>
      </text>
      <g transform="translate(1000, 880)">
        <rect width="400" height="120" class="card-style" />
        <text x="20" y="40" class="timeline-event-title font-primary text-primary-fill">
          <tspan>持续创新和行业领导</tspan>
        </text>
        <text x="20" y="75" class="timeline-event-description font-secondary text-secondary-fill">
          <tspan>深耕技术研发，拓展新领域。</tspan>
          <tspan x="20" dy="35">成为行业领导者。</tspan>
        </text>
        <use href="#icon-rocket" x="350" y="10" width="32" height="32" class="timeline-icon" />
      </g>
    </g>
  </g>

  <!-- 页面底部信息 -->
  <g id="footer">
    <text x="80" y="1030" class="small-text font-primary text-secondary-fill">
      <tspan>{logo_url} | {date} | {author}</tspan>
    </text>
  </g>
</svg>