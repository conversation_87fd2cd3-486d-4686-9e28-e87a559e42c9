import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Card, Select, Button, Upload, Tabs, message,Input, Radio, Progress } from 'antd';
import { FileTextOutlined, SoundOutlined, FileImageOutlined, UploadOutlined, EditOutlined, ReloadOutlined } from '@ant-design/icons';
import { useLocation } from 'react-router-dom';
import { MessageService } from '../../../utils/MessageService';

import PodcastContent from './PodcastContent';
import PPTContent from './PPTContent';
import ProseContent from './ProseContent';

import SVGPPTService from '../../../utils/svgPptService';

const { Option } = Select;
const { TabPane } = Tabs;
const { TextArea } = Input;

/**
 * DeerFlow内容生成组件
 * 支持播客、PPT、散文等多媒体内容生成
 */
const DeerFlowContent = ({ initialData }) => {
  const location = useLocation();
  const [contentType, setContentType] = useState('podcast');
  const [sourceContent, setSourceContent] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [availableVoices, setAvailableVoices] = useState([]);
  const [pptTemplates, setPptTemplates] = useState([]);
  const [loadingTemplates, setLoadingTemplates] = useState(false);
  
  // {{CHENGQI:
  // Action: Added
  // Timestamp: [2025-01-16T16:20:00+08:00]
  // Reason: 添加PPT大纲编辑功能的状态管理
  // Principle_Applied: 状态管理 - 清晰的状态分离; 用户体验 - 支持大纲编辑流程
  // Optimization: 分离大纲生成和PPT生成的状态
  // Architectural_Note (AR): 扩展前端功能，支持两步PPT生成
  // Documentation_Note (DW): 新增大纲编辑相关状态管理
  // }}
  // PPT大纲编辑相关状态
  const [outlineContent, setOutlineContent] = useState('');
  const [outlineGenerated, setOutlineGenerated] = useState(false);
  const [generatingOutline, setGeneratingOutline] = useState(false);
  
  // {{CHENGQI:
  // Action: Added
  // Timestamp: [2025-01-18T00:05:00+08:00]
  // Reason: 添加播客脚本编辑功能的状态管理，参考PPT大纲流程
  // Principle_Applied: 状态管理 - 清晰的状态分离; 用户体验 - 支持脚本编辑流程
  // Optimization: 分离脚本生成和音频生成的状态
  // Architectural_Note (AR): 扩展播客功能，支持两步生成流程
  // Documentation_Note (DW): 新增播客脚本编辑相关状态管理
  // }}
  // 播客脚本编辑相关状态
  const [scriptContent, setScriptContent] = useState('');
  const [scriptGenerated, setScriptGenerated] = useState(false);
  const [generatingScript, setGeneratingScript] = useState(false);
  
  // {{CHENGQI:
  // Action: Modified
  // Timestamp: [2025-01-19T16:25:00+08:00]
  // Reason: 扩展播客脚本状态管理，添加异步任务支持
  // Principle_Applied: 状态管理 - 清晰的状态分离; 异步任务 - 统一的任务管理模式
  // Optimization: 添加脚本生成异步任务状态，与音频生成保持一致
  // Architectural_Note (AR): 统一异步任务处理模式
  // Documentation_Note (DW): 扩展播客脚本编辑的异步任务状态管理
  // }}
  // 播客脚本相关状态
  const [podcastScriptGenerated, setPodcastScriptGenerated] = useState(false);
  const [podcastScriptContent, setPodcastScriptContent] = useState('');
  
  // 脚本生成异步任务相关状态
  const [scriptTaskId, setScriptTaskId] = useState(null);
  const [scriptGenerating, setScriptGenerating] = useState(false);
  const [scriptTaskPolling, setScriptTaskPolling] = useState(false);
  const [scriptProgress, setScriptProgress] = useState(0);
  const scriptPollingRef = useRef(null);
  
  // {{CHENGQI:
  // Action: Added
  // Timestamp: [2025-01-19T15:35:00+08:00]
  // Reason: 添加播客音频生成异步任务相关状态管理，参考深度研究的异步模式
  // Principle_Applied: 状态管理 - 清晰的状态分离; 用户体验 - 防止重复点击和提供进度反馈
  // Optimization: 添加播客任务轮询和进度显示
  // Architectural_Note (AR): 统一异步任务状态管理模式
  // Documentation_Note (DW): 新增播客音频生成异步任务状态管理
  // }}
  // 播客音频生成异步任务相关状态
  const [podcastTaskId, setPodcastTaskId] = useState(null);
  const [podcastGenerating, setPodcastGenerating] = useState(false);
  const [podcastTaskPolling, setPodcastTaskPolling] = useState(false);
  const [podcastProgress, setPodcastProgress] = useState(0);
  const podcastPollingRef = useRef(null);
  
  // {{CHENGQI:
  // Action: Added
  // Timestamp: [2025-06-22T15:46:00+08:00]
  // Reason: 添加SVG+PPT生成相关状态管理，支持完整的SVG生成、转换、下载流程
  // Principle_Applied: 状态管理 - 清晰的状态分离; 用户体验 - 支持SVG+PPT生成流程
  // Optimization: 分离SVG生成和PPT转换的状态，提供详细的进度反馈
  // Architectural_Note (AR): 扩展前端功能，支持SVG+PPT生成流程
  // Documentation_Note (DW): 新增SVG+PPT相关状态管理
  // }}
  // SVG+PPT生成相关状态
  const [svgContent, setSvgContent] = useState([]);
  const [svgGenerated, setSvgGenerated] = useState(false);
  const [generatingSvg, setGeneratingSvg] = useState(false);
  const [svgProgress, setSvgProgress] = useState({ stage: '', message: '', progress: 0 });
  const [convertingToPpt, setConvertingToPpt] = useState(false);
  const [svgPreviewVisible, setSvgPreviewVisible] = useState(false);
  const [svgCurrentIndex, setSvgCurrentIndex] = useState(0);
  const [svgPptService] = useState(() => new SVGPPTService());
  
  // 单张SVG重新生成相关状态
  const [regeneratingSvgIndex, setRegeneratingSvgIndex] = useState(null);
  
  // 设计规范缓存管理
  const [originalSessionId, setOriginalSessionId] = useState(null);
  
  /**
   * 设计规范缓存管理工具函数
   */
  const saveDesignSpecToCache = useCallback((sessionId, designSpec, generatorConfig) => {
    try {
      const cacheKey = `svg_design_spec_${sessionId}`;
      const cacheData = {
        designSpec,
        generatorConfig,
        sourceContent,
        timestamp: Date.now(),
        version: '1.0'
      };
      localStorage.setItem(cacheKey, JSON.stringify(cacheData));
      console.log('💾 已保存设计规范到本地缓存:', sessionId);
    } catch (error) {
      console.warn('⚠️ 保存设计规范到缓存失败:', error);
    }
  }, [sourceContent]);
  
  const getDesignSpecFromCache = useCallback((sessionId) => {
    try {
      const cacheKey = `svg_design_spec_${sessionId}`;
      const cacheData = localStorage.getItem(cacheKey);
      if (cacheData) {
        const parsed = JSON.parse(cacheData);
        // 检查缓存是否还有效（24小时内）
        const isValid = (Date.now() - parsed.timestamp) < 24 * 60 * 60 * 1000;
        if (isValid && parsed.sourceContent === sourceContent) {
          console.log('📦 从本地缓存获取到设计规范:', sessionId);
          return parsed;
        } else {
          // 缓存过期或内容不匹配，清除
          localStorage.removeItem(cacheKey);
          console.log('🗑️ 清除过期的设计规范缓存:', sessionId);
        }
      }
    } catch (error) {
      console.warn('⚠️ 从缓存获取设计规范失败:', error);
    }
    return null;
  }, [sourceContent]);
  
  const clearDesignSpecCache = useCallback(() => {
    try {
      // 清除所有设计规范相关的缓存
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith('svg_design_spec_')) {
          localStorage.removeItem(key);
        }
      });
      console.log('🧹 已清除所有设计规范缓存');
    } catch (error) {
      console.warn('⚠️ 清除设计规范缓存失败:', error);
    }
  }, []);

  // {{CHENGQI:
  // Action: Moved
  // Timestamp: [2025-01-16T17:15:00+08:00]
  // Reason: 将config状态定义移到更早位置，解决"Cannot access before initialization"错误
  // Principle_Applied: 变量声明顺序 - 确保变量在使用前定义; 错误修复 - 解决初始化顺序问题
  // Optimization: 修复运行时错误，确保依赖关系正确
  // Architectural_Note (AR): 重新组织代码结构，解决变量引用问题
  // Documentation_Note (DW): 修复config状态初始化顺序问题
  // }}
  // 生成配置 - 移到此处以避免初始化顺序问题
  const [config, setConfig] = useState({
    podcast: {
      style: 'conversational',
      duration: 'medium',
      voice: 'male-qn-qingse',  // 保留用于单语音模式和向后兼容
      voice1: 'male-qn-qingse', // 主播一语音（对话式和访谈式）
      voice2: 'female-shaonv',  // 主播二语音（对话式和访谈式）
      long_text_strategy: 'ai_compress'
    },
    ppt: {
      theme: 'professional',
      slides: 'auto',
      template: 'business',
      use_template: false,
      template_id: null
    },
    prose: {
      style: 'popular',
      length: 'medium',
      tone: 'informative'
    }
  });

  /**
   * 更新配置
   */
  const updateConfig = useCallback((type, key, value) => {
    setConfig(prev => ({
      ...prev,
      [type]: {
        ...prev[type],
        [key]: value
      }
    }));
  }, []);
  
  // SVG缓存相关常量
  const SVG_CACHE_KEY = 'deer_flow_svg_cache';
  const SVG_GENERATION_STATE_KEY = 'deer_flow_svg_generation_state';
  
  /**
   * SVG缓存管理
   */
  // 保存SVG到缓存
  const saveSvgToCache = useCallback((svgData, generationState = null) => {
    try {
      const cacheData = {
        content: svgData,
        timestamp: Date.now(),
        sourceContentHash: btoa(sourceContent).slice(0, 50), // 使用源内容的哈希作为标识
        config: config.ppt // 保存生成时的配置
      };
      localStorage.setItem(SVG_CACHE_KEY, JSON.stringify(cacheData));
      
      // 如果有生成状态信息，也保存
      if (generationState) {
        localStorage.setItem(SVG_GENERATION_STATE_KEY, JSON.stringify({
          ...generationState,
          timestamp: Date.now()
        }));
      }
      
      console.log('💾 SVG缓存已保存:', { pageCount: svgData.length, timestamp: cacheData.timestamp });
    } catch (error) {
      console.warn('保存SVG缓存失败:', error);
    }
  }, [sourceContent, config.ppt]);
  
  // 从缓存加载SVG
  const loadSvgFromCache = useCallback(() => {
    try {
      const cacheData = localStorage.getItem(SVG_CACHE_KEY);
      if (!cacheData) return null;
      
      const parsed = JSON.parse(cacheData);
      const currentSourceHash = btoa(sourceContent).slice(0, 50);
      
      // 检查源内容是否发生变化
      if (parsed.sourceContentHash !== currentSourceHash) {
        console.log('📝 源内容已更改，清除过期的SVG缓存');
        clearSvgCache();
        return null;
      }
      
      // 检查缓存是否过期（24小时）
      const cacheAge = Date.now() - parsed.timestamp;
      if (cacheAge > 24 * 60 * 60 * 1000) {
        console.log('⏰ SVG缓存已过期，自动清除');
        clearSvgCache();
        return null;
      }
      
      console.log('📂 从缓存加载SVG:', { pageCount: parsed.content.length, cacheAge: Math.round(cacheAge / 1000 / 60) + '分钟前' });
      return parsed;
    } catch (error) {
      console.warn('加载SVG缓存失败:', error);
      return null;
    }
  }, [sourceContent]);
  
  // 清除SVG缓存
  const clearSvgCache = useCallback(() => {
    try {
      localStorage.removeItem(SVG_CACHE_KEY);
      localStorage.removeItem(SVG_GENERATION_STATE_KEY);
      console.log('🗑️ SVG缓存已清除');
    } catch (error) {
      console.warn('清除SVG缓存失败:', error);
    }
  }, []);
  
  // 获取生成状态缓存
  const getGenerationStateFromCache = useCallback(() => {
    try {
      const stateData = localStorage.getItem(SVG_GENERATION_STATE_KEY);
      if (!stateData) return null;
      
      const parsed = JSON.parse(stateData);
      
      // 检查状态缓存是否过期（2小时）
      const stateAge = Date.now() - parsed.timestamp;
      if (stateAge > 2 * 60 * 60 * 1000) {
        localStorage.removeItem(SVG_GENERATION_STATE_KEY);
        return null;
      }
      
      return parsed;
    } catch (error) {
      console.warn('获取生成状态缓存失败:', error);
      return null;
    }
  }, []);

  // 原config定义已移到更早位置，此处删除以避免重复声明
  
  /**
   * 播放音频
   */
  /**
   * 解码音频数据
   * 处理后端返回的复杂数据结构：base64(JSON) -> JSON.data.audio(hex) -> bytes
   */
  const decodeAudioData = useCallback((data) => {
    // {{CHENGQI:
    // Action: Added
    // Timestamp: [2025-06-10T22:15:00+08:00]
    // Reason: 处理后端返回的音频数据结构，先base64解码JSON，再hex解码音频
    // Principle_Applied: 数据处理 - 正确处理嵌套编码格式; KISS - 简化解码逻辑
    // Optimization: 按照实际数据结构进行解码，提高成功率
    // Architectural_Note (AR): 处理后端返回的实际数据格式
    // Documentation_Note (DW): 解决用户反馈的音频数据结构问题
    // }}
    if (typeof data !== 'string') {
      return data; // 已经是二进制数据
    }
    
    try {
      console.log('🔍 Processing audio data, length:', data.length);
      
      // 第一步：base64解码得到JSON字符串
      const jsonString = atob(data.trim());
      console.log('✅ Base64 decoded to JSON string, length:', jsonString.length);
      
      // 第二步：解析JSON
      const audioResponse = JSON.parse(jsonString);
      console.log('✅ Parsed JSON response:', audioResponse);
      
      // 第三步：提取hex编码的音频数据
      const hexAudio = audioResponse.data?.audio;
      if (!hexAudio) {
        throw new Error('Audio data not found in response');
      }
      
      console.log('🔍 Found hex audio data, length:', hexAudio.length);
      console.log('📊 Audio info:', {
        format: audioResponse.extra_info?.audio_format,
        size: audioResponse.extra_info?.audio_size,
        duration: audioResponse.extra_info?.audio_length,
        sampleRate: audioResponse.extra_info?.audio_sample_rate
      });
      
      // 第四步：hex解码为字节数组
      const bytes = new Uint8Array(hexAudio.length / 2);
      for (let i = 0; i < hexAudio.length; i += 2) {
        bytes[i / 2] = parseInt(hexAudio.substr(i, 2), 16);
      }
      
      console.log('✅ Hex decoded to bytes, final size:', bytes.length);
      return bytes;
      
    } catch (error) {
      console.error('❌ Audio decode error:', error);
      console.log('⚠️ Trying fallback: direct base64 decode');
      
      // 回退方案：尝试直接base64解码（兼容旧格式）
      try {
        const binaryString = atob(data.trim());
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
        return bytes;
      } catch (fallbackError) {
        throw new Error('Unable to decode audio data: ' + error.message);
      }
    }
  }, []);
/**
   * 调用插件API
   */
const callPluginAPI = useCallback(async (endpoint, options = {}) => {
  const url = `/api/plugins/deer_flow${endpoint}`;
  
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  });
  
  if (!response.ok) {
    throw new Error(`API调用失败: ${response.statusText}`);
  }
  
  return await response.json();
}, []);
  /**
   * 下载文件
   */
const handleDownload = useCallback((data, filename, contentType) => {
  // {{CHENGQI:
  // Action: Modified
  // Timestamp: [2025-01-22T10:30:00+08:00]
  // Reason: 修复contentType为undefined时的startsWith错误，添加参数验证和默认值处理
  // Principle_Applied: 防御性编程 - 添加参数验证; 错误处理 - 防止undefined导致的运行时错误
  // Optimization: 增强代码健壮性，确保文件下载功能稳定运行
  // Architectural_Note (AR): 提高下载功能的错误容错能力
  // Documentation_Note (DW): 修复用户报告的"Cannot read properties of undefined (reading 'startsWith')"错误
  // }}
  // {{CHENGQI:
  // Action: Modified
  // Timestamp: [2025-01-17T21:30:00+08:00]
  // Reason: 修复音频下载损坏问题，对于音频文件直接传递原始base64数据，避免重复编码
  // Principle_Applied: KISS - 简化数据处理流程; 数据完整性 - 避免不必要的编码转换
  // Optimization: 区分音频和其他文件类型的处理逻辑，保持数据原始性
  // Architectural_Note (AR): 修复桌面应用下载功能，确保音频文件完整性
  // Documentation_Note (DW): 解决用户反馈的音频下载损坏问题
  // }}
  if (!contentType) {
    console.warn('handleDownload: contentType is undefined, fallback to text/plain');
    contentType = 'text/plain';
  }
  
  try {
    // 参数验证
    if (!data) {
      throw new Error('下载数据不能为空');
    }
    if (!filename) {
      throw new Error('文件名不能为空');
    }
    
    // 为contentType提供默认值
    const safeContentType = contentType || 'application/octet-stream';
    
    console.log('🔽 开始下载文件:', { filename, contentType: safeContentType, dataType: typeof data });
    
    // 检测桌面应用环境
    const isDesktopApp = window.pywebview || window.TKINTER_CLIENT === true;
    
    if (!isDesktopApp) {
      // 浏览器环境，使用原有的下载逻辑
      console.log('🌐 浏览器环境，使用原生下载');
      let blob;
      
      if (typeof data === 'string' && safeContentType !== 'text/plain') {
        try {
          const bytes = decodeAudioData(data);
          blob = new Blob([bytes], { type: safeContentType });
          console.log(`✅ 成功解码音频数据，文件大小: ${bytes.length} bytes`);
        } catch (decodeError) {
          console.error('❌ 音频解码失败:', decodeError);
          blob = new Blob([data], { type: safeContentType });
          console.log('⚠️ 回退到文本格式处理');
        }
      } else if (safeContentType === 'text/plain' || typeof data === 'string') {
        blob = new Blob([data], { type: safeContentType });
      } else {
        blob = new Blob([data], { type: safeContentType });
      }
      
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      console.log(`📁 浏览器下载触发: ${filename}`);
      MessageService.success(`${filename} 下载成功`);
      return;
    }
    
    // 桌面应用环境，调用后端API
    console.log('🖥️ 桌面应用环境，使用Tkinter文件保存');
    
    // 准备数据，区分音频和其他文件类型的处理
    let base64Data = '';
    let encoding = 'base64';
    
    if (typeof data === 'string') {
      if (safeContentType === 'text/plain') {
        // 文本数据直接转换
        base64Data = btoa(unescape(encodeURIComponent(data)));
        encoding = 'base64';
      } else if (typeof contentType === 'string' && contentType.startsWith('audio/')) {
        // 音频数据：直接使用原始base64数据，不进行解码重编码
        console.log('🎵 音频文件：直接使用原始base64数据');
        base64Data = data;
        encoding = 'audio_base64'; // 标记为音频base64，让后端特殊处理
      } else {
        // 其他类型数据
        base64Data = data;
        encoding = 'base64';
      }
    } else {
      // 二进制数据转换为base64
      const bytes = new Uint8Array(data);
      let binary = '';
      for (let i = 0; i < bytes.length; i++) {
        binary += String.fromCharCode(bytes[i]);
      }
      base64Data = btoa(binary);
      encoding = 'base64';
    }
    
    console.log('📦 准备发送数据:', { 
      filename, 
      contentType: safeContentType, 
      encoding, 
      dataLength: base64Data.length 
    });
    
    // 调用后端API保存文件
    callPluginAPI('/save_file', {
      method: 'POST',
      body: JSON.stringify({
        filename: filename,
        data: base64Data,
        content_type: safeContentType,
        encoding: encoding
      })
    }).then(response => {
      console.log('📁 文件保存API响应:', response);
      if (response.success) {
        MessageService.success(response.message || `${filename} 保存成功`);
      } else {
        MessageService.error(response.error || '文件保存失败');
      }
    }).catch(error => {
      console.error('❌ 文件保存API调用失败:', error);
      MessageService.error('文件保存失败: ' + error.message);
    });
    
  } catch (error) {
    console.error('❌ 下载处理错误:', error);
    MessageService.error('文件下载失败: ' + error.message);
  }
}, [decodeAudioData, callPluginAPI]);

// {{CHENGQI:
// Action: Added
// Timestamp: [2025-01-16T19:20:00+08:00]
// Reason: 设置全局handleDownload引用，供SVGToPPTApiService在桌面应用环境下使用
// Principle_Applied: DRY - 复用现有下载逻辑; 全局访问 - 提供统一的下载接口
// Optimization: 使SVG转PPT下载功能在桌面应用下能够使用tk文件对话框
// Architectural_Note (AR): 建立全局下载接口，统一所有下载功能
// Documentation_Note (DW): 为SVG转PPT功能提供桌面应用下载支持
// }}
// 设置全局handleDownload引用，供其他服务使用
useEffect(() => {
  window.currentHandleDownload = handleDownload;
  
  // 清理函数
  return () => {
    if (window.currentHandleDownload === handleDownload) {
      window.currentHandleDownload = null;
    }
  };
}, [handleDownload]);
  // {{CHENGQI:
  // Action: Moved & Corrected
  // Timestamp: [2024-07-30 12:00:00+08:00]
  // Reason: 调用PodcastContent组件工厂，获取播客部分的UI组件
  // Principle_Applied: DRY, 模块化
  // }}
  
  const handlePlayAudio = useCallback((data, contentType) => {
    // {{CHENGQI:
    // Action: Modified
    // Timestamp: [2025-01-22T09:25:00+08:00]
    // Reason: 优化临时音频播放逻辑，改进事件监听和状态管理，确保进度显示正常
    // Principle_Applied: 用户体验 - 稳定的音频播放; 资源管理 - 避免内存泄漏; 状态管理 - 完整的事件处理
    // Optimization: 使用全局AudioManager，添加完整的事件监听，确保进度更新正常
    // Architectural_Note (AR): 统一音频播放体验，所有音频播放都通过AudioManager管理
    // Documentation_Note (DW): 修复临时播放功能，确保进度条和时间显示正常
    // }}
    try {
      console.log('🎵 播放音频，内容类型:', contentType, '数据类型:', typeof data);
      
      // 使用全局AudioManager进行临时播放
      import('../../utils/AudioManager').then(({ default: audioManager }) => {
        // 生成临时播放器ID
        const tempAudioId = `temp_play_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        try {
          // 注册临时音频播放器
          audioManager.registerAudio(tempAudioId, null, data, contentType);
          
          // 添加完整的事件监听器，包括进度更新
          const eventListener = (eventType, eventData) => {
            console.log(`🎵 临时播放器[${tempAudioId}]事件:`, eventType, {
              currentTime: eventData?.currentTime,
              duration: eventData?.duration,
              isPlaying: eventData?.isPlaying
            });
            
            switch (eventType) {
              case 'loadedmetadata':
                console.log(`📊 音频元数据加载完成，时长: ${eventData.duration?.toFixed(1)}秒`);
                break;
                
              case 'timeupdate':
                // 临时播放器的进度更新（可以在这里添加自定义进度显示逻辑）
                if (eventData.duration > 0) {
                  const progress = (eventData.currentTime / eventData.duration * 100).toFixed(1);
                  // 每10%进度记录一次
                  if (Math.floor(progress / 10) !== Math.floor((eventData.currentTime - 1) / eventData.duration * 10)) {
                    console.log(`⏱️ 播放进度: ${progress}% (${eventData.currentTime.toFixed(1)}/${eventData.duration.toFixed(1)}秒)`);
                  }
                }
                break;
                
              case 'ended':
              case 'error':
                // 播放结束或出错时自动清理
                audioManager.removeEventListener(tempAudioId, eventListener);
                audioManager.unregisterAudio(tempAudioId);
                console.log('🧹 临时音频播放器已清理:', tempAudioId);
                
                if (eventType === 'ended') {
                  MessageService.info('音频播放完成');
                } else {
                  MessageService.error('音频播放出错');
                }
                break;
                
              case 'play':
                MessageService.success('开始播放音频');
                break;
                
              case 'pause':
                MessageService.info('音频已暂停');
                break;
            }
          };
          
          audioManager.addEventListener(tempAudioId, eventListener);
          
          // 开始播放
          audioManager.play(tempAudioId).then(() => {
            console.log('✅ 临时音频播放启动成功');
          }).catch(error => {
            console.error('❌ 临时音频播放失败:', error);
            MessageService.error('音频播放失败: ' + error.message);
            // 播放失败时也要清理
            audioManager.removeEventListener(tempAudioId, eventListener);
            audioManager.unregisterAudio(tempAudioId);
          });
          
        } catch (registerError) {
          console.error('❌ 注册临时音频播放器失败:', registerError);
          MessageService.error('音频播放失败: ' + registerError.message);
        }
      }).catch(importError => {
        console.error('❌ 导入AudioManager失败:', importError);
        
        // 回退到原始播放方式
        console.log('⚠️ 回退到原始播放方式');
        try {
          let blob;
          if (typeof data === 'string') {
            const bytes = decodeAudioData(data);
            blob = new Blob([bytes], { type: contentType });
          } else {
            blob = new Blob([data], { type: contentType });
          }
          
          const url = URL.createObjectURL(blob);
          const audio = new Audio(url);
          
          audio.addEventListener('canplay', () => {
            console.log('✅ 音频已准备就绪（回退模式）');
          });
          
          audio.addEventListener('timeupdate', () => {
            if (audio.duration > 0) {
              const progress = (audio.currentTime / audio.duration * 100).toFixed(1);
              console.log(`⏱️ 播放进度（回退模式）: ${progress}%`);
            }
          });
          
          audio.addEventListener('error', (e) => {
            console.error('❌ 音频播放错误:', e);
            MessageService.error('音频格式不支持或文件损坏');
            URL.revokeObjectURL(url);
          });
          
          audio.addEventListener('ended', () => {
            console.log('🎵 音频播放完成（回退模式）');
            URL.revokeObjectURL(url);
            MessageService.info('音频播放完成');
          });
          
          audio.play().catch(error => {
            console.error('❌ 回退播放失败:', error);
            MessageService.error('音频播放失败: ' + error.message);
            URL.revokeObjectURL(url);
          });
          
          MessageService.success('开始播放音频（回退模式）');
          
        } catch (fallbackError) {
          console.error('❌ 回退播放也失败:', fallbackError);
          MessageService.error('音频播放完全失败');
        }
      });
      
    } catch (error) {
      console.error('❌ 处理音频播放请求失败:', error);
      MessageService.error('音频播放失败: ' + error.message);
    }
  }, [decodeAudioData]);
  /**
   * 处理文件上传
   */
  const handleFileUpload = useCallback((info) => {
    const { status } = info.file;
    if (status === 'done') {
      // 这里应该处理文件内容读取
      MessageService.success(`${info.file.name} 文件上传成功`);
    } else if (status === 'error') {
      MessageService.error(`${info.file.name} 文件上传失败`);
    }
  }, []);
  // 播客结果更新回调
  const updatePodcastResult = useCallback((podcastResult) => {
    setResult(podcastResult);
    console.log('播客结果已更新:', podcastResult);
  }, []);

  // {{CHENGQI:
  // Action: Added
  // Timestamp: [2025-01-18T00:58:00+08:00]
  // Reason: 添加播客脚本状态回调处理函数
  // Principle_Applied: 状态管理 - 统一管理播客脚本状态; 组件通信 - 处理子组件状态变化
  // Optimization: 实现播客脚本状态的集中管理
  // Architectural_Note (AR): 完善播客两步生成流程的状态管理
  // Documentation_Note (DW): 新增播客脚本状态处理逻辑
  // }}
  const handlePodcastScriptStatusChange = useCallback((status) => {
    setPodcastScriptGenerated(status.hasScript);
    setPodcastScriptContent(status.scriptContent || '');
  }, []);

  const { 
    ConfigPanel: PodcastConfigPanel, 
    ResultDisplay: PodcastResultDisplay
  } = PodcastContent({ 
    config, 
    updateConfig, 
    result, 
    handleDownload, 
    handlePlayAudio, 
    availableVoices
  });

  // {{CHENGQI:
  // Action: Added
  // Timestamp: [2024-07-30 12:15:00+08:00]
  // Reason: 调用PPTContent组件工厂，获取PPT部分的UI组件
  // Principle_Applied: DRY, 模块化
  // }}


  const { 
    ConfigPanel: ProseConfigPanel, 
    ResultDisplay: ProseResultDisplay 
  } = ProseContent({ config, updateConfig, result, handleDownload });

  // 处理从研究页面传递的数据
  useEffect(() => {
    // 优先使用路由state中的数据
    console.log(`🔄 useEffect 渲染次数:1`);
    if (location.state) {
      const { contentType: routeContentType, sourceContent: routeContent } = location.state;
      if (routeContentType) {
        setContentType(routeContentType);
      }
      if (routeContent) {
        setSourceContent(routeContent);
        MessageService.success('已自动填入研究报告内容');
      }
    } 
    // 其次使用props传递的数据
    else if (initialData?.sourceContent) {
      setSourceContent(initialData.sourceContent);
      MessageService.success('已自动填入研究报告内容');
    }
    // 最后尝试从localStorage加载历史研究结果
    else {
      try {
        const savedResult = localStorage.getItem('deerflow_research_result');
        if (savedResult) {
          setSourceContent(savedResult);
          MessageService.info('已自动填入历史研究报告内容');
        }
      } catch (error) {
        console.warn('加载历史研究数据失败:', error);
      }
    }
  }, [location.state, initialData]);

  // 新增：检查SVG生成状态恢复
  useEffect(() => {
    const checkSvgGenerationStatus = async () => {
      try {
        // 从localStorage获取上次的session_id
        const savedSessionId = localStorage.getItem('svg_generation_session_id');
        if (!savedSessionId) return;

        console.log('🔍 检查SVG生成状态，会话ID:', savedSessionId);

        // 查询后端状态
        const response = await fetch(`/api/svg-ppt/generation-status/${savedSessionId}`);
        if (!response.ok) return;

        const statusData = await response.json();
        console.log('📊 获取到SVG生成状态:', statusData);

        if (statusData.status === 'generating') {
          // 如果还在生成中，恢复生成状态
          setGeneratingSvg(true);
          setSvgProgress({
            stage: statusData.stage,
            message: statusData.message,
            progress: statusData.progress
          });

          // 重新连接WebSocket以接收后续进度更新
          const reconnectProgress = (progressData) => {
            setSvgProgress(progressData);

            if (progressData.stage === '生成完成') {
              setGeneratingSvg(false);
              setSvgGenerated(true);
              localStorage.removeItem('svg_generation_session_id');
              MessageService.success('SVG生成已完成');
            } else if (progressData.stage === '生成失败') {
              setGeneratingSvg(false);
              setSvgProgress({ stage: '', message: '', progress: 0 });
              localStorage.removeItem('svg_generation_session_id');
              MessageService.error('SVG生成失败');
            } else if (progressData.partial || progressData.stage === '部分生成完成') {
              // 处理部分成功的恢复
              setGeneratingSvg(false);
              setSvgGenerated(true);
              localStorage.removeItem('svg_generation_session_id');
              MessageService.warning({
                content: `${progressData.warning || progressData.message}。已生成的页面可以继续使用。`,
                duration: 8
              });
            }
          };

          // 使用svgPptService重新连接WebSocket
          svgPptService.connectWebSocket(savedSessionId, reconnectProgress);
          
          MessageService.info('已恢复SVG生成任务，正在继续生成...');
        } else if (statusData.status === 'completed') {
          // 如果已完成，显示结果
          if (statusData.generated_pages && statusData.generated_pages.length > 0) {
            setSvgContent(statusData.generated_pages);
            setSvgGenerated(true);
            setGeneratingSvg(false);
            MessageService.success('发现已完成的SVG生成结果');
          }
          localStorage.removeItem('svg_generation_session_id');
        } else if (statusData.status === 'partial_completed') {
          // 如果部分成功，显示已生成的页面
          if (statusData.generated_pages && statusData.generated_pages.length > 0) {
            setSvgContent(statusData.generated_pages);
            setSvgGenerated(true);
            setGeneratingSvg(false);
            MessageService.warning({
              content: `发现部分完成的SVG生成结果：已生成 ${statusData.generated_pages.length} 页。${statusData.error || '部分页面生成失败'}`,
              duration: 8
            });
          }
          localStorage.removeItem('svg_generation_session_id');
        } else if (statusData.status === 'failed') {
          // 如果失败，清除状态
          localStorage.removeItem('svg_generation_session_id');
          console.log('❌ SVG生成任务已失败:', statusData.error);
        } else if (statusData.status === 'not_found') {
          // 会话不存在，清除本地记录
          localStorage.removeItem('svg_generation_session_id');
        }

      } catch (error) {
        console.warn('检查SVG生成状态失败:', error);
        // 出错时清除可能过期的session_id
        localStorage.removeItem('svg_generation_session_id');
      }
    };

    // 只在首次加载时检查
    if (contentType === 'ppt') {
      checkSvgGenerationStatus();
    }
  }, [contentType, svgPptService]);

  const contentTypes = [
    { 
      value: 'podcast', 
      label: '播客音频', 
      icon: <SoundOutlined />,
      description: '将文本内容转换为自然流畅的播客音频'
    },
    { 
      value: 'ppt', 
      label: 'PPT演示', 
      icon: <FileImageOutlined />,
      description: '生成专业的PowerPoint演示文稿'
    },
    { 
      value: 'prose', 
      label: '散文文章', 
      icon: <FileTextOutlined />,
      description: '创作优美的散文或技术文章'
    }
  ];
  
  

  /**
   * 生成PPT大纲
   */
  const handleGenerateOutline = useCallback(async () => {
    // {{CHENGQI:
    // Action: Added
    // Timestamp: [2025-01-16T16:25:00+08:00]
    // Reason: 添加生成PPT大纲的方法，支持前端大纲编辑功能
    // Principle_Applied: 单一职责 - 专门处理大纲生成; 用户体验 - 清晰的加载状态和错误处理
    // Optimization: 复用现有的API调用逻辑，添加详细的错误处理
    // Architectural_Note (AR): 实现两步PPT生成的第一步
    // Documentation_Note (DW): 新增大纲生成前端逻辑
    // }}
    if (!sourceContent.trim()) {
      MessageService.warning('请先输入源内容');
      return;
    }
    
    setGeneratingOutline(true);
    try {
      const response = await callPluginAPI('/generate_outline', {
        method: 'POST',
        body: JSON.stringify({
          research_data: sourceContent,
          options: {
            ...config[contentType],
            // 传递PPT相关配置
            ...(contentType === 'ppt' && config.ppt.use_template && {
              use_template: true,
              template_id: config.ppt.template_id
            })
          }
        })
      });
      
      if (response.success) {
        setOutlineContent(response.outline);
        setOutlineGenerated(true);
        MessageService.success(response.message || 'PPT大纲生成成功');
      } else {
        MessageService.error(response.error || 'PPT大纲生成失败');
      }
    } catch (error) {
      console.error('Outline generation error:', error);
      MessageService.error('PPT大纲生成失败: ' + error.message);
    } finally {
      setGeneratingOutline(false);
    }
  }, [sourceContent, contentType, config, callPluginAPI]);

  /**
   * 获取可用语音列表
   */
  const fetchAvailableVoices = useCallback(async () => {
    console.log(`🔄 fetchAvailableVoices 渲染次数:1`);
    try {
      const response = await callPluginAPI('/voices');
      if (response.success) {
        const voices = [];
        
        // 处理系统语音
        if (response.data.system_voice) {
          response.data.system_voice.forEach(voice => {
            voices.push({
              value: voice.voice_id,
              label: voice.voice_name || voice.voice_id,
              description: Array.isArray(voice.description) ? voice.description.join('、') : voice.description,
              type: 'system'
            });
          });
        }
        
        // 处理用户克隆语音
        if (response.data.voice_cloning) {
          response.data.voice_cloning.forEach(voice => {
            voices.push({
              value: voice.voice_id,
              label: `克隆语音 - ${voice.voice_id}`,
              description: Array.isArray(voice.description) ? voice.description.join('、') : voice.description,
              type: 'cloning'
            });
          });
        }
        
        setAvailableVoices(voices);
        
        // 如果当前选择的语音不在列表中，设置为第一个可用语音
        const currentVoice = config.podcast.voice;
        if (voices.length > 0 && !voices.find(v => v.value === currentVoice)) {
          // 只有在真正需要更新时才调用setConfig，避免不必要的重渲染
          const newVoice = voices[0].value;
          if (currentVoice !== newVoice) {
            console.log(`🔄 更新语音设置: ${currentVoice} -> ${newVoice}`);
            setConfig(prev => ({
              ...prev,
              podcast: {
                ...prev.podcast,
                voice: newVoice
              }
            }));
          }
        }
      } else {
        console.error('Failed to fetch voices:', response.error);
        // 使用默认语音列表
        setAvailableVoices([
          { value: 'male-qn-qingse', label: '男声（清晰）', description: '男性、成年、清晰', type: 'system' },
          { value: 'female-shaonv', label: '女声（少女音）', description: '女性、年轻、甜美', type: 'system' },
          { value: 'male-qn-jingying', label: '男声（精英音）', description: '男性、成年、专业', type: 'system' }
        ]);
      }
    } catch (error) {
      console.error('Error fetching voices:', error);
      // 使用默认语音列表
      setAvailableVoices([
        { value: 'male-qn-qingse', label: '男声（清晰）', description: '男性、成年、清晰', type: 'system' },
        { value: 'female-shaonv', label: '女声（少女音）', description: '女性、年轻、甜美', type: 'system' },
        { value: 'male-qn-jingying', label: '男声（精英音）', description: '男性、成年、专业', type: 'system' }
      ]);
         }
   }, [callPluginAPI]);
 
   // 获取可用语音列表
   useEffect(() => {
    console.log(`🔄 useEffect 渲染次数:2`);
     fetchAvailableVoices();
   }, [fetchAvailableVoices]);

   /**
    * 获取PPT模板列表
    */
   const fetchPptTemplates = useCallback(async () => {
     try {
       setLoadingTemplates(true);
       const response = await callPluginAPI('/templates');
       if (response.success) {
         setPptTemplates(response.data);
         
         // 如果有模板且当前没有选择，默认选择第一个
         if (response.data.length > 0 && !config.ppt.template_id) {
           const newTemplateId = response.data[0].id;
           if (config.ppt.template_id !== newTemplateId) {
             console.log(`🔄 更新PPT模板设置: ${config.ppt.template_id} -> ${newTemplateId}`);
             setConfig(prev => ({
               ...prev,
               ppt: {
                 ...prev.ppt,
                 template_id: newTemplateId
               }
             }));
           }
         }
       } else {
         console.error('Failed to fetch templates:', response.error);
         MessageService.warning(`获取模板失败: ${response.error}`);
       }
     } catch (error) {
       console.error('Error fetching templates:', error);
       MessageService.error('获取模板失败，请检查网络连接');
     } finally {
       setLoadingTemplates(false);
     }
   }, [callPluginAPI, config.ppt.template_id]);
// {{CHENGQI:
  // Action: Added
  // Timestamp: [2025-01-19T16:40:00+08:00]
  // Reason: 添加播客脚本编辑器组件，从PodcastContent.jsx移动到这里
  // Principle_Applied: 单一职责 - 专门处理脚本编辑功能; 组件整合 - 将相关功能集中管理
  // Optimization: 统一脚本编辑和生成功能在主组件中
  // Architectural_Note (AR): 实现脚本编辑功能的集中管理
  // Documentation_Note (DW): 移动脚本编辑器组件到主组件
  // }}
  const PodcastScriptEditor = () => {
    if (!sourceContent || sourceContent.trim().length === 0) {
      return (
        <Card 
          title="📝 播客脚本生成" 
          style={{ marginTop: '20px' }}
          extra={
            <span style={{ fontSize: '12px', color: '#999' }}>
              请先输入源内容
            </span>
          }
        >
          <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
            <p>请先在上方输入源内容，然后生成播客脚本</p>
          </div>
        </Card>
      );
    }

    return (
      <Card 
        title="📝 播客脚本生成与编辑" 
        style={{ marginTop: '20px' }}
        extra={
          <span style={{ fontSize: '12px', color: '#999' }}>
            {sourceContent.length} 字符源内容
          </span>
        }
      >
        {!podcastScriptGenerated ? (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            {/* 脚本生成进度显示 */}
            {scriptGenerating && (
              <div style={{ marginBottom: '20px' }}>
                <Progress
                  percent={scriptProgress}
                  status="active"
                  showInfo={true}
                  strokeColor="#52c41a"
                  style={{ marginBottom: '8px' }}
                />
                <div style={{ fontSize: '14px', color: '#666' }}>
                  📝 正在生成播客脚本，请稍候... ({scriptProgress}%)
                </div>
                {scriptTaskId && (
                  <div style={{ fontSize: '12px', color: '#999', marginTop: '4px' }}>
                    任务ID: {scriptTaskId}
                  </div>
                )}
              </div>
            )}
            
            <p style={{ color: '#666', marginBottom: '16px', fontSize: '14px' }}>
              根据您选择的播客风格（{config.podcast.style === 'conversational' ? '对话式' : 
                config.podcast.style === 'educational' ? '教育性' : '访谈式'}）
              和时长（{config.podcast.duration === 'short' ? '5-10分钟' : 
                config.podcast.duration === 'medium' ? '10-20分钟' : '20-30分钟'}），
              AI将为您生成专业的播客对话脚本
            </p>
            <Button 
              type="primary" 
              icon={<EditOutlined />}
              loading={scriptGenerating}
              onClick={handleGeneratePodcastScript}
              size="large"
              style={{ minWidth: '160px' }}
            >
              {scriptGenerating ? '正在生成脚本...' : '生成播客脚本'}
            </Button>
          </div>
        ) : (
          <div>
            <div style={{ 
              marginBottom: '16px', 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'space-between',
              padding: '12px',
              backgroundColor: '#f6ffed',
              border: '1px solid #b7eb8f',
              borderRadius: '6px'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span style={{ color: '#52c41a', fontWeight: 'bold' }}>✅ 脚本已生成</span>
                <span style={{ fontSize: '12px', color: '#666' }}>
                  您可以编辑下方脚本内容
                </span>
              </div>
              <Button 
                type="link" 
                icon={<ReloadOutlined />}
                onClick={() => {
                  setPodcastScriptGenerated(false);
                  setPodcastScriptContent('');
                  handleGeneratePodcastScript();
                }}
                loading={scriptGenerating}
                size="small"
              >
                重新生成
              </Button>
            </div>
            
            <TextArea
              value={podcastScriptContent}
              onChange={(e) => setPodcastScriptContent(e.target.value)}
              placeholder="播客脚本内容将显示在这里，您可以自由编辑..."
              rows={15}
              style={{ 
                fontSize: '13px',
                lineHeight: '1.6',
                fontFamily: 'Microsoft YaHei, SimHei, sans-serif',
                marginBottom: '12px'
              }}
            />
            
            <div style={{ 
              padding: '12px',
              backgroundColor: '#fafafa',
              borderRadius: '6px',
              textAlign: 'center'
            }}>
              <span style={{ fontSize: '12px', color: '#666' }}>
                字符数: {podcastScriptContent.length} 
                {podcastScriptContent.length > 10000 && (
                  <span style={{ color: '#ff7875' }}> (超过10000字符将自动分段处理)</span>
                )}
              </span>
              <div style={{ marginTop: '8px', fontSize: '12px', color: '#52c41a' }}>
                ✅ 脚本编辑完成后，请使用下方的"生成播客音频"按钮
              </div>
            </div>
          </div>
        )}
      </Card>
    );
  };
  
  // {{CHENGQI:
  // Action: Added
  // Timestamp: [2025-06-22T15:50:00+08:00]
  // Reason: 添加SVG生成处理函数，实现调用后端API生成SVG内容的功能
  // Principle_Applied: 单一职责 - 专门处理SVG生成; 错误处理 - 完整的异常捕获
  // Optimization: 提供详细的进度反馈，优化用户体验
  // Architectural_Note (AR): 集成SVGPPTService，实现前端SVG生成流程
  // Documentation_Note (DW): 新增SVG生成处理逻辑
  // }}
  /**
   * 生成SVG内容
   */
  const handleGenerateSvg = useCallback(async () => {
    if (!sourceContent.trim()) {
      MessageService.warning('请先输入源内容');
      return;
    }

    // 开始新的生成时清除缓存
    clearSvgCache();
    // 清除设计规范缓存，准备生成新的
    clearDesignSpecCache();
    
    setGeneratingSvg(true);
    setSvgProgress({ stage: '准备中', message: '正在初始化SVG生成...', progress: 0 });
    setSvgContent([]);
    setSvgGenerated(false);
    
    // 用于存储增量接收的SVG页面 - 移到try块外部以便catch块访问
    const incrementalSvgPagesMap = new Map();
    
    try {
      // 使用PPT配置中的SVG相关选项
      const svgConfig = {
        svg_style: config.ppt.svg_style || 'modern',
        svg_color_theme: config.ppt.svg_color_theme || 'blue',
        svg_layout: config.ppt.svg_layout || 'standard',
        svg_custom_requirements: config.ppt.svg_custom_requirements || '',
        // 新增：生成方式和模板选择
        svg_generation_mode: config.ppt.svg_generation_mode || 'free',
        template_set_name: config.ppt.svg_template_set || null,
        color_theme: config.ppt.svg_color_theme || 'blue'
      };
      
      console.log('🎨 开始生成SVG内容，配置:', svgConfig);
      
      // 生成session_id
      const sessionId = `svg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // 保存session_id到localStorage，用于状态恢复
      localStorage.setItem('svg_generation_session_id', sessionId);
      setOriginalSessionId(sessionId); // 保存到状态中，用于重新生成
      console.log('💾 已保存SVG生成会话ID:', sessionId);
      
      const svgData = await svgPptService.generateSVGContent(
        sourceContent,
        svgConfig,
        (progress) => {
          console.log('📊 SVG生成进度更新:', progress);
          console.log('📊 进度详情 - progress:', progress.progress, 'stage:', progress.stage, 'message:', progress.message);
          
          // 优化进度显示，如果有页面进度信息，则显示更详细的信息
          const enhancedProgress = { ...progress };
          if (progress.progress_info) {
            const { completed_pages, total_pages, percentage } = progress.progress_info;
            enhancedProgress.message = `${progress.stage} - ${completed_pages}/${total_pages} 页 (${percentage.toFixed(1)}%)`;
            console.log('📈 页面进度:', `${completed_pages}/${total_pages} (${percentage.toFixed(1)}%)`);
          }
          
          setSvgProgress(enhancedProgress);
          
          // 处理增量SVG页面完成事件
          if (progress.type === 'svg_page_completed' && progress.page_data) {
            console.log('🎨 收到增量SVG页面:', progress.page_data);
            
            // 转换页面数据格式
            const svgPageItem = {
              content: progress.page_data.svg_code,
              title: progress.page_data.title || `幻灯片 ${progress.page_data.page_number}`,
              index: progress.page_data.page_number - 1,
              page_number: progress.page_data.page_number,
              generated_at: progress.page_data.generated_at,
              layout_type: progress.page_data.layout_type
            };
            
            // 添加到增量Map（避免重复）
            incrementalSvgPagesMap.set(progress.page_data.page_number, svgPageItem);
            
            // 实时更新SVG内容显示（按页码排序）
            const sortedPages = Array.from(incrementalSvgPagesMap.values()).sort((a, b) => a.page_number - b.page_number);
            setSvgContent([...sortedPages]);
            
            console.log(`✨ 已接收第 ${progress.page_data.page_number} 页，当前共有 ${sortedPages.length} 页`);
          }
          
          // 处理部分成功的情况
          if (progress.partial) {
            console.warn('⚠️ SVG部分生成成功:', progress.message);
            MessageService.warning({
              content: `${progress.warning || progress.message}。已生成的页面可以继续使用。`,
              duration: 8
            });
            setSvgGenerated(true); // 标记为已生成，允许用户使用部分结果
          }
          
          // 如果生成完成，清除localStorage中的session_id
          if (progress.stage === '生成完成' || progress.stage === '部分生成完成' || progress.progress >= 100) {
            setTimeout(() => {
              localStorage.removeItem('svg_generation_session_id');
              console.log('🗑️ 已清除SVG生成会话ID（生成完成）');
            }, 1000); // 延迟清除，确保状态更新完成
          }
        },
        sessionId  // 传递session_id给service
      );

      // 最终设置完整的SVG数据（如果增量接收未完成）
      let finalSvgContent = [];
      if (svgData && svgData.length > 0) {
        const incrementalPageCount = incrementalSvgPagesMap.size;
        // 如果增量接收的页面数量与最终结果不一致，使用最终结果
        if (incrementalPageCount !== svgData.length) {
          console.log('🔄 使用最终SVG数据，增量接收可能不完整');
          setSvgContent(svgData);
          finalSvgContent = svgData;
        } else {
          console.log('✅ 增量接收完整，使用增量数据');
          const sortedPages = Array.from(incrementalSvgPagesMap.values()).sort((a, b) => a.page_number - b.page_number);
          finalSvgContent = sortedPages;
        }
      } else {
        // 如果最终结果为空，使用增量接收的数据
        const sortedPages = Array.from(incrementalSvgPagesMap.values()).sort((a, b) => a.page_number - b.page_number);
        finalSvgContent = sortedPages;
      }
      
      setSvgGenerated(true);
      setGeneratingSvg(false);
      
      const finalPageCount = finalSvgContent.length;
      MessageService.success(`成功生成 ${finalPageCount} 页SVG内容`);
      
      // 保存SVG到缓存
      if (finalSvgContent.length > 0) {
        saveSvgToCache(finalSvgContent, {
          status: 'completed',
          pageCount: finalPageCount,
          progress: 100,
          stage: '生成完成'
        });
      }
      
      // 生成成功，清除session_id
      localStorage.removeItem('svg_generation_session_id');
      console.log('✅ SVG生成完成，已清除会话ID');
      
    } catch (error) {
      console.error('❌ SVG生成错误:', error);
      setGeneratingSvg(false);
      setSvgProgress({ stage: '', message: '', progress: 0 });
      
      // 检查是否有已生成的页面（通过增量接收）
      const hasGeneratedPages = incrementalSvgPagesMap.size > 0;
      
      if (hasGeneratedPages) {
        // 如果有已生成的页面，保留它们并提示部分成功
        const sortedPages = Array.from(incrementalSvgPagesMap.values()).sort((a, b) => a.page_number - b.page_number);
        setSvgContent(sortedPages);
        setSvgGenerated(true);
        
        // 保存部分成功的页面到缓存
        saveSvgToCache(sortedPages, {
          status: 'partial_completed',
          pageCount: sortedPages.length,
          progress: 80,
          stage: '部分生成完成',
          error: error.message
        });
        
        MessageService.warning({
          content: `生成过程中出现错误，但已成功生成 ${sortedPages.length} 页内容。您可以继续使用这些页面。错误信息: ${error.message}`,
          duration: 10
        });
        
        console.log(`⚠️ 生成出错但保留了 ${sortedPages.length} 页已生成内容`);
      } else {
        // 如果没有任何页面生成，完全清空
        setSvgContent([]);
        setSvgGenerated(false);
        MessageService.error(`SVG生成失败: ${error.message}`);
      }
      
      // 出错时也要清除session_id
      localStorage.removeItem('svg_generation_session_id');
      console.log('🗑️ 已清除SVG生成会话ID（生成失败）');
    }
  }, [sourceContent, config.ppt, svgPptService]);

  /**
   * 重新生成单张SVG
   */
  const handleRegenerateSingleSvg = useCallback(async (pageIndex, pageTitle) => {
    if (!sourceContent.trim()) {
      MessageService.warning('请先输入源内容');
      return;
    }

    if (pageIndex < 0 || pageIndex >= svgContent.length) {
      MessageService.warning('页面索引无效');
      return;
    }

    setRegeneratingSvgIndex(pageIndex);
    
    try {
      // 使用当前的配置
      const svgConfig = {
        svg_style: config.ppt.svg_style || 'modern',
        svg_color_theme: config.ppt.svg_color_theme || 'blue',
        svg_layout: config.ppt.svg_layout || 'standard',
        svg_custom_requirements: config.ppt.svg_custom_requirements || ''
      };
      
      console.log(`🔄 开始重新生成第 ${pageIndex + 1} 页 SVG：${pageTitle}`);
      
      // 生成session_id
      const sessionId = `svg_regen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // 获取原始session_id，优先级：状态中的 > localStorage中的
      let originalSId = originalSessionId;
      if (!originalSId) {
        originalSId = localStorage.getItem('svg_generation_session_id');
        if (originalSId) {
          setOriginalSessionId(originalSId);
          console.log('📦 从localStorage恢复原始会话ID:', originalSId);
        }
      }
      
      console.log('🔗 使用原始会话ID:', originalSId, '新会话ID:', sessionId);
      
      // 调用后端重新生成单页 SVG
      const response = await fetch('/api/svg-ppt/regenerate-single-svg', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: sourceContent,
          page_index: pageIndex,
          page_title: pageTitle,
          style_config: svgConfig,
          custom_requirements: config.ppt.svg_custom_requirements || '',
          session_id: sessionId,
          original_session_id: originalSId || '' // 传递原始会话ID
        })
      });

      if (!response.ok) {
        throw new Error(`请求失败: ${response.status}`);
      }

      const result = await response.json();
      console.log('单页重新生成响应:', result);

      if (result.success) {
        // {{CHENGQI:
        // Action: Added
        // Timestamp: 2025-01-16 22:45:00 +08:00
        // Reason: 为单个SVG重新生成添加轮询机制，确保状态跟踪的可靠性
        // Principle_Applied: 可靠性 - 多重保障机制; 用户体验 - 确保状态同步
        // Optimization: WebSocket + 轮询双重保障，防止消息丢失
        // Architectural_Note (AR): 增强单个SVG重新生成的可靠性
        // Documentation_Note (DW): 添加轮询备份机制，提高可靠性
        // }}
        
        // 保存重新生成会话信息到localStorage，用于状态恢复
        localStorage.setItem(`svg_regeneration_${sessionId}`, JSON.stringify({
          sessionId,
          pageIndex,
          pageTitle,
          startTime: Date.now(),
          originalSessionId: originalSId || ''
        }));
        
        let wsConnected = false;
        let wsAttempted = false;
        let pollInterval = null;
        
        // 主要通信方式：WebSocket
        const connectWebSocket = () => {
          const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
          const wsHost = window.location.host;
          const ws = new WebSocket(`${wsProtocol}//${wsHost}/api/svg-ppt/svg-progress/${sessionId}`);
          
          const wsTimeout = setTimeout(() => {
            if (!wsConnected) {
              console.warn('WebSocket连接超时，启用轮询备份');
              ws.close();
              startPolling();
            }
          }, 10000); // 10秒超时
          
          ws.onopen = () => {
            console.log('重新生成进度WebSocket连接已建立');
            wsConnected = true;
            clearTimeout(wsTimeout);
            if (pollInterval) {
              clearInterval(pollInterval);
              pollInterval = null;
            }
          };
          
          ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            console.log('重新生成进度更新:', data);
            
            if (data.type === 'svg_page_regenerated') {
              // 页面重新生成完成，更新对应页面
              handleRegenerationSuccess(data.page_data, pageIndex, sessionId);
              ws.close();
            } else if (data.type === 'svg_regeneration_error') {
              MessageService.error(`重新生成失败: ${data.message}`);
              cleanupRegeneration(sessionId);
              ws.close();
            } else if (data.type === 'svg_single_regeneration_progress') {
              // 处理进度更新
              console.log(`重新生成进度: ${data.progress}% - ${data.message}`);
            }
          };
          
          ws.onerror = (error) => {
            console.error('重新生成WebSocket错误:', error);
            clearTimeout(wsTimeout);
            if (!wsAttempted) {
              wsAttempted = true;
              setTimeout(() => {
                console.log('尝试重新连接WebSocket...');
                connectWebSocket();
              }, 2000);
            } else {
              console.warn('WebSocket连接失败，启用轮询备份');
              startPolling();
            }
          };
          
          ws.onclose = () => {
            console.log('重新生成WebSocket连接关闭');
            clearTimeout(wsTimeout);
            wsConnected = false;
          };
          
          return ws;
        };
        
        // 备用通信方式：轮询
        const startPolling = () => {
          if (pollInterval) return; // 防止重复启动
          
          console.log('启动单页重新生成状态轮询...');
          
          const pollStatus = async () => {
            try {
              const statusResponse = await fetch(`/api/svg-ppt/generation-status/${sessionId}`);
              if (!statusResponse.ok) return;
              
              const statusData = await statusResponse.json();
              console.log('轮询到重新生成状态:', statusData);
              
              if (statusData.status === 'completed') {
                // 重新生成完成
                if (statusData.generated_pages && statusData.generated_pages.length > 0) {
                  // 查找对应页面
                  const targetPage = statusData.generated_pages.find(page => 
                    page.page_number === pageIndex + 1
                  );
                  if (targetPage) {
                    handleRegenerationSuccess(targetPage, pageIndex, sessionId);
                  }
                }
                clearInterval(pollInterval);
                pollInterval = null;
              } else if (statusData.status === 'failed') {
                // 重新生成失败
                MessageService.error(`重新生成失败: ${statusData.error || '未知错误'}`);
                cleanupRegeneration(sessionId);
                clearInterval(pollInterval);
                pollInterval = null;
              } else if (statusData.status === 'not_found') {
                // 会话不存在，可能已清理
                console.warn('重新生成会话不存在，停止轮询');
                cleanupRegeneration(sessionId);
                clearInterval(pollInterval);
                pollInterval = null;
              }
              // status === 'regenerating' 继续轮询
              
            } catch (error) {
              console.warn('轮询重新生成状态失败:', error);
              // 继续轮询，不中断
            }
          };
          
          // 立即执行一次，然后每3秒轮询一次
          pollStatus();
          pollInterval = setInterval(pollStatus, 3000);
          
          // 设置轮询超时（5分钟）
          setTimeout(() => {
            if (pollInterval) {
              console.warn('重新生成轮询超时，停止轮询');
              clearInterval(pollInterval);
              pollInterval = null;
              MessageService.error('重新生成超时，请重试');
              cleanupRegeneration(sessionId);
            }
          }, 300000); // 5分钟超时
        };
        
        // 处理重新生成成功
        const handleRegenerationSuccess = (pageData, targetIndex, sessionId) => {
          const newPageData = {
            content: pageData.content || pageData.svg_code,
            title: pageData.title,
            index: targetIndex,
            page_number: targetIndex + 1,
            generated_at: pageData.generated_at,
            layout_type: pageData.layout_type,
            regenerated: true
          };
          
          setSvgContent(prevContent => {
            const newContent = [...prevContent];
            newContent[targetIndex] = newPageData;
            return newContent;
          });
          
          MessageService.success(`第 ${targetIndex + 1} 页重新生成成功！`);
          cleanupRegeneration(sessionId);
        };
        
        // 清理重新生成状态
        const cleanupRegeneration = (sessionId) => {
          setRegeneratingSvgIndex(null);
          localStorage.removeItem(`svg_regeneration_${sessionId}`);
          if (pollInterval) {
            clearInterval(pollInterval);
            pollInterval = null;
          }
        };
        
        // 启动WebSocket连接
        connectWebSocket();
        
      } else {
        throw new Error(result.message || '启动重新生成任务失败');
      }
      
    } catch (error) {
      console.error('❌ 重新生成单页SVG错误:', error);
      MessageService.error(`重新生成失败: ${error.message}`);
      setRegeneratingSvgIndex(null);
    }
  }, [sourceContent, config.ppt, svgContent.length]);

  // {{CHENGQI:
  // Action: Added
  // Timestamp: 2025-01-16 22:50:00 +08:00
  // Reason: 添加单个SVG重新生成状态恢复机制，支持页面刷新后的状态恢复
  // Principle_Applied: 用户体验 - 支持状态恢复; 可靠性 - 防止状态丢失
  // Optimization: 页面刷新后自动恢复重新生成状态
  // Architectural_Note (AR): 完善状态管理和恢复机制
  // Documentation_Note (DW): 新增单个SVG重新生成状态恢复逻辑
  // }}
  
  /**
   * 检查并恢复单个SVG重新生成状态
   */
  const checkPendingSvgRegeneration = useCallback(async () => {
    try {
      // 检查localStorage中是否有未完成的重新生成任务
      const allKeys = Object.keys(localStorage);
      const regenKeys = allKeys.filter(key => key.startsWith('svg_regeneration_'));
      
      for (const key of regenKeys) {
        try {
          const regenInfo = JSON.parse(localStorage.getItem(key));
          const { sessionId, pageIndex, pageTitle, startTime } = regenInfo;
          
          // 检查任务是否超时（10分钟）
          if (Date.now() - startTime > 600000) {
            console.log('清理超时的重新生成任务:', sessionId);
            localStorage.removeItem(key);
            continue;
          }
          
          console.log('发现未完成的重新生成任务:', sessionId, `第${pageIndex + 1}页`);
          
          // 查询任务状态
          const response = await fetch(`/api/svg-ppt/generation-status/${sessionId}`);
          if (!response.ok) {
            localStorage.removeItem(key);
            continue;
          }
          
          const statusData = await response.json();
          
          if (statusData.status === 'completed') {
            // 任务已完成，恢复结果
            if (statusData.generated_pages && statusData.generated_pages.length > 0) {
              const targetPage = statusData.generated_pages.find(page => 
                page.page_number === pageIndex + 1
              );
              if (targetPage) {
                console.log('恢复已完成的重新生成结果:', `第${pageIndex + 1}页`);
                
                const newPageData = {
                  content: targetPage.content || targetPage.svg_code,
                  title: targetPage.title,
                  index: pageIndex,
                  page_number: pageIndex + 1,
                  generated_at: targetPage.generated_at,
                  layout_type: targetPage.layout_type,
                  regenerated: true
                };
                
                setSvgContent(prevContent => {
                  const newContent = [...prevContent];
                  if (newContent[pageIndex]) {
                    newContent[pageIndex] = newPageData;
                  }
                  return newContent;
                });
                
                MessageService.success(`已恢复第 ${pageIndex + 1} 页的重新生成结果`);
              }
            }
            localStorage.removeItem(key);
          } else if (statusData.status === 'regenerating') {
            // 任务还在进行中，恢复状态并继续监控
            console.log('恢复进行中的重新生成任务:', sessionId);
            setRegeneratingSvgIndex(pageIndex);
            
            // 启动轮询监控
            const pollRegenStatus = async () => {
              try {
                const statusResponse = await fetch(`/api/svg-ppt/generation-status/${sessionId}`);
                if (!statusResponse.ok) return;
                
                const currentStatus = await statusResponse.json();
                
                if (currentStatus.status === 'completed') {
                  if (currentStatus.generated_pages && currentStatus.generated_pages.length > 0) {
                    const targetPage = currentStatus.generated_pages.find(page => 
                      page.page_number === pageIndex + 1
                    );
                    if (targetPage) {
                      const newPageData = {
                        content: targetPage.content || targetPage.svg_code,
                        title: targetPage.title,
                        index: pageIndex,
                        page_number: pageIndex + 1,
                        generated_at: targetPage.generated_at,
                        layout_type: targetPage.layout_type,
                        regenerated: true
                      };
                      
                      setSvgContent(prevContent => {
                        const newContent = [...prevContent];
                        newContent[pageIndex] = newPageData;
                        return newContent;
                      });
                      
                      MessageService.success(`第 ${pageIndex + 1} 页重新生成完成！`);
                    }
                  }
                  setRegeneratingSvgIndex(null);
                  localStorage.removeItem(key);
                  clearInterval(pollInterval);
                } else if (currentStatus.status === 'failed' || currentStatus.status === 'not_found') {
                  setRegeneratingSvgIndex(null);
                  localStorage.removeItem(key);
                  clearInterval(pollInterval);
                  if (currentStatus.status === 'failed') {
                    MessageService.error(`第 ${pageIndex + 1} 页重新生成失败: ${currentStatus.error || '未知错误'}`);
                  }
                }
              } catch (error) {
                console.warn('恢复轮询状态失败:', error);
              }
            };
            
            const pollInterval = setInterval(pollRegenStatus, 3000);
            setTimeout(() => clearInterval(pollInterval), 300000); // 5分钟后停止
            
          } else if (statusData.status === 'failed' || statusData.status === 'not_found') {
            // 任务失败或不存在，清理
            localStorage.removeItem(key);
            if (statusData.status === 'failed') {
              console.warn('发现失败的重新生成任务:', sessionId, statusData.error);
            }
          }
          
        } catch (error) {
          console.warn('处理重新生成恢复任务失败:', error);
          localStorage.removeItem(key);
        }
      }
      
    } catch (error) {
      console.warn('检查重新生成状态失败:', error);
    }
  }, []);

  // 在组件加载时检查未完成的重新生成任务
  useEffect(() => {
    if (contentType === 'ppt' && svgContent.length > 0) {
      // 延迟执行，确保svgContent已加载
      setTimeout(checkPendingSvgRegeneration, 1000);
    }
  }, [contentType, svgContent.length, checkPendingSvgRegeneration]);

  /**
   * 转换SVG为PPT
   */
  const handleConvertToPpt = useCallback(async () => {
    if (!svgContent || svgContent.length === 0) {
      MessageService.warning('请先生成SVG内容');
      return;
    }

    setConvertingToPpt(true);
    
    try {
      // 使用新的API服务生成真正的PPT文件
      const { default: SVGToPPTApiService } = await import('../../../service/svgToPptApiService.js');
      const apiService = new SVGToPPTApiService();
      
      // 提取SVG内容字符串数组
      const svgStringList = svgContent.map(item => item.content || item);
      
      // 完整的SVG转PPT流程：SVG → PNG → 后端生成PPT → 下载
      const result = await apiService.processSVGToPPT(
        svgStringList,
        {
          title: `SVG演示文稿_${new Date().toISOString().slice(0, 10)}`,
          author: 'DeerFlow SVG转PPT工具',
          width: 1920,
          height: 1080,
          quality: 0.95,
          backgroundColor: '#ffffff',
          filename: `SVG_PPT_${new Date().toISOString().slice(0, 10)}.pptx`
        },
        (progress) => {
          // 更新进度显示
          setSvgProgress(progress);
          console.log('SVG转PPT进度:', progress);
        }
      );

      MessageService.success(`真正的PPT文件下载成功！包含 ${result.totalSlides} 张幻灯片，文件大小: ${(result.fileSize / 1024 / 1024).toFixed(2)}MB`);
      
      // 更新结果状态
      setResult({
        contentType: 'svg_ppt',
        data: {
          success: true,
          totalSlides: result.totalSlides,
          failedConversions: result.failedConversions,
          fileSize: result.fileSize,
          downloadResult: result.downloadResult
        },
        filename: result.downloadResult.filename,
        content_type: 'application/vnd.openxmlformats-presentationml.presentation'
      });

    } catch (error) {
      console.error('SVG转PPT错误:', error);
      MessageService.error(`转换失败: ${error.message}`);
    } finally {
      setConvertingToPpt(false);
    }
  }, [svgContent]);

   const { 
    ConfigPanel: PPTConfigPanel, 
    OutlineEditor: PPTOutlineEditor,
    ResultDisplay: PPTResultDisplay,
    SVGPreviewModal
  } = PPTContent({ 
    config, updateConfig, result, handleDownload,
    outlineContent, setOutlineContent, outlineGenerated, generatingOutline, handleGenerateOutline, sourceContent,
    pptTemplates, loadingTemplates, fetchPptTemplates,
    // SVG相关功能
    svgContent,
    svgGenerating: generatingSvg,
    svgProgress,
    svgPreviewVisible,
    svgCurrentIndex,
    handleGenerateSvg,
    handleConvertToPpt,
    setSvgPreviewVisible,
    setSvgCurrentIndex,
    // 新增：单张SVG重新生成功能
    handleRegenerateSingleSvg,
    regeneratingSvgIndex
  });

     // {{CHENGQI:
   // Action: Modified
   // Timestamp: [2025-06-22T15:58:00+08:00]
   // Reason: 移除独立SVGPPTContent的引用，SVG功能已集成到PPTContent内部
   // Principle_Applied: 简化架构 - 减少重复组件; 集成 - SVG功能并入PPT类型
   // Optimization: 简化代码结构，统一管理PPT和SVG功能
   // Architectural_Note (AR): 调整架构，SVG功能集成到PPT内部
   // Documentation_Note (DW): 根据用户反馈移除独立组件引用
   // }}
    // 当选择PPT类型时，获取模板列表
    useEffect(() => {
      if (contentType === 'ppt' && config.ppt.use_template && pptTemplates.length === 0) {
        fetchPptTemplates();
      }
    }, [contentType, config.ppt.use_template, pptTemplates.length, fetchPptTemplates]);
    
   // {{CHENGQI:
   // Action: Added
   // Timestamp: [2025-01-16T16:40:00+08:00]
   // Reason: 添加内容类型切换时的大纲状态重置逻辑
   // Principle_Applied: 状态管理 - 清理无关状态; 用户体验 - 避免状态混乱
   // Optimization: 确保大纲状态只在PPT类型下有效
   // Architectural_Note (AR): 完善状态管理逻辑
   // Documentation_Note (DW): 添加状态清理逻辑
   // }}
   // 当内容类型切换时，重置大纲相关状态
   useEffect(() => {
     if (contentType !== 'ppt') {
       setOutlineContent('');
       setOutlineGenerated(false);
       setGeneratingOutline(false);
     }
   }, [contentType]);

   // {{CHENGQI:
   // Action: Added
   // Timestamp: [2025-06-23T10:45:00+08:00]
   // Reason: 添加SVG生成状态恢复功能，解决用户切换页面后无法看到生成结果的问题
   // Principle_Applied: 状态持久化 - 保持用户操作连续性; 用户体验 - 避免状态丢失
   // Optimization: 通过localStorage和WebSocket重连恢复生成进度和已生成的SVG页面
   // Architectural_Note (AR): 实现客户端状态恢复机制
   // Documentation_Note (DW): 解决页面切换时状态丢失问题
   // }}
   // SVG生成状态恢复功能（包括缓存恢复）
   useEffect(() => {
     const restoreSvgGenerationState = async () => {
       // 只在PPT的SVG模式下进行状态恢复
       if (contentType !== 'ppt' || !config.ppt.use_svg) {
         return;
       }

       const savedSessionId = localStorage.getItem('svg_generation_session_id');
       
       // 如果没有正在进行的生成任务，尝试从缓存恢复
       if (!savedSessionId) {
         const cachedData = loadSvgFromCache();
         if (cachedData && cachedData.content.length > 0) {
           console.log('📂 从缓存恢复SVG:', cachedData.content.length + ' 页');
           setSvgContent(cachedData.content);
           setSvgGenerated(true);
           setSvgProgress({
             stage: '已从缓存恢复',
             message: `已恢复 ${cachedData.content.length} 页SVG内容（${Math.round((Date.now() - cachedData.timestamp) / 1000 / 60)}分钟前生成）`,
             progress: 100
           });
           
           MessageService.success({
             content: `已从缓存恢复 ${cachedData.content.length} 页SVG内容`,
             duration: 3
           });
         }
         return;
       }

       console.log('🔄 发现未完成的SVG生成任务，尝试恢复状态...', savedSessionId);

       try {
         // 查询当前生成状态
         const response = await fetch(`${svgPptService.baseURL}/generation-status/${savedSessionId}`);
         if (!response.ok) {
           throw new Error(`HTTP ${response.status}`);
         }

         const statusData = await response.json();
         console.log('📊 获取到SVG生成状态:', statusData);

         if (statusData.status === 'not_found') {
           // 会话不存在或已结束，清除localStorage
           localStorage.removeItem('svg_generation_session_id');
           console.log('🗑️ 会话已结束，清除本地状态');
           return;
         }

         // 恢复状态显示
         setSvgProgress({
           stage: statusData.stage,
           message: statusData.message,
           progress: statusData.progress,
           error: statusData.error
         });

         // 如果还在生成中，恢复WebSocket连接
         if (statusData.status === 'generating') {
           setGeneratingSvg(true);
           console.log('🔄 恢复WebSocket连接以继续接收进度...');

           // 重新连接WebSocket监听进度
           await svgPptService.connectWebSocket(savedSessionId, (progress) => {
             console.log('📊 恢复的进度更新:', progress);
             setSvgProgress(progress);

             // 处理增量SVG页面完成事件
             if (progress.type === 'svg_page_completed' && progress.page_data) {
               console.log('🎨 恢复接收到增量SVG页面:', progress.page_data);

               // 转换页面数据格式
               const svgPageItem = {
                 content: progress.page_data.svg_code,
                 title: progress.page_data.title || `幻灯片 ${progress.page_data.page_number}`,
                 index: progress.page_data.page_number - 1,
                 page_number: progress.page_data.page_number,
                 generated_at: progress.page_data.generated_at,
                 layout_type: progress.page_data.layout_type
               };

               // 实时更新SVG内容显示
               setSvgContent(prevContent => {
                 const updatedContent = [...prevContent, svgPageItem];
                 return updatedContent.sort((a, b) => a.page_number - b.page_number);
               });

               console.log(`✨ 恢复接收第 ${progress.page_data.page_number} 页`);
             }

             // 如果生成完成，更新状态
             if (progress.stage === '生成完成' || progress.progress >= 100) {
               setSvgGenerated(true);
               setGeneratingSvg(false);
               localStorage.removeItem('svg_generation_session_id');
               console.log('✅ SVG生成恢复完成');
             }
           });
         } else if (statusData.status === 'completed') {
           // 已完成的任务，恢复已生成的页面
           if (statusData.generated_pages && statusData.generated_pages.length > 0) {
             const restoredContent = statusData.generated_pages.map((page, index) => ({
               content: page.svg_code,
               title: page.title || `幻灯片 ${page.page_number}`,
               index: page.page_number - 1,
               page_number: page.page_number,
               generated_at: page.generated_at,
               layout_type: page.layout_type
             }));

             setSvgContent(restoredContent.sort((a, b) => a.page_number - b.page_number));
             setSvgGenerated(true);
             console.log(`✅ 恢复了 ${restoredContent.length} 页已生成的SVG内容`);
           }

           setGeneratingSvg(false);
           localStorage.removeItem('svg_generation_session_id');
         } else if (statusData.status === 'failed') {
           // 生成失败，清除状态
           setGeneratingSvg(false);
           setSvgProgress({ stage: '', message: '', progress: 0 });
           localStorage.removeItem('svg_generation_session_id');
           console.log('❌ 检测到生成失败，清除状态');
         }

       } catch (error) {
         console.warn('⚠️ 状态恢复失败:', error);
         // 恢复失败，清除localStorage避免重复尝试
         localStorage.removeItem('svg_generation_session_id');
       }
     };

     // 延迟执行，确保组件状态初始化完成
     setTimeout(restoreSvgGenerationState, 500);
   }, [contentType, config.ppt.use_svg, svgPptService]);

   /**
    * 生成内容
    */
   const handleGenerate = useCallback(async () => {
     // {{CHENGQI:
     // Action: Modified
     // Timestamp: [2025-06-22T17:20:00+08:00]
     // Reason: 修改生成内容方法，支持SVG模式的PPT生成，根据use_svg参数选择不同的生成路径
     // Principle_Applied: 灵活性 - 支持多种PPT生成模式; 单一职责 - 根据配置选择合适的生成方法
     // Optimization: 前端路由方案 - 根据use_svg参数选择API调用路径，避免后端复杂修改
     // Architectural_Note (AR): 实现前端路由方案，根据use_svg参数调用不同的生成逻辑
     // Documentation_Note (DW): 完善PPT生成的SVG模式支持
     // }}
     console.log(`🔄 handleGenerate 渲染次数:1`);
     if (!sourceContent.trim()) {
       MessageService.warning('请先输入或上传源内容');
       return;
     }
     
     // 特殊处理：PPT的SVG模式，直接调用SVG生成
     if (contentType === 'ppt' && config.ppt.use_svg) {
       MessageService.info('SVG模式已选择，请使用"生成SVG演示"按钮生成内容');
       return;
     }
     
     // 对于PPT模板生成，清除SVG缓存（因为要生成传统PPT）
     if (contentType === 'ppt' && config.ppt.use_template) {
       clearSvgCache();
       if (!outlineGenerated) {
         MessageService.info('建议先生成PPT大纲，编辑后再生成PPT以获得更好的效果');
       }
     }
     
     setLoading(true);
     try {
       const requestOptions = {
         research_data: sourceContent,
         content_type: contentType,
         options: {
           ...config[contentType],
           // 确保长文本策略传递给后端
           long_text_strategy: config[contentType].long_text_strategy || 'ai_compress',
           // 确保模板相关参数传递给后端
           ...(contentType === 'ppt' && config.ppt.use_template && {
             use_template: true,
             template_id: config.ppt.template_id
           }),
           // 如果是PPT且已生成大纲，传递用户编辑的大纲
           ...(contentType === 'ppt' && outlineGenerated && outlineContent.trim() && {
             user_outline: outlineContent.trim()
           })
         }
       };
       
       // {{CHENGQI:
       // Action: Added
       // Timestamp: [2025-01-18T01:05:00+08:00]
       // Reason: 为播客生成添加脚本内容支持，使用用户编辑的脚本生成音频
       // Principle_Applied: 功能完整性 - 支持播客脚本编辑流程; 用户体验 - 使用用户自定义脚本
       // Optimization: 优先使用用户编辑的脚本内容
       // Architectural_Note (AR): 完善播客两步生成流程
       // Documentation_Note (DW): 新增播客脚本内容传递逻辑
       // }}
       // 如果是播客且已生成脚本，使用脚本内容而不是源内容
       if (contentType === 'podcast' && podcastScriptGenerated && podcastScriptContent.trim()) {
         requestOptions.research_data = podcastScriptContent.trim();
         requestOptions.script_text = podcastScriptContent.trim();
       }
       
       // {{CHENGQI:
       // Action: Modified
       // Timestamp: [2025-01-19T15:40:00+08:00]
       // Reason: 为播客生成添加异步任务模式，参考深度研究的实现，解决生成时间长和重复点击问题
       // Principle_Applied: 一致性 - 与研究查询使用相同的异步模式; 用户体验 - 防止重复点击和长时间等待
       // Optimization: 播客生成使用异步任务，其他内容类型保持同步处理
       // Architectural_Note (AR): 统一异步任务处理模式，提升系统响应性
       // Documentation_Note (DW): 实现播客生成的异步任务功能
       // }}
       
       // 播客生成使用异步任务模式
       if (contentType === 'podcast') {
         // 清理之前的任务状态
         setPodcastTaskId(null);
         setPodcastTaskPolling(false);
         setPodcastProgress(0);
         if (podcastPollingRef.current) {
           clearInterval(podcastPollingRef.current);
           podcastPollingRef.current = null;
         }
         
         setPodcastGenerating(true);
         const startTime = Date.now();
         
         try {
           // 1. 创建播客生成任务，获取task_id
           const response = await callPluginAPI('/generate_content', {
             method: 'POST',
             body: JSON.stringify(requestOptions)
           });
           
           if (response.success && response.task_id) {
             setPodcastTaskId(response.task_id);
             setPodcastTaskPolling(true);
             MessageService.info('播客生成任务已启动，正在处理中...');
             
             // 2. 启动轮询检查任务状态
             podcastPollingRef.current = setInterval(async () => {
               try {
                 const res = await callPluginAPI('/task_result', {
                   method: 'POST',
                   body: JSON.stringify({ task_id: response.task_id })
                 });
                 
                 if (res.success && res.data) {
                   // 任务完成
                   const endTime = Date.now();
                   const duration = Math.round((endTime - startTime) / 1000);
                   
                   // 处理播客生成结果
                   const resultData = {
                     ...res,
                     contentType: contentType
                   };
                   setResult(resultData);
                   
                   // 清理任务状态
                   setPodcastGenerating(false);
                   setPodcastTaskPolling(false);
                   setPodcastProgress(100);
                   setPodcastTaskId(null);
                   if (podcastPollingRef.current) {
                     clearInterval(podcastPollingRef.current);
                     podcastPollingRef.current = null;
                   }
                   
                   MessageService.success(`播客生成完成！耗时 ${duration} 秒`);
                 } else if (res.success && res.status === 'pending') {
                   // 任务还在进行中，更新进度
                   setPodcastProgress(prev => Math.min(prev + 5, 95));
                 } else if (!res.success) {
                   // 任务失败
                   setPodcastGenerating(false);
                   setPodcastTaskPolling(false);
                   setPodcastTaskId(null);
                   if (podcastPollingRef.current) {
                     clearInterval(podcastPollingRef.current);
                     podcastPollingRef.current = null;
                   }
                   MessageService.error(res.error || '播客生成任务失败');
                 }
               } catch (err) {
                 console.error('查询播客任务结果失败:', err);
                 setPodcastGenerating(false);
                 setPodcastTaskPolling(false);
                 setPodcastTaskId(null);
                 if (podcastPollingRef.current) {
                   clearInterval(podcastPollingRef.current);
                   podcastPollingRef.current = null;
                 }
                 MessageService.error('查询播客任务结果失败: ' + err.message);
               }
             }, 5000); // 每5秒检查一次
             
           } else {
             setPodcastGenerating(false);
             MessageService.error(response.error || '播客生成任务创建失败');
           }
         } catch (err) {
           setPodcastGenerating(false);
           MessageService.error('播客生成任务创建出错: ' + err.message);
         }
       } else {
         // 其他内容类型保持原有的同步处理
         const response = await callPluginAPI('/generate_content', {
           method: 'POST',
           body: JSON.stringify(requestOptions)
         });
         
         if (response.success) {
           // 处理新的响应格式
           const resultData = {
             ...response,
             contentType: contentType  // 保留内容类型用于UI显示
           };
           setResult(resultData);
           MessageService.success('内容生成成功');
         } else {
           MessageService.error(response.error || '内容生成失败');
         }
       }
     } catch (error) {
       console.error('Content generation error:', error);
       MessageService.error('内容生成失败: ' + error.message);
       // 确保播客生成状态被重置
       if (contentType === 'podcast') {
         setPodcastGenerating(false);
         setPodcastTaskPolling(false);
         setPodcastTaskId(null);
         if (podcastPollingRef.current) {
           clearInterval(podcastPollingRef.current);
           podcastPollingRef.current = null;
         }
       }
     } finally {
       setLoading(false);
     }
   }, [sourceContent, contentType, config, callPluginAPI, outlineGenerated, outlineContent, podcastScriptGenerated, podcastScriptContent]);
   
   // {{CHENGQI:
   // Action: Modified
   // Timestamp: [2025-01-19T16:30:00+08:00]
   // Reason: 扩展组件清理逻辑，添加脚本生成任务轮询定时器清理
   // Principle_Applied: 资源管理 - 防止内存泄漏; 生命周期管理 - 正确清理副作用
   // Optimization: 清理所有异步任务相关的定时器
   // Architectural_Note (AR): 完善异步任务的生命周期管理
   // Documentation_Note (DW): 扩展任务轮询清理逻辑
   // }}
   // 组件卸载时清理所有任务轮询定时器
   useEffect(() => {
     return () => {
       if (podcastPollingRef.current) {
         clearInterval(podcastPollingRef.current);
         podcastPollingRef.current = null;
       }
       if (scriptPollingRef.current) {
         clearInterval(scriptPollingRef.current);
         scriptPollingRef.current = null;
       }
     };
   }, []);
   
   // {{CHENGQI:
   // Action: Added
   // Timestamp: [2025-01-19T16:35:00+08:00]
   // Reason: 添加播客脚本生成的异步任务处理函数
   // Principle_Applied: 异步任务 - 统一的任务管理模式; 用户体验 - 防止重复点击和提供进度反馈
   // Optimization: 使用轮询机制检查脚本生成任务状态
   // Architectural_Note (AR): 实现脚本生成的异步任务功能
   // Documentation_Note (DW): 新增脚本生成异步任务处理函数
   // }}
   // 生成播客脚本的异步任务处理函数
   const handleGeneratePodcastScript = useCallback(async () => {
     if (!sourceContent || sourceContent.trim().length === 0) {
       MessageService.error('请先提供源内容');
       return;
     }
     
     // 清理之前的任务状态
     setScriptTaskId(null);
     setScriptTaskPolling(false);
     setScriptProgress(0);
     if (scriptPollingRef.current) {
       clearInterval(scriptPollingRef.current);
       scriptPollingRef.current = null;
     }
     
     setScriptGenerating(true);
     const startTime = Date.now();
     
     try {
       // 1. 创建脚本生成任务，获取task_id
       const response = await callPluginAPI('/generate_podcast_script', {
         method: 'POST',
         body: JSON.stringify({
           research_data: sourceContent,
           options: {
             style: config.podcast.style,
             duration: config.podcast.duration,
             voice1: config.podcast.voice1,
             voice2: config.podcast.voice2
           }
         })
       });
       
       if (response.success && response.task_id) {
         setScriptTaskId(response.task_id);
         setScriptTaskPolling(true);
         MessageService.info('脚本生成任务已启动，正在处理中...');
         
         // 2. 启动轮询检查任务状态
         scriptPollingRef.current = setInterval(async () => {
           try {
             const res = await callPluginAPI('/task_result', {
               method: 'POST',
               body: JSON.stringify({ task_id: response.task_id })
             });
             
             if (res.success && res.script) {
               // {{CHENGQI:
               // Action: Modified
               // Timestamp: [2025-01-19T17:30:00+08:00]
               // Reason: 修复脚本生成任务结果处理逻辑，正确解析后端返回的任务结果格式
               // Principle_Applied: 数据一致性 - 正确处理后端任务结果; 错误修复 - 解决任务完成但前端无法识别问题
               // Optimization: 修正结果检查逻辑，确保脚本内容正确提取和显示
               // Architectural_Note (AR): 修复异步任务结果处理的关键逻辑
               // Documentation_Note (DW): 解决脚本生成完成后前端无法识别的问题
               // }}
               // 任务完成
               const endTime = Date.now();
               const duration = Math.round((endTime - startTime) / 1000);
               
               // 处理脚本生成结果
               setPodcastScriptContent(res.script_text || '');
               setPodcastScriptGenerated(true);
               
               // 清理任务状态
               setScriptGenerating(false);
               setScriptTaskPolling(false);
               setScriptProgress(100);
               setScriptTaskId(null);
               if (scriptPollingRef.current) {
                 clearInterval(scriptPollingRef.current);
                 scriptPollingRef.current = null;
               }
               
               MessageService.success(`脚本生成完成！耗时 ${duration} 秒，您可以编辑后再生成音频`);
             } else if (res.success && res.status === 'pending') {
               // 任务还在进行中，更新进度
               setScriptProgress(prev => Math.min(prev + 5, 95));
             } else if (!res.success) {
               // 任务失败
               setScriptGenerating(false);
               setScriptTaskPolling(false);
               setScriptTaskId(null);
               if (scriptPollingRef.current) {
                 clearInterval(scriptPollingRef.current);
                 scriptPollingRef.current = null;
               }
               MessageService.error(res.error || '脚本生成任务失败');
             }
           } catch (err) {
             console.error('查询脚本生成任务结果失败:', err);
             setScriptGenerating(false);
             setScriptTaskPolling(false);
             setScriptTaskId(null);
             if (scriptPollingRef.current) {
               clearInterval(scriptPollingRef.current);
               scriptPollingRef.current = null;
             }
             MessageService.error('查询脚本生成任务结果失败: ' + err.message);
           }
         }, 5000); // 每5秒检查一次
         
       } else {
         setScriptGenerating(false);
         MessageService.error(response.error || '脚本生成任务创建失败');
       }
     } catch (err) {
       setScriptGenerating(false);
       MessageService.error('脚本生成任务创建出错: ' + err.message);
     }
   }, [sourceContent, config.podcast, callPluginAPI]);
   
   /**
    * 渲染配置面板
    */
  const renderConfigPanel = () => {
    console.log(`🔄 renderConfigPanel 渲染次数:1`);
    const currentConfig = config[contentType];
    
    switch (contentType) {
      case 'podcast':
        return <PodcastConfigPanel />;
        
      case 'ppt':
        // {{CHENGQI:
        // Action: Modified
        // Timestamp: [2025-01-16T18:30:00+08:00]
        // Reason: 将SVG自定义需求输入框从PPTContent移至DeerFlowContent中，解决输入框跳动和焦点丢失问题
        // Principle_Applied: 状态管理 - 提升状态到稳定的父组件; 用户体验 - 修复输入框交互问题
        // Optimization: 避免组件重复创建导致的输入框焦点丢失
        // Architectural_Note (AR): 改进组件状态管理，确保输入框稳定性
        // Documentation_Note (DW): 解决用户反馈的输入框跳动问题
        // }}
        return (
          <div>
            <PPTConfigPanel />
            {/* SVG自定义需求输入框 - 移至此处以避免跳动问题 */}
            {currentConfig.use_svg && (
              <div style={{
                marginTop: '16px',
                padding: '16px',
                border: '1px solid #d9d9d9',
                borderRadius: '6px',
                backgroundColor: '#fafafa'
              }}>
                <label style={{ fontSize: '14px', fontWeight: 'bold', display: 'block', marginBottom: '8px' }}>
                  🎨 SVG自定义需求：
                </label>
                <TextArea
                  value={currentConfig.svg_custom_requirements || ''}
                  onChange={(e) => updateConfig('ppt', 'svg_custom_requirements', e.target.value)}
                  placeholder={`请输入您的具体需求，例如：
- 需要突出数据可视化效果
- 适用于学术会议演示
- 需要包含公司Logo位置
- 字体要求较大便于远距离观看`}
                  rows={4}
                  style={{ 
                    fontSize: '13px',
                    lineHeight: '1.5'
                  }}
                  maxLength={500}
                />
                <div style={{ 
                  fontSize: '12px', 
                  color: '#999', 
                  textAlign: 'right',
                  marginTop: '4px'
                }}>
                  {(currentConfig.svg_custom_requirements || '').length}/500
                </div>
              </div>
            )}
          </div>
        );
        
      case 'prose':
        return <ProseConfigPanel />;
        
      default:
        return null;
    }
  };
  
  return (
    <div className="deer-flow-content" style={{ 
      padding: '24px',
      paddingBottom: '80px', // 确保底部有足够空间
      backgroundColor: '#f5f5f5'
    }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <h1 style={{ 
          fontSize: '24px', 
          fontWeight: 'bold', 
          margin: 0,
          display: 'flex',
          alignItems: 'center'
        }}>
          <FileTextOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
          多媒体内容生成
        </h1>
        <p style={{ color: '#666', margin: '8px 0 0 0' }}>
          将研究报告转换为播客、PPT、散文等多种形式的内容
        </p>
      </div>
      
      {/* 内容类型选择 */}
      <Card title="选择生成类型" style={{ marginBottom: '16px' }}>
        <div style={{ marginBottom: '16px' }}>
          <Radio.Group 
            value={contentType} 
            onChange={(e) => setContentType(e.target.value)}
            size="large"
          >
            {contentTypes.map(type => (
              <Radio.Button key={type.value} value={type.value} style={{ height: 'auto', padding: '12px 16px' }}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '20px', marginBottom: '4px' }}>
                    {type.icon}
                  </div>
                  <div style={{ fontWeight: 'bold' }}>{type.label}</div>
                  <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                    {type.description}
                  </div>
                </div>
              </Radio.Button>
            ))}
          </Radio.Group>
        </div>
        
        {/* 配置面板 */}
        <Card size="small" title="生成配置">
          {renderConfigPanel()}
        </Card>
      </Card>
      
      {/* 源内容输入 - 统一显示所有类型的输入框 */}
      {true && (
        <Card 
          title="源内容" 
          style={{ marginBottom: '16px' }}
          bodyStyle={{ padding: '20px' }}
        >
        <Tabs defaultActiveKey="input">
          <TabPane tab="📝 文本输入" key="input">
            <div style={{ marginBottom: '12px' }}>
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center',
                marginBottom: '8px'
              }}>
                <span style={{ color: '#666', fontSize: '14px' }}>
                  💡 提示：支持Markdown格式，无字数限制，可以粘贴长篇研究报告
                </span>
                <span style={{ 
                  color: sourceContent.length > 10000 ? '#ff4d4f' : '#666', 
                  fontSize: '14px',
                  fontWeight: sourceContent.length > 10000 ? 'bold' : 'normal'
                }}>
                  字符数：{sourceContent.length.toLocaleString()}
                </span>
              </div>
              
              {/* 长文本提醒 */}
              {sourceContent.length > 10000 && (
                <div style={{
                  background: 'linear-gradient(135deg, #fff2e8 0%, #ffeee8 100%)',
                  border: '1px solid #ffad6a',
                  borderRadius: '6px',
                  padding: '12px 16px',
                  marginBottom: '12px',
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <div style={{ 
                    fontSize: '18px', 
                    marginRight: '8px',
                    color: '#ff7a00'
                  }}>
                    🤖
                  </div>
                  <div style={{ flex: 1 }}>
                    <div style={{ 
                      fontWeight: 'bold', 
                      color: '#d46b08',
                      marginBottom: '4px'
                    }}>
                      智能长文本处理
                    </div>
                    <div style={{ 
                      color: '#ad6800', 
                      fontSize: '13px',
                      lineHeight: '1.4'
                    }}>
                      检测到内容较长（{sourceContent.length.toLocaleString()} 字符），系统将自动使用AI智能压缩技术，
                      保留核心信息的同时确保生成质量。压缩后约为 8000-12000 字符。
                    </div>
                  </div>
                </div>
              )}
            </div>
            <TextArea
              placeholder="请输入要转换的内容，支持Markdown格式..."
              value={sourceContent}
              onChange={(e) => setSourceContent(e.target.value)}
              rows={20}
              showCount
              style={{
                fontSize: '14px',
                lineHeight: '1.6',
                resize: 'vertical',
                minHeight: '100px'
              }}
            />
          </TabPane>
          
          <TabPane tab="📂 文件上传" key="upload">
            <Upload.Dragger
              accept=".txt,.md,.docx"
              beforeUpload={() => false}
              onChange={handleFileUpload}
              multiple={false}
              style={{ padding: '40px 20px' }}
            >
              <p className="ant-upload-drag-icon">
                <UploadOutlined />
              </p>
              <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p className="ant-upload-hint">
                支持 .txt, .md, .docx 格式文件，无大小限制
              </p>
            </Upload.Dragger>
          </TabPane>
        </Tabs>
        
        {/* {{CHENGQI:
          Action: Modified
          Timestamp: [2025-06-22T17:25:00+08:00]
          Reason: 修改PPT编辑器的显示条件，支持SVG模式显示生成面板
          Principle_Applied: 用户体验 - 根据功能需求显示相应UI; 功能完整性 - 确保SVG模式能正常工作
          Optimization: 在模板模式或SVG模式下都显示PPTOutlineEditor，因为SVG生成面板也在其中
          Architectural_Note (AR): 修复SVG模式下生成面板不显示的问题
          Documentation_Note (DW): 解决用户反馈的SVG按钮不显示问题
        }} */}
        {contentType === 'ppt' && (config.ppt.use_template || config.ppt.use_svg) && (
          <div>
            {/* SVG生成进度显示 - 直接在DeerFlowContent中管理 */}
            {config.ppt.use_svg && generatingSvg && (
              <div style={{
                padding: '20px',
                textAlign: 'center',
                border: '1px solid #d9d9d9',
                borderRadius: '6px',
                backgroundColor: '#fff',
                marginBottom: '16px',
                marginTop: '16px'
              }}>
                <div style={{ marginBottom: '16px' }}>
                  <Progress 
                    percent={Math.round(svgProgress?.progress || 0)} 
                    status={svgProgress?.error ? "exception" : "active"}
                    strokeColor={svgProgress?.error ? {
                      '0%': '#ff4d4f',
                      '100%': '#ff7875',
                    } : {
                      '0%': '#108ee9',
                      '100%': '#87d068',
                    }}
                    showInfo={true}
                    format={(percent) => `${percent}%`}
                  />
                </div>
                <div style={{ 
                  fontSize: '14px', 
                  color: svgProgress?.error ? '#ff4d4f' : '#666',
                  marginBottom: '8px',
                  fontWeight: 'bold'
                }}>
                  {svgProgress?.stage && svgProgress?.message 
                    ? `${svgProgress.stage}: ${svgProgress.message}` 
                    : svgProgress?.message || svgProgress?.stage || '正在生成SVG内容...'}
                </div>
                {/* 调试信息 - 开发环境显示 */}
                {process.env.NODE_ENV === 'development' && (
                  <div style={{ 
                    fontSize: '11px', 
                    color: '#999',
                    marginTop: '4px',
                    fontFamily: 'monospace'
                  }}>
                    调试: progress={svgProgress?.progress} (显示: {Math.round(svgProgress?.progress || 0)}%), stage="{svgProgress?.stage}", error={!!svgProgress?.error}, svgContent.length={svgContent?.length || 0}
                  </div>
                )}
              </div>
            )}
            <PPTOutlineEditor />
          </div>
        )}

        {/* {{CHENGQI:
          Action: Added
          Timestamp: [2025-01-18T00:30:00+08:00]
          Reason: 添加播客脚本编辑器，支持播客的两步生成流程
          Principle_Applied: 用户体验 - 支持脚本编辑和自定义; 一致性 - 与PPT大纲编辑器保持一致的交互模式
          Optimization: 提供播客脚本的生成和编辑功能
          Architectural_Note (AR): 扩展内容生成的编辑功能
          Documentation_Note (DW): 新增播客脚本编辑器
        }} */}
        {contentType === 'podcast' && <PodcastScriptEditor />}



        <div style={{ marginTop: '20px', marginBottom: '40px', textAlign: 'center' }}>
          {/* {{CHENGQI:
            Action: Added
            Timestamp: [2025-01-19T15:50:00+08:00]
            Reason: 添加播客生成进度显示，为用户提供任务进度反馈
            Principle_Applied: 用户体验 - 提供清晰的进度反馈; 一致性 - 与其他异步任务进度保持一致
            Optimization: 在播客生成期间显示进度条和状态信息
            Architectural_Note (AR): 完善播客异步任务的用户界面
            Documentation_Note (DW): 新增播客生成进度显示
          }} */}
          {contentType === 'podcast' && podcastGenerating && (
            <div style={{ marginBottom: '20px' }}>
              <Progress
                percent={podcastProgress}
                status="active"
                showInfo={true}
                strokeColor="#1890ff"
                style={{ marginBottom: '8px' }}
              />
              <div style={{ fontSize: '14px', color: '#666' }}>
                🎙️ 正在生成播客音频，请稍候... ({podcastProgress}%)
              </div>
              {podcastTaskId && (
                <div style={{ fontSize: '12px', color: '#999', marginTop: '4px' }}>
                  任务ID: {podcastTaskId}
                </div>
              )}
            </div>
          )}
          
          <Button
            type="primary"
            size="large"
            loading={loading}
            onClick={handleGenerate}
            disabled={!sourceContent.trim() || 
              (contentType === 'ppt' && config.ppt.use_template && !outlineGenerated) ||
              (contentType === 'ppt' && config.ppt.use_svg) ||
              (contentType === 'podcast' && !podcastScriptGenerated) ||
              (contentType === 'podcast' && podcastGenerating)}
            icon={contentType === 'podcast' ? <SoundOutlined /> : 
                  contentType === 'ppt' ? <FileImageOutlined /> : <FileTextOutlined />}
            style={{ padding: '8px 32px', height: '48px', fontSize: '16px' }}
          >
            {/* {{CHENGQI:
              Action: Modified
              Timestamp: [2025-01-19T15:52:00+08:00]
              Reason: 修改按钮文本逻辑，支持播客生成期间的状态显示
              Principle_Applied: 用户体验 - 清晰的操作指导和状态反馈; 直观性 - 明确的按钮状态
              Optimization: 为播客生成期间提供状态反馈，防止重复点击
              Architectural_Note (AR): 完善播客异步生成的用户界面
              Documentation_Note (DW): 优化按钮交互逻辑，支持播客生成状态显示
            }} */}
            {contentType === 'ppt' && config.ppt.use_svg
              ? '请使用SVG生成按钮'
              : contentType === 'ppt' && config.ppt.use_template && !outlineGenerated 
              ? '请先生成PPT大纲' 
              : contentType === 'podcast' && !podcastScriptGenerated
              ? '请先生成播客脚本'
              : contentType === 'podcast' && podcastGenerating
              ? '正在生成播客音频...'
              : `生成 ${contentTypes.find(t => t.value === contentType)?.label}`
            }
          </Button>
          
          {contentType === 'ppt' && config.ppt.use_svg && (
            <div style={{ 
              marginTop: '12px',
              fontSize: '13px',
              color: '#666',
              maxWidth: '400px',
              marginLeft: 'auto',
              marginRight: 'auto'
            }}>
              🎨 SVG增强模式已选择，请在下方配置区域使用"生成SVG演示"按钮
              svg生成时，请不要关闭页面，否则生成会失败
            </div>
          )}
          
          {contentType === 'ppt' && config.ppt.use_template && !outlineGenerated && !config.ppt.use_svg && (
            <div style={{ 
              marginTop: '12px',
              fontSize: '13px',
              color: '#666',
              maxWidth: '400px',
              marginLeft: 'auto',
              marginRight: 'auto'
            }}>
              💡 专业模板模式需要先生成PPT大纲，编辑后再生成PPT以获得更好的效果
            </div>
          )}
          
          {/* {{CHENGQI:
            Action: Added
            Timestamp: [2025-01-18T01:02:00+08:00]
            Reason: 添加播客脚本生成提示信息
            Principle_Applied: 用户体验 - 提供清晰的操作指导; 一致性 - 与PPT提示保持一致
            Optimization: 为用户提供明确的操作指导
            Architectural_Note (AR): 完善播客两步生成的用户指导
            Documentation_Note (DW): 新增播客脚本生成提示
          }} */}
          {contentType === 'podcast' && !podcastScriptGenerated && (
            <div style={{ 
              marginTop: '12px',
              fontSize: '13px',
              color: '#666',
              maxWidth: '400px',
              marginLeft: 'auto',
              marginRight: 'auto'
            }}>
              💡 播客生成需要先生成脚本，编辑后再生成音频以获得更好的效果
            </div>
          )}
          
          {/* {{CHENGQI:
            Action: Added
            Timestamp: [2025-01-18T01:03:00+08:00]
            Reason: 添加播客脚本已生成的提示信息
            Principle_Applied: 用户体验 - 提供状态反馈; 一致性 - 与其他模式保持一致
            Optimization: 为用户提供明确的状态反馈
            Architectural_Note (AR): 完善播客两步生成的状态提示
            Documentation_Note (DW): 新增播客脚本状态提示
          }} */}
          {contentType === 'podcast' && podcastScriptGenerated && (
            <div style={{ 
              marginTop: '12px',
              fontSize: '13px',
              color: '#52c41a',
              maxWidth: '400px',
              marginLeft: 'auto',
              marginRight: 'auto'
            }}>
              ✅ 播客脚本已生成，现在可以生成音频
            </div>
          )}
          
          
        </div>
      </Card>
      )}
      
      {/* 生成结果 */}
      {result && (
        <Card title="生成结果" style={{ marginBottom: '60px' }}>
          {result.contentType === 'podcast' && <PodcastResultDisplay />}
          
          {/* {{CHENGQI:
            Action: Modified
            Timestamp: [2024-07-30 12:20:00+08:00]
            Reason: 将内联的PPT结果UI替换为从PPTContent组件中获取的专用结果显示组件
            Principle_Applied: 单一职责原则
          }} */}
          {result.contentType === 'ppt' && <PPTResultDisplay />}
          
          {result.contentType === 'prose' && <ProseResultDisplay />}
          

        </Card>
      )}
      
      {/* 功能说明 */}
      {!result && (
        <Card title="功能说明" style={{ marginTop: '10px', marginBottom: '60px' }}>
          <div style={{ color: '#666' }}>
            <h4>🎙️ 播客音频</h4>
            <p>• 支持多种播客风格：对话式、教育性、访谈式</p>
            <p>• 自动调节语速和停顿，生成自然流畅的音频</p>
            <p>• 可控制时长，适合不同场景需求</p>
            
            <h4>📊 PPT演示</h4>
            <p>• 智能分析内容结构，自动生成幻灯片</p>
            <p>• 多种专业主题模板可选</p>
            <p>• 自动配图和排版，节省制作时间</p>
            
            <h4>📝 散文文章</h4>
            <p>• 多种写作风格：学术性、通俗易懂、技术性、叙述性</p>
            <p>• 可控制文章长度和语调</p>
            <p>• 保持原有信息的同时提升可读性</p>
          </div>
        </Card>
      )}
      
      <SVGPreviewModal />
    </div>
  );

  
  };

export default DeerFlowContent; 