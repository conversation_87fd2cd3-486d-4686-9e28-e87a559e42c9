<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- CSS Styles for consistent design -->
    <style type="text/css"><![CDATA[
      /* Font Families */
      .font-primary { font-family: 'Inter', Helvetica, Arial, sans-serif; }
      .font-secondary { font-family: 'SF Pro Display', system-ui, sans-serif; }
      .font-accent { font-family: 'Poppins', sans-serif; }

      /* Text Colors */
      .text-primary-color { fill: #1E293B; }
      .text-secondary-color { fill: #64748B; }
      .text-light-color { fill: #94A3B8; }

      /* Specific Text Styles */
      .chapter-title-zh {
        font-size: 80px; /* Enhanced for visual impact, larger than hero_title */
        font-weight: 900; /* black */
        line-height: 1.1; /* tight */
        letter-spacing: 0em; /* normal */
        fill: #1E293B; /* text-primary */
        text-anchor: middle;
        dominant-baseline: central;
      }
      .chapter-title-en {
        font-size: 32px; /* content_title size */
        font-weight: 500; /* medium */
        line-height: 1.4; /* normal */
        fill: #64748B; /* text-secondary */
        text-anchor: middle;
        dominant-baseline: central;
      }
      .page-number {
        font-size: 24px; /* body_text size */
        font-weight: 400; /* normal */
        fill: #64748B; /* text-secondary */
        text-anchor: end;
        dominant-baseline: central;
      }
      .small-caption-text {
        font-size: 16px;
        font-weight: 400;
        fill: #64748B; /* text-secondary */
      }
      .logo-text {
        font-size: 22px; /* body_text size */
        font-weight: 600; /* semibold */
        fill: #3B82F6; /* primary color */
      }

      /* Gradients for decorative elements */
      <linearGradient id="backgroundGradient" x1="0.5" y1="0" x2="0.5" y2="1">
        <stop offset="0%" stop-color="#F8FAFC" />
        <stop offset="100%" stop-color="#E0F2FE" />
      </linearGradient>

      <linearGradient id="transparentAccentGradient" x1="0" y1="0" x2="1" y2="0">
        <stop offset="0%" stop-color="#BAE6FD" stop-opacity="0" />
        <stop offset="50%" stop-color="#BAE6FD" stop-opacity="0.8" />
        <stop offset="100%" stop-color="#BAE6FD" stop-opacity="0" />
      </linearGradient>

      <!-- Primary Gradient for subtle accents -->
      <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="1">
        <stop offset="0%" stop-color="#3B82F6" />
        <stop offset="100%" stop-color="#7DD3FC" />
      </linearGradient>

    ]]></style>

  </defs>

  <!-- Background Layer -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)" />

  <!-- Decorative Geometric Shapes (subtle and transparent) -->
  <!-- Left large wave-like shape -->
  <path d="M0 0 L 0 1080 L 350 1080 C 180 800 180 200 350 0 L 0 0 Z" fill="#BAE6FD" opacity="0.15" />
  <!-- Left smaller wave-like shape -->
  <path d="M0 0 L 0 1080 L 280 1080 C 150 750 150 250 280 0 L 0 0 Z" fill="#BAE6FD" opacity="0.08" />

  <!-- Right large wave-like shape -->
  <path d="M1920 0 L 1920 1080 L 1570 1080 C 1740 800 1740 200 1570 0 L 1920 0 Z" fill="#BAE6FD" opacity="0.15" />
  <!-- Right smaller wave-like shape -->
  <path d="M1920 0 L 1920 1080 L 1640 1080 C 1770 750 1770 250 1640 0 L 1920 0 Z" fill="#BAE6FD" opacity="0.08" />

  <!-- Top-left subtle rectangle -->
  <rect x="80" y="60" width="120" height="120" fill="#7DD3FC" opacity="0.07" />
  <!-- Bottom-right subtle circle -->
  <circle cx="1840" cy="1020" r="70" fill="#7DD3FC" opacity="0.07" />

  <!-- Central horizontal line divider with gradient -->
  <rect x="160" y="539" width="1600" height="2" fill="url(#transparentAccentGradient)" />

  <!-- Main Content Group (Chapter Title and Subtitle) -->
  <g class="font-primary" transform="translate(960, 540)">
    <!-- Chapter Title (Chinese) - Super large, bold, and primary text color -->
    <text x="0" y="-70" class="chapter-title-zh">
      <tspan x="0" y="-70">项目总结汇报</tspan>
    </text>

    <!-- Chapter Subtitle (English) - Smaller, secondary text color, as embellishment -->
    <text x="0" y="20" class="chapter-title-en">
      <tspan x="0" y="20">Project Summary Report</tspan>
    </text>
  </g>

  <!-- Page Number -->
  <text x="1840" y="1020" class="font-primary page-number">
    <tspan x="1840" y="1020">3/10</tspan>
  </text>

  <!-- Placeholder for Logo (top left) -->
  <!-- A simple rounded rectangle with text as a placeholder for a logo -->
  <rect x="80" y="60" width="180" height="60" fill="#BAE6FD" opacity="0.3" rx="8" ry="8"/>
  <text x="170" y="95" class="font-primary logo-text" text-anchor="middle" dominant-baseline="central">
      <tspan x="170" y="95">LOGO</tspan>
  </text>

  <!-- Placeholder for Date and Author (bottom left) -->
  <g class="font-primary small-caption-text">
    <text x="80" y="1020">
      <tspan x="80" y="1020">日期: 2023年10月27日</tspan>
    </text>
    <text x="80" y="1055"> <!-- Adjusted y for sufficient line spacing (35px gap) -->
      <tspan x="80" y="1055">作者: {author}</tspan>
    </text>
  </g>

</svg>