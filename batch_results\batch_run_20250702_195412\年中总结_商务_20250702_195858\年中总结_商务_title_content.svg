<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette -->
    <style type="text/css">
      /* Background */
      .background { fill: #F8FAFC; }
      .container-background { fill: #E0F2FE; }

      /* Primary Colors */
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }

      /* Text Colors */
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }

      /* Card Style */
      .card-background { fill: #FFFFFF; }
      .card-border { stroke: #BAE6FD; stroke-width: 1; }
      .card-shadow { filter: url(#drop-shadow); }

      /* Font Styles */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* Specific Text Classes */
      .main-title {
        font-size: 36px; /* section_title for page title */
        font-weight: 700; /* bold */
        fill: #1E293B; /* text_primary */
        line-height: 1.4; /* normal */
      }
      .subtitle-text {
        font-size: 22px; /* body_text */
        font-weight: 400; /* normal */
        fill: #64748B; /* text_secondary */
        line-height: 1.4;
      }
      .content-text {
        font-size: 22px; /* body_text */
        font-weight: 400; /* normal */
        fill: #1E293B; /* text_primary */
        line-height: 1.6; /* relaxed */
      }
      .list-item-text {
        font-size: 22px; /* body_text */
        font-weight: 400; /* normal */
        fill: #1E293B; /* text_primary */
        line-height: 1.6;
      }
      .highlight-number {
        font-size: 96px; /* Larger than hero_title 72px, custom for emphasis */
        font-weight: 900; /* black */
        fill: url(#accentGradient); /* Using gradient for highlight */
        letter-spacing: -0.025em; /* tight */
      }
      .highlight-label {
        font-size: 28px; /* content_title */
        font-weight: 700; /* bold */
        fill: #1E293B; /* text_primary */
      }
      .caption-text {
        font-size: 16px; /* small_text */
        font-weight: 400;
        fill: #94A3B8; /* text_light */
      }
      .page-indicator {
        font-size: 16px;
        font-weight: 400;
        fill: #64748B;
      }
      .bullet-icon {
        fill: #3B82F6; /* accent_color */
      }
      .icon-stroke {
        stroke: #4A86E8; /* from icon_system color */
        stroke-width: 2;
        fill: none;
      }
      .chart-placeholder-text {
        font-size: 28px;
        font-weight: 600;
        fill: #475569;
      }
      .module-title {
        font-size: 28px; /* content_title */
        font-weight: 700; /* bold */
        fill: #1E293B; /* text_primary */
      }

    </style>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E40AF" />
      <stop offset="100%" style="stop-color:#475569" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6" />
      <stop offset="100%" style="stop-color:#1E40AF" />
    </linearGradient>
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#F8FAFC" />
      <stop offset="100%" style="stop-color:#E0F2FE" />
    </linearGradient>

    <!-- Filter for Card Shadow -->
    <filter id="drop-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="4"/>
      <feGaussianBlur stdDeviation="3"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="background" />
  <rect x="0" y="880" width="1920" height="200" fill="url(#backgroundGradient)" /> <!-- Subtle bottom gradient -->

  <!-- Header Section -->
  <g id="header-area">
    <!-- Logo Placeholder -->
    <rect x="80" y="60" width="150" height="40" fill="#E0F2FE" rx="8" ry="8"/>
    <text x="155" y="87" text-anchor="middle" class="caption-text font-primary" fill="#1E40AF">
      <tspan x="155" y="87">{logo_url}</tspan>
    </text>

    <!-- Page Indicator -->
    <text x="1840" y="87" text-anchor="end" class="page-indicator font-primary">
      <tspan x="1840" y="87">4/10</tspan>
    </text>
  </g>

  <!-- Main Content Area -->
  <g id="main-content-area">
    <!-- Title -->
    <text x="160" y="190" class="main-title font-primary">
      <tspan x="160" y="190">{title}</tspan>
    </text>

    <!-- Subtitle -->
    <text x="160" y="235" class="subtitle-text font-primary">
      <tspan x="160" y="235">{subtitle}</tspan>
    </text>

    <!-- Content Modules (Bento Grid inspired simple layout) -->
    <g id="content-modules">
      <!-- Main Content Card -->
      <rect x="160" y="320" width="780" height="640" rx="12" ry="12" class="card-background card-border card-shadow" />

      <!-- Content Title inside card -->
      <text x="190" y="370" class="module-title font-primary">
        <tspan x="190" y="370">核心成果回顾</tspan>
      </text>

      <!-- Paragraph Content -->
      <text x="190" y="420" class="content-text font-primary">
        <tspan x="190" y="420">{content}</tspan>
        <tspan x="190" y="455">本部分将详细阐述我们在过去半年的关键成就，</tspan>
        <tspan x="190" y="490">包括市场拓展、产品优化和团队协作等多方面。</tspan>
        <tspan x="190" y="525">通过数据分析和具体案例，展示项目进展和</tspan>
        <tspan x="190" y="560">我们克服的挑战，确保信息传达的准确和专业。</tspan>
      </text>

      <!-- Bullet Point List -->
      <g id="bullet-points">
        <text x="190" y="630" class="module-title font-primary">
          <tspan x="190" y="630">主要里程碑</tspan>
        </text>

        <!-- List Item 1 -->
        <circle cx="205" cy="685" r="6" class="bullet-icon"/>
        <text x="230" y="690" class="list-item-text font-primary">
          <tspan x="230" y="690">市场份额增长15%和用户活跃度提升</tspan>
        </text>

        <!-- List Item 2 -->
        <circle cx="205" cy="730" r="6" class="bullet-icon"/>
        <text x="230" y="735" class="list-item-text font-primary">
          <tspan x="230" y="735">新产品成功发布，实现预期销售目标</tspan>
        </text>

        <!-- List Item 3 -->
        <circle cx="205" cy="775" r="6" class="bullet-icon"/>
        <text x="230" y="780" class="list-item-text font-primary">
          <tspan x="230" y="780">优化运营流程，效率提升达20%</tspan>
        </text>

        <!-- List Item 4 -->
        <circle cx="205" cy="820" r="6" class="bullet-icon"/>
        <text x="230" y="825" class="list-item-text font-primary">
          <tspan x="230" y="825">团队协作加强，项目按时交付和高质量完成</tspan>
        </text>
      </g>

      <!-- Right Side - Highlight Number & Data Visualization -->
      <g id="right-side-modules">
        <!-- Highlight Number Card -->
        <rect x="980" y="320" width="780" height="280" rx="12" ry="12" class="card-background card-border card-shadow" />
        <text x="1370" y="450" text-anchor="middle" class="highlight-number font-accent">
          <tspan x="1370" y="450">95%</tspan>
        </text>
        <text x="1370" y="500" text-anchor="middle" class="highlight-label font-primary">
          <tspan x="1370" y="500">项目完成率</tspan>
        </text>
        <text x="1370" y="530" text-anchor="middle" class="caption-text font-primary">
          <tspan x="1370" y="530">Project Completion Rate</tspan>
        </text>

        <!-- Data Visualization Placeholder Card -->
        <rect x="980" y="640" width="780" height="320" rx="12" ry="12" class="card-background card-border card-shadow" />
        <text x="1370" y="750" text-anchor="middle" class="chart-placeholder-text font-primary">
          <tspan x="1370" y="750">数据图表占位</tspan>
        </text>
        <text x="1370" y="790" text-anchor="middle" class="caption-text font-primary">
          <tspan x="1370" y="790">Placeholder for Data Chart</tspan>
        </text>

        <!-- Simple Outline Chart Icon -->
        <g class="icon-stroke" transform="translate(1320, 820)">
          <rect x="0" y="30" width="20" height="40" rx="2" ry="2"/>
          <rect x="30" y="0" width="20" height="70" rx="2" ry="2"/>
          <rect x="60" y="45" width="20" height="25" rx="2" ry="2"/>
        </g>

      </g>
    </g>
  </g>

  <!-- Decorative Elements / Footer -->
  <g id="decorative-elements">
    <!-- Accent line at the bottom of the main title -->
    <rect x="160" y="270" width="100" height="4" rx="2" ry="2" class="accent-color" />

    <!-- Subtle geometric shape (bottom right) -->
    <rect x="1600" y="980" width="100" height="100" fill="#E0F2FE" rx="20" ry="20" opacity="0.5"/>
    <circle cx="1700" cy="980" r="50" fill="#E0F2FE" opacity="0.3"/>
    <polygon points="1800,1080 1920,1080 1920,960" fill="#E0F2FE" opacity="0.2"/>

    <!-- Footer Info -->
    <text x="80" y="1020" class="caption-text font-primary">
      <tspan x="80" y="1020">{date}</tspan>
    </text>
    <text x="1840" y="1020" text-anchor="end" class="caption-text font-primary">
      <tspan x="1840" y="1020">{author}</tspan>
    </text>
  </g>

</svg>