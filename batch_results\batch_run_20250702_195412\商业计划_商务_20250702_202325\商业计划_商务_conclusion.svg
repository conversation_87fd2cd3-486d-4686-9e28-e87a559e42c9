<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <style type="text/css"><![CDATA[
      /* Color Palette */
      .bg-color { fill: #F8FAFC; }
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .text-primary-fill { fill: #1E293B; }
      .text-secondary-fill { fill: #64748B; }
      .text-light-fill { fill: #94A3B8; }
      .card-background-fill { fill: #FFFFFF; }
      .card-border-stroke { stroke: #BAE6FD; stroke-width: 1px; }
      .container-background-fill { fill: #E0F2FE; }

      /* Font System */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source <PERSON> Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      .hero-title { font-size: 72px; font-weight: 700; } /* bold */
      .main-title { font-size: 56px; font-weight: 700; } /* bold */
      .section-title { font-size: 36px; font-weight: 600; } /* semibold */
      .content-title { font-size: 28px; font-weight: 600; } /* semibold */
      .body-text { font-size: 22px; font-weight: 400; } /* normal */
      .small-text { font-size: 16px; font-weight: 400; } /* normal */
      .caption-text { font-size: 14px; font-weight: 400; } /* normal */

      /* Shadows (for cards) */
      .card-shadow { filter: url(#drop-shadow); }

      /* Decorative elements */
      .outline-stroke { stroke: #3B82F6; stroke-width: 2; fill: none; }
      .divider-line { stroke: #BAE6FD; stroke-width: 1; }

      /* Specific element styles */
      .contact-icon { fill: #3B82F6; }

    ]]></style>

    <!-- Drop Shadow Filter -->
    <filter id="drop-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="4"/>
      <feGaussianBlur stdDeviation="3"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Linear Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF"/>
      <stop offset="100%" stop-color="#475569"/>
    </linearGradient>

    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- Icon for checkmark -->
    <symbol id="icon-check" viewBox="0 0 24 24">
      <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" class="contact-icon"/>
    </symbol>

    <!-- Icon for email -->
    <symbol id="icon-email" viewBox="0 0 24 24">
      <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z" class="contact-icon"/>
    </symbol>

    <!-- Icon for phone -->
    <symbol id="icon-phone" viewBox="0 0 24 24">
      <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.32.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.12.35.03.75-.25 1.02l-2.2 2.2z" class="contact-icon"/>
    </symbol>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color"/>

  <!-- Page Header -->
  <text x="1840" y="60" text-anchor="end" class="caption-text text-secondary-fill font-primary">10/10</text>

  <!-- Logo Placeholder -->
  <image x="80" y="40" width="120" height="60" xlink:href="{logo_url}" />

  <!-- Main Title Area -->
  <g class="font-primary text-primary-fill">
    <text x="960" y="200" text-anchor="middle" class="hero-title">
      <tspan x="960" dy="0">{title}</tspan>
    </text>
    <text x="960" y="270" text-anchor="middle" class="main-title">
      <tspan x="960" dy="0">{subtitle}</tspan>
    </text>
  </g>

  <!-- Main Content Areas (Bento Grid Style) -->
  <!-- Key Conclusions Card -->
  <rect x="80" y="380" width="860" height="400" rx="12" ry="12" class="card-background-fill card-border-stroke card-shadow"/>
  <text x="110" y="430" class="section-title text-primary-fill font-primary">主要结论</text>
  <rect x="110" y="455" width="60" height="3" fill="url(#accentGradient)"/>
  <g class="body-text text-secondary-fill font-primary">
    <text x="110" y="500">
      <tspan x="110" dy="0">1. 市场潜力巨大，创新驱动增长。</tspan>
      <tspan x="110" dy="35">2. 财务模型稳健，回报预期乐观。</tspan>
      <tspan x="110" dy="35">3. 核心团队经验丰富，执行力强。</tspan>
      <tspan x="110" dy="35">4. 风险评估充分，应对策略完善。</tspan>
      <tspan x="110" dy="35">5. 竞争优势明显，持续创新是关键。</tspan>
    </text>
  </g>

  <!-- Action Points Card -->
  <rect x="980" y="380" width="860" height="400" rx="12" ry="12" class="card-background-fill card-border-stroke card-shadow"/>
  <text x="1010" y="430" class="section-title text-primary-fill font-primary">行动要点</text>
  <rect x="1010" y="455" width="60" height="3" fill="url(#accentGradient)"/>
  <g class="body-text text-primary-fill font-primary">
    <text x="1010" y="500">
      <tspan x="1010" dy="0">
        <use xlink:href="#icon-check" x="1010" y="475" width="24" height="24" />
        <tspan x="1050" dy="0">加速产品研发，扩大市场份额。</tspan>
      </tspan>
      <tspan x="1010" dy="35">
        <use xlink:href="#icon-check" x="1010" y="510" width="24" height="24" />
        <tspan x="1050" dy="0">优化运营效率，提升盈利能力。</tspan>
      </tspan>
      <tspan x="1010" dy="35">
        <use xlink:href="#icon-check" x="1010" y="545" width="24" height="24" />
        <tspan x="1050" dy="0">深化伙伴合作，构建生态系统。</tspan>
      </tspan>
      <tspan x="1010" dy="35">
        <use xlink:href="#icon-check" x="1010" y="580" width="24" height="24" />
        <tspan x="1050" dy="0">加强人才培养，确保团队活力。</tspan>
      </tspan>
      <tspan x="1010" dy="35">
        <use xlink:href="#icon-check" x="1010" y="615" width="24" height="24" />
        <tspan x="1050" dy="0">定期复盘评估，灵活调整战略。</tspan>
      </tspan>
    </text>
  </g>
  
  <!-- Decorative element for emphasis -->
  <rect x="990" y="390" width="10" height="380" rx="5" ry="5" fill="#3B82F6" opacity="0.15"/>


  <!-- Contact and Call to Action Area -->
  <rect x="80" y="820" width="1760" height="200" rx="12" ry="12" class="card-background-fill card-border-stroke card-shadow"/>
  <g class="font-primary text-primary-fill">
    <text x="960" y="870" text-anchor="middle" class="section-title">期待与您携手共创未来！</text>
  </g>

  <g class="body-text text-secondary-fill font-primary">
    <text x="960" y="920" text-anchor="middle">
      <tspan x="960" dy="0">
        <use xlink:href="#icon-email" x="800" y="895" width="24" height="24" />
        <tspan x="830" dy="0">邮箱: contact和#38;example.com</tspan>
      </tspan>
      <tspan x="960" dy="0">
        <use xlink:href="#icon-phone" x="1070" y="895" width="24" height="24" />
        <tspan x="1100" dy="0">电话: +86 123 4567 8901</tspan>
      </tspan>
    </text>
    <text x="960" y="960" text-anchor="middle" class="small-text text-light-fill">
      <tspan x="960" dy="0">版权所有 © {date} {author}。保留所有权利。</tspan>
    </text>
  </g>

  <!-- Decorative bottom line -->
  <rect x="80" y="1030" width="1760" height="5" fill="url(#primaryGradient)"/>

</svg>