<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Gradients based on the provided color scheme -->
    <linearGradient id="backgroundGradient" x1="960" y1="0" x2="960" y2="1080" gradientUnits="userSpaceOnUse">
      <stop stop-color="#F8FAFC"/>
      <stop offset="1" stop-color="#E0F2FE"/>
    </linearGradient>

    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="0" gradientTransform="rotate(135)" gradientUnits="objectBoundingBox">
      <stop stop-color="#4A86E8"/>
      <stop offset="1" stop-color="#3B82F6"/>
    </linearGradient>

    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="0" gradientTransform="rotate(45)" gradientUnits="objectBoundingBox">
      <stop stop-color="#0EA5E9"/>
      <stop offset="1" stop-color="#4A86E8"/>
    </linearGradient>

    <!-- Define a filter for subtle shadow for cards -->
    <filter id="cardShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="8"/>
      <feOffset dx="0" dy="4"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Filter for hover shadow, not directly used in static SVG but for conceptual completeness -->
    <filter id="hoverShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="15"/>
      <feOffset dx="0" dy="10"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

  </defs>

  <style>
    /* Font Definitions */
    .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
    .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
    .font-accent { font-family: 'Times New Roman', serif; }

    /* Text Colors */
    .text-primary-color { fill: #1E293B; }
    .text-secondary-color { fill: #64748B; }
    .text-light-color { fill: #94A3B8; }
    .text-accent-color { fill: #0EA5E9; } /* For quote symbols */
    .text-highlight-color { fill: #E31937; } /* Tesla Red for strong emphasis */

    /* General Colors */
    .primary-color-fill { fill: #4A86E8; }
    .secondary-color-fill { fill: #3B82F6; }
    .accent-color-fill { fill: #0EA5E9; }
    .background-color-fill { fill: #F8FAFC; }
    .card-background-fill { fill: #FFFFFF; }
    .card-border-stroke { stroke: #BAE6FD; }

    /* Font Sizes and Weights (from design system) */
    .hero-title { font-size: 72px; font-weight: 700; }
    .main-title { font-size: 56px; font-weight: 700; }
    .section-title { font-size: 36px; font-weight: 700; }
    .content-title { font-size: 28px; font-weight: 700; }
    .body-text { font-size: 22px; font-weight: 400; }
    .small-text { font-size: 16px; font-weight: 400; }
    .caption-text { font-size: 14px; font-weight: 400; }

    /* Quote specific styles */
    .quote-content-style {
      font-size: 48px; /* Prominent size for quote */
      font-weight: 600; /* Semibold for readability */
      text-anchor: middle;
      line-height: 1.4; /* Controlled by dy in tspan */
    }
    .quote-author-style {
      font-size: 26px; /* Larger than body-text for prominence */
      font-weight: 500;
      text-anchor: middle;
    }
    .quote-symbol {
      font-size: 200px; /* Very large for decorative impact */
      font-weight: 900; /* Black weight for boldness */
      font-family: 'Times New Roman', serif; /* Accent font for decorative symbols */
      text-anchor: middle;
    }

    /* Card styles with shadow */
    .card-base-style {
      filter: url(#cardShadow);
      stroke: #BAE6FD;
      stroke-width: 1;
      rx: 12;
      ry: 12;
    }
    .dark-card-style {
      filter: url(#cardShadow);
      rx: 12;
      ry: 12;
    }

    /* Icon style */
    .icon-style {
      stroke: #4A86E8;
      stroke-width: 2;
      fill: none;
      stroke-linecap: round;
      stroke-linejoin: round;
    }

    /* Ensure no content overlap by explicit positioning */
    /* All x, y, dx, dy values must be concrete numbers */
  </style>

  <!-- Background Layer with subtle gradient -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

  <!-- Decorative geometric elements (trendy feel, transparency gradients) -->
  <circle cx="1700" cy="150" r="80" fill="url(#accentGradient)" opacity="0.3"/>
  <rect x="1650" y="800" width="150" height="150" rx="20" ry="20" fill="url(#primaryGradient)" opacity="0.2" transform="rotate(25 1650 800)"/>
  <path d="M100 900 L300 800 L400 1000 L100 900 Z" fill="#4A86E8" opacity="0.1"/>
  <path d="M1800 100 L1600 200 L1500 0 L1800 100 Z" fill="#0EA5E9" opacity="0.15"/>


  <!-- Main Content Area - Centered Quote Block -->
  <g transform="translate(960, 540)"> <!-- Center point of the canvas -->

    <!-- Quote Block - Main Focus -->
    <g class="quote-block">
      <!-- Large opening quote symbol -->
      <text class="quote-symbol text-accent-color font-accent" x="0" y="-200">“</text>

      <!-- Quote Content (Placeholder) -->
      <!-- Using multiple tspan for line breaks and precise dy control -->
      <text class="quote-content-style text-primary-color font-primary" x="0" y="-20">
        <tspan x="0" dy="0">我们致力于为您提供卓越的产品</tspan>
        <tspan x="0" dy="60">和无与伦比的用户体验</tspan>
        <tspan x="0" dy="60">赋能您的成功之路</tspan>
      </text>

      <!-- Author / Source (Placeholder) -->
      <text class="quote-author-style text-secondary-color font-primary" x="0" y="200">
        — 创始人 王明
      </text>

      <!-- Large closing quote symbol -->
      <text class="quote-symbol text-accent-color font-accent" x="0" y="300">”</text>
    </g>

  </g>

  <!-- Product Advantage / Value Proposition Section (Bento-Grid like card) -->
  <g class="feature-section" transform="translate(100, 100)">
    <rect x="0" y="0" width="400" height="250" class="card-background-fill card-base-style"/>
    <text x="200" y="60" class="content-title text-primary-color font-primary" text-anchor="middle">核心优势</text>
    <text x="200" y="100" class="body-text text-secondary-color font-secondary" text-anchor="middle">
      <tspan x="200" dy="0">Core Advantages</tspan>
      <tspan x="200" dy="30">领先技术和创新设计</tspan>
    </text>
    <!-- Simple outline icon (check mark for advantage) -->
    <circle cx="200" cy="180" r="30" class="icon-style"/>
    <path d="M185 180 L200 195 L215 165" class="icon-style"/>
  </g>

  <!-- Another Bento-Grid like element: A strong call-out with Tesla Red -->
  <g class="highlight-section" transform="translate(1420, 750)">
    <rect x="0" y="0" width="400" height="250" fill="#1E293B" class="dark-card-style"/> <!-- Dark background -->
    <text x="200" y="100" class="section-title text-highlight-color font-primary" text-anchor="middle">
      <tspan x="200" dy="0">突破性创新</tspan>
      <tspan x="200" dy="40" class="body-text text-highlight-color font-secondary">Revolutionary Innovation</tspan>
    </text>
    <text x="200" y="180" class="small-text text-light-color font-secondary" text-anchor="middle">
      <tspan x="200" dy="0">探索未来科技，引领行业变革</tspan>
      <tspan x="200" dy="25">Explore future tech, lead industry change</tspan>
    </text>
  </g>

  <!-- Company Logo (Placeholder) -->
  <g class="logo-area" transform="translate(80, 80)">
    <!-- Using text placeholder for logo -->
    <text x="0" y="30" class="content-title text-primary-color font-primary">品牌标志</text>
    <text x="0" y="60" class="small-text text-secondary-color font-secondary">Brand Logo</text>
    <!-- If an image logo is provided, it would be like: -->
    <!-- <image href="{logo_url}" x="0" y="0" width="150" height="auto"/> -->
  </g>

  <!-- Date / Page Info -->
  <g class="page-info" transform="translate(1700, 1000)">
    <text x="0" y="0" class="small-text text-secondary-color font-secondary" text-anchor="end">
      2023年10月27日 | 页面 9/10
    </text>
  </g>
</svg>