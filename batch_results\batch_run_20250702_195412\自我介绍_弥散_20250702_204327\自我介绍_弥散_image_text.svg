<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- 背景渐变: 弥散风格的柔和背景 -->
    <linearGradient id="backgroundGradient" x1="960" y1="0" x2="960" y2="1080" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#F8FAFC"/>
      <stop offset="1" stop-color="#E0F2FE"/>
    </linearGradient>

    <!-- 主强调色渐变: 用于装饰元素，制造科技感和流动性 -->
    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#06B6D4" stop-opacity="0.6"/>
      <stop offset="1" stop-color="#3B82F6" stop-opacity="0.4"/>
    </linearGradient>

    <!-- 柔和阴影滤镜 -->
    <filter id="softShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="8"/>
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="15"/>
      <feBlend in="SourceGraphic" in2="blurOut" mode="normal"/>
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.2"/>
      </feComponentTransfer>
    </filter>

    <!-- 卡片阴影滤镜 -->
    <filter id="cardShadow" x="-10%" y="-10%" width="120%" height="120%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4"/>
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="6"/>
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.1"/>
      </feComponentTransfer>
      <feBlend in="SourceGraphic" in2="blurOut" mode="normal"/>
    </filter>
  </defs>

  <style>
    /* 字体定义 */
    @font-face {
      font-family: 'Microsoft YaHei';
      src: local('Microsoft YaHei'), local('MSYH');
    }
    @font-face {
      font-family: 'Segoe UI';
      src: local('Segoe UI'), local('SegoeUI');
    }
    @font-face {
      font-family: 'Source Han Sans CN';
      src: local('Source Han Sans CN'), local('Noto Sans CJK SC');
    }

    /* 通用样式 */
    .background-fill {
      fill: url(#backgroundGradient);
    }

    .text-primary {
      font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
      fill: #1E293B;
    }
    .text-secondary {
      font-family: 'Source Han Sans CN', 'Segoe UI', sans-serif;
      fill: #64748B;
    }
    .text-accent {
      font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
      fill: #06B6D4;
    }
    .text-bold {
      font-weight: 700;
    }

    /* 字体大小 */
    .font-hero-title { font-size: 72px; }
    .font-main-title { font-size: 56px; }
    .font-section-title { font-size: 36px; }
    .font-content-title { font-size: 28px; }
    .font-body-text { font-size: 22px; }
    .font-small-text { font-size: 16px; }
    .font-caption { font-size: 14px; }

    /* 卡片样式 */
    .card-background {
      fill: #FFFFFF;
      stroke: #BAE6FD;
      stroke-width: 1px;
      filter: url(#cardShadow);
      border-radius: 12px; /* SVG rect does not support border-radius property, handled by rx/ry */
    }

    /* 装饰元素样式 */
    .accent-shape {
      fill: url(#accentGradient);
      opacity: 0.7;
    }
  </style>

  <!-- 背景层 -->
  <rect x="0" y="0" width="1920" height="1080" class="background-fill"/>

  <!-- 装饰性弥散图形 -->
  <circle cx="1700" cy="150" r="100" class="accent-shape" filter="url(#softShadow)"/>
  <circle cx="200" cy="900" r="80" class="accent-shape" filter="url(#softShadow)"/>
  <ellipse cx="1400" cy="950" rx="150" ry="80" class="accent-shape" transform="rotate(-15 1400 950)" filter="url(#softShadow)"/>
  <rect x="50" y="50" width="100" height="100" rx="20" ry="20" class="accent-shape" transform="rotate(30 100 100)" filter="url(#softShadow)"/>

  <!-- 主内容区域 - 左右分栏布局，居中对齐 -->
  <g transform="translate(80 60)">
    <!-- Logo 占位符 -->
    <image x="0" y="0" width="160" height="40" xlink:href="{logo_url}" />

    <!-- 页面标题 -->
    <text x="800" y="40" text-anchor="middle" class="text-primary font-main-title text-bold">
      <tspan>自我介绍</tspan>
    </text>
    <text x="800" y="100" text-anchor="middle" class="text-secondary font-section-title">
      <tspan>My Personal Profile</tspan>
    </text>

    <!-- 内容布局: 图片 (左侧) 和 文字 (右侧) -->
    <!-- 图片栏 (约占40%宽度) -->
    <g transform="translate(0, 180)">
      <!-- 图片框架 -->
      <rect x="0" y="0" width="800" height="600" rx="12" ry="12" class="card-background"/>
      <!-- 图片占位符 -->
      <image x="10" y="10" width="780" height="580" href="{image_url}" preserveAspectRatio="xMidYMid slice" style="border-radius: 8px;"/>
      <!-- 图片描述或作者信息 -->
      <text x="400" y="630" text-anchor="middle" class="text-secondary font-small-text">
        <tspan>{author} 和#8212; {date}</tspan>
      </text>
    </g>

    <!-- 文字栏 (约占50%宽度) -->
    <g transform="translate(880, 180)">
      <!-- 内容标题 -->
      <text x="0" y="0" class="text-primary font-section-title text-bold">
        <tspan>{title}</tspan>
      </text>
      <!-- 内容副标题 -->
      <text x="0" y="48" class="text-secondary font-content-title">
        <tspan>{subtitle}</tspan>
      </text>

      <!-- 主要内容文本 -->
      <!-- 每行文本间距至少30px，这里使用35px确保足够间距 -->
      <text x="0" y="140" class="text-primary font-body-text">
        <tspan x="0" dy="0">{content}</tspan>
        <tspan x="0" dy="35">我是一名充满热情的软件工程师，专注于前端开发和用户体验设计。</tspan>
        <tspan x="0" dy="35">拥有五年以上行业经验，熟悉React、Vue和Node.js等主流技术栈，</tspan>
        <tspan x="0" dy="35">致力于构建高性能、可扩展的Web应用程序。在过去的项目中，</tspan>
        <tspan x="0" dy="35">我成功交付了多个关键产品，提升了用户满意度和业务效率。</tspan>
        <tspan x="0" dy="35">我善于团队协作，乐于分享知识和经验，渴望在创新环境中不断学习</tspan>
        <tspan x="0" dy="35">和成长。期待能与您共同探索更多可能性，为团队贡献我的价值。</tspan>
        <tspan x="0" dy="35">我的优势在于能够将复杂的技术概念转化为用户友好的界面，</tspan>
        <tspan x="0" dy="35">并具备解决问题和快速学习新技术的强大能力。</tspan>
      </text>

      <!-- 强调超大字体或数字突出核心要点 -->
      <!-- 超大字体需预留至少100px垂直空间 -->
      <text x="0" y="580" class="text-accent font-hero-title text-bold">
        <tspan>5+</tspan>
      </text>
      <text x="160" y="580" class="text-primary font-section-title text-bold">
        <tspan>年经验</tspan>
      </text>
      <text x="0" y="680" class="text-secondary font-small-text">
        <tspan>Years of Professional Experience</tspan>
      </text>

    </g>
  </g>
</svg>