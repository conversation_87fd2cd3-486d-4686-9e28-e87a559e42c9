<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color definitions based on the provided palette -->
    <style type="text/css">
      <![CDATA[
        .background { fill: #F8FAFC; }
        .primary-color { fill: #1E40AF; }
        .secondary-color { fill: #475569; }
        .accent-color { fill: #3B82F6; }
        .text-primary { fill: #1E293B; }
        .text-secondary { fill: #64748B; }
        .text-light { fill: #94A3B8; }
        .card-background { fill: #FFFFFF; }
        .card-border { stroke: #BAE6FD; }

        .font-primary { font-family: "Microsoft YaHei", "Segoe UI", sans-serif; }
        .font-secondary { font-family: "Source Han Sans CN", "Noto Sans CJK SC", sans-serif; }
        .font-accent { font-family: "Times New Roman", serif; }

        .main-title {
          font-size: 56px; /* main_title */
          font-weight: 700; /* bold */
          fill: #1E293B; /* text_primary */
        }
        .section-title {
          font-size: 36px; /* section_title */
          font-weight: 700; /* bold */
          fill: #1E293B; /* text_primary */
        }
        .content-title {
          font-size: 28px; /* content_title */
          font-weight: 600; /* semibold */
          fill: #1E293B; /* text_primary */
        }
        .body-text {
          font-size: 22px; /* body_text */
          font-weight: 400; /* normal */
          fill: #64748B; /* text_secondary */
        }
        .small-text {
          font-size: 16px; /* small_text */
          font-weight: 400; /* normal */
          fill: #64748B; /* text_secondary */
        }
        .caption {
          font-size: 14px; /* caption */
          font-weight: 400; /* normal */
          fill: #94A3B8; /* text_light */
        }

        /* Specific styles for this template */
        .chapter-title-chinese {
            font-size: 80px; /* Larger than main_title for strong impact */
            font-weight: 900; /* Black for maximum impact */
            fill: url(#textGradient);
            text-anchor: middle;
        }
        .chapter-subtitle-english {
            font-size: 36px; /* section_title for prominent subtitle */
            font-weight: 400; /* normal */
            fill: #475569; /* secondary_color or text_secondary for contrast */
            text-anchor: middle;
            letter-spacing: 0.05em; /* wider */
        }
        .page-number {
            font-size: 24px; /* Slightly larger small text */
            font-weight: 500; /* medium */
            fill: #94A3B8; /* text_light */
            text-anchor: end;
        }

        /* Decorative element styles */
        .deco-shape-1 { fill: #1E40AF; opacity: 0.1; } /* primary_color with transparency */
        .deco-shape-2 { fill: #3B82F6; opacity: 0.15; } /* accent_color with transparency */
        .deco-shape-3 { fill: #475569; opacity: 0.05; } /* secondary_color with transparency */
        .deco-line { stroke: #BAE6FD; stroke-width: 2; opacity: 0.5; } /* card_border */

        /* Icon style */
        .icon-style {
            fill: none;
            stroke: #3B82F6; /* Using accent_color for icons */
            stroke-width: 2;
            stroke-linecap: round;
            stroke-linejoin: round;
        }
      ]]>
    </style>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#475569" />
    </linearGradient>

    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>

    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#3B82F6" />
    </linearGradient>

    <linearGradient id="backgroundDecoGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#E0F2FE" />
      <stop offset="100%" stop-color="#F8FAFC" />
    </linearGradient>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="background" />

  <!-- Decorative Background Elements - Bento Grid inspired, abstract business shapes -->
  <!-- Using transparent colors to create depth and subtle texture -->
  <g transform="translate(0, -100)">
      <rect x="100" y="100" width="400" height="250" rx="20" ry="20" class="deco-shape-1" />
      <rect x="1500" y="50" width="300" height="200" rx="20" ry="20" class="deco-shape-2" />
      <rect x="200" y="700" width="350" height="200" rx="20" ry="20" class="deco-shape-3" />
      <rect x="1400" y="800" width="450" height="250" rx="20" ry="20" class="deco-shape-1" />

      <!-- Abstract data graph-like shapes -->
      <path d="M 600 200 L 750 150 L 900 250 L 1050 200 L 1200 180 L 1350 220 L 1500 190" class="deco-line" fill="none" stroke-dasharray="10 5" />
      <circle cx="750" cy="150" r="10" class="accent-color" opacity="0.3" />
      <circle cx="900" cy="250" r="10" class="accent-color" opacity="0.3" />
      <circle cx="1200" cy="180" r="10" class="accent-color" opacity="0.3" />

      <path d="M 600 900 L 750 850 L 900 950 L 1050 900 L 1200 880 L 1350 920 L 1500 890" class="deco-line" fill="none" stroke-dasharray="10 5" />
      <circle cx="750" cy="850" r="10" class="primary-color" opacity="0.3" />
      <circle cx="900" cy="950" r="10" class="primary-color" opacity="0.3" />
      <circle cx="1200" cy="880" r="10" class="primary-color" opacity="0.3" />
  </g>

  <!-- Main Content Area - Centered Chapter Title -->
  <g transform="translate(0, 0)">
    <!-- Chapter Title (Chinese, large, bold, gradient) -->
    <text x="960" y="480" class="font-primary chapter-title-chinese">
      {title}
    </text>

    <!-- Subtitle (English, smaller, as embellishment) -->
    <text x="960" y="560" class="font-secondary chapter-subtitle-english">
      {subtitle}
    </text>

    <!-- Abstract Icon / Graphic Element (Business/Data related) -->
    <!-- Represents growth or connection -->
    <g class="icon-style" transform="translate(960, 680) scale(2)">
        <!-- Upward trend arrow -->
        <path d="M -50 20 L 0 -20 L 50 20" stroke-width="3" />
        <path d="M 0 -20 L -10 -10 M 0 -20 L 10 -10" stroke-width="3" />
        <!-- Abstract connecting dots or data points -->
        <circle cx="-30" cy="30" r="5" fill="#3B82F6" stroke="none" />
        <circle cx="30" cy="30" r="5" fill="#1E40AF" stroke="none" />
        <line x1="-30" y1="30" x2="30" y2="30" stroke="#BAE6FD" stroke-width="1" stroke-dasharray="2 2" />
    </g>
  </g>

  <!-- Page Number -->
  <text x="1840" y="1020" class="font-primary page-number">
    3/10
  </text>

  <!-- Logo Placeholder (Top Left) -->
  <rect x="80" y="60" width="150" height="40" fill="#E0F2FE" rx="5" ry="5" />
  <text x="155" y="87" class="small-text text-primary" text-anchor="middle">
    {logo_url}
  </text>

</svg>