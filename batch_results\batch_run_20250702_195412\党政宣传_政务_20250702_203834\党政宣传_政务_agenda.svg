<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Primary Gradient for subtle background elements or text highlights -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E3A8A" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>

    <!-- Accent Gradient for highlights -->
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E3A8A" />
    </linearGradient>

    <!-- Background Gradient for subtle depth -->
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#F8FAFC" />
      <stop offset="100%" stop-color="#E0F2FE" />
    </linearGradient>

    <!-- Icon: Simple document outline -->
    <symbol id="iconDocument" viewBox="0 0 24 24">
      <path fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
      <polyline points="14 2 14 8 20 8" />
      <line x1="16" y1="13" x2="8" y2="13" />
      <line x1="16" y1="17" x2="8" y2="17" />
      <line x1="10" y1="9" x2="8" y2="9" />
    </symbol>

    <!-- Icon: Arrow right for navigation -->
    <symbol id="iconArrowRight" viewBox="0 0 24 24">
      <path fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" d="M5 12h14M12 5l7 7-7 7" />
    </symbol>

  </defs>

  <style>
    /* Global Styles */
    .font-primary {
      font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
    }
    .font-secondary {
      font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif;
    }
    .font-accent {
      font-family: 'Times New Roman', serif;
    }

    /* Colors */
    .color-primary { fill: #1E3A8A; }
    .color-secondary { fill: #1E40AF; }
    .color-accent { fill: #3B82F6; }
    .color-background { fill: #F8FAFC; }
    .color-text-primary { fill: #1E293B; }
    .color-text-secondary { fill: #64748B; }
    .color-text-light { fill: #94A3B8; }
    .color-white { fill: #FFFFFF; }
    .color-card-border { stroke: #BAE6FD; }

    /* Text Sizes */
    .text-hero-title { font-size: 72px; }
    .text-main-title { font-size: 56px; }
    .text-section-title { font-size: 36px; }
    .text-content-title { font-size: 28px; }
    .text-body { font-size: 22px; }
    .text-small { font-size: 16px; }
    .text-caption { font-size: 14px; }

    /* Font Weights */
    .font-normal { font-weight: 400; }
    .font-medium { font-weight: 500; }
    .font-semibold { font-weight: 600; }
    .font-bold { font-weight: 700; }

    /* Generic styles for elements */
    .card-background {
      fill: #FFFFFF;
      stroke: #BAE6FD;
      stroke-width: 1px;
      rx: 12px; /* border-radius */
    }

    .shadow {
      filter: drop-shadow(0px 4px 6px rgba(0, 0, 0, 0.1)) drop-shadow(0px 2px 4px rgba(0, 0, 0, 0.06));
    }

    /* Specific element styles */
    .header-logo-placeholder {
      fill: #1E3A8A;
      rx: 8px;
    }

    .progress-bar-bg {
      fill: #E0F2FE; /* container_background */
      rx: 8px;
    }

    .progress-bar-fill {
      fill: url(#accentGradient);
      rx: 8px;
    }

    .chapter-number {
        font-size: 48px; /* Larger for emphasis */
        font-weight: 700;
        fill: #3B82F6; /* accent_color */
    }

    .chapter-title {
        font-size: 28px;
        font-weight: 600;
        fill: #1E293B; /* text_primary */
    }

    .chapter-description {
        font-size: 18px; /* Slightly smaller than body for detail */
        fill: #64748B; /* text_secondary */
    }

    .icon-style {
      stroke: #3B82F6; /* Use accent color for icons */
      stroke-width: 2;
    }

  </style>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="color-background" />

  <!-- Subtle background decorative elements -->
  <rect x="0" y="0" width="400" height="400" fill="url(#primaryGradient)" opacity="0.05" />
  <circle cx="1800" cy="1000" r="150" fill="url(#accentGradient)" opacity="0.05" />

  <!-- Header Section -->
  <g class="header-section">
    <!-- Logo Placeholder -->
    <rect x="80" y="60" width="180" height="60" class="header-logo-placeholder" />
    <text x="170" y="98" text-anchor="middle" class="font-primary font-bold color-white text-content-title">
      <tspan x="170" dy="0">政务标识</tspan>
    </text>

    <!-- Main Title -->
    <text x="960" y="90" text-anchor="middle" class="font-primary font-bold color-text-primary text-main-title">
      <tspan x="960" dy="0">{title}</tspan>
    </text>
    <text x="960" y="145" text-anchor="middle" class="font-secondary font-normal color-text-secondary text-body">
      <tspan x="960" dy="0">{subtitle}</tspan>
    </text>

    <!-- Page Number -->
    <text x="1840" y="90" text-anchor="end" class="font-primary font-semibold color-text-secondary text-small">
      <tspan x="1840" dy="0">页面 2 / 10</tspan>
    </text>
  </g>

  <!-- Main Content Area - Directory Structure -->
  <g class="directory-section">
    <!-- Section Title -->
    <text x="80" y="250" class="font-primary font-bold color-text-primary text-section-title">
      <tspan x="80" dy="0">内容概览 和 章节导航</tspan>
    </text>
    <text x="80" y="295" class="font-secondary font-normal color-text-secondary text-body">
      <tspan x="80" dy="0">政策解读、工作成果、发展规划</tspan>
    </text>

    <!-- Directory Items (Bento Grid feel) -->
    <!-- Row 1 -->
    <g class="chapter-item">
      <rect x="80" y="350" width="560" height="240" class="card-background shadow" />
      <use xlink:href="#iconDocument" x="120" y="380" width="48" height="48" class="icon-style" />
      <text x="190" y="415" class="font-primary chapter-number">
        <tspan x="190" dy="0">01</tspan>
      </text>
      <text x="120" y="470" class="font-primary chapter-title">
        <tspan x="120" dy="0">第一部分: 政策核心解读</tspan>
      </text>
      <text x="120" y="510" class="font-secondary chapter-description">
        <tspan x="120" dy="0">深入理解国家最新政策要点</tspan>
        <tspan x="120" dy="30">和实施细则。</tspan>
      </text>
      <use xlink:href="#iconArrowRight" x="570" y="530" width="32" height="32" class="icon-style" />
    </g>

    <g class="chapter-item">
      <rect x="680" y="350" width="560" height="240" class="card-background shadow" />
      <use xlink:href="#iconDocument" x="720" y="380" width="48" height="48" class="icon-style" />
      <text x="790" y="415" class="font-primary chapter-number">
        <tspan x="790" dy="0">02</tspan>
      </text>
      <text x="720" y="470" class="font-primary chapter-title">
        <tspan x="720" dy="0">第二部分: 工作成果展示</tspan>
      </text>
      <text x="720" y="510" class="font-secondary chapter-description">
        <tspan x="720" dy="0">回顾过去一年重要工作成就</tspan>
        <tspan x="720" dy="30">和亮点项目。</tspan>
      </text>
      <use xlink:href="#iconArrowRight" x="1170" y="530" width="32" height="32" class="icon-style" />
    </g>

    <g class="chapter-item">
      <rect x="1280" y="350" width="560" height="240" class="card-background shadow" />
      <use xlink:href="#iconDocument" x="1320" y="380" width="48" height="48" class="icon-style" />
      <text x="1390" y="415" class="font-primary chapter-number">
        <tspan x="1390" dy="0">03</tspan>
      </text>
      <text x="1320" y="470" class="font-primary chapter-title">
        <tspan x="1320" dy="0">第三部分: 发展规划展望</tspan>
      </text>
      <text x="1320" y="510" class="font-secondary chapter-description">
        <tspan x="1320" dy="0">未来五年发展蓝图和关键</tspan>
        <tspan x="1320" dy="30">举措。</tspan>
      </text>
      <use xlink:href="#iconArrowRight" x="1770" y="530" width="32" height="32" class="icon-style" />
    </g>

    <!-- Row 2 -->
    <g class="chapter-item">
      <rect x="80" y="620" width="560" height="240" class="card-background shadow" />
      <use xlink:href="#iconDocument" x="120" y="650" width="48" height="48" class="icon-style" />
      <text x="190" y="685" class="font-primary chapter-number">
        <tspan x="190" dy="0">04</tspan>
      </text>
      <text x="120" y="740" class="font-primary chapter-title">
        <tspan x="120" dy="0">第四部分: 基层党建创新</tspan>
      </text>
      <text x="120" y="780" class="font-secondary chapter-description">
        <tspan x="120" dy="0">党建工作新模式和经验</tspan>
        <tspan x="120" dy="30">交流。</tspan>
      </text>
      <use xlink:href="#iconArrowRight" x="570" y="800" width="32" height="32" class="icon-style" />
    </g>

    <g class="chapter-item">
      <rect x="680" y="620" width="560" height="240" class="card-background shadow" />
      <use xlink:href="#iconDocument" x="720" y="650" width="48" height="48" class="icon-style" />
      <text x="790" y="685" class="font-primary chapter-number">
        <tspan x="790" dy="0">05</tspan>
      </text>
      <text x="720" y="740" class="font-primary chapter-title">
        <tspan x="720" dy="0">第五部分: 公众服务提升</tspan>
      </text>
      <text x="720" y="780" class="font-secondary chapter-description">
        <tspan x="720" dy="0">优化营商环境和便民措施</tspan>
        <tspan x="720" dy="30">介绍。</tspan>
      </text>
      <use xlink:href="#iconArrowRight" x="1170" y="800" width="32" height="32" class="icon-style" />
    </g>

    <g class="chapter-item">
      <rect x="1280" y="620" width="560" height="240" class="card-background shadow" />
      <use xlink:href="#iconDocument" x="1320" y="650" width="48" height="48" class="icon-style" />
      <text x="1390" y="685" class="font-primary chapter-number">
        <tspan x="1390" dy="0">06</tspan>
      </text>
      <text x="1320" y="740" class="font-primary chapter-title">
        <tspan x="1320" dy="0">第六部分: 廉政建设</tspan>
      </text>
      <text x="1320" y="780" class="font-secondary chapter-description">
        <tspan x="1320" dy="0">强化反腐倡廉机制和作风</tspan>
        <tspan x="1320" dy="30">建设。</tspan>
      </text>
      <use xlink:href="#iconArrowRight" x="1770" y="800" width="32" height="32" class="icon-style" />
    </g>

  </g>

  <!-- Progress Indicator and Footer -->
  <g class="progress-section">
    <text x="80" y="930" class="font-primary font-semibold color-text-primary text-content-title">
      <tspan x="80" dy="0">当前进度</tspan>
    </text>

    <!-- Progress Bar Background -->
    <rect x="80" y="960" width="1760" height="20" class="progress-bar-bg" />
    <!-- Progress Bar Fill (20% of 1760 = 352) -->
    <rect x="80" y="960" width="352" height="20" class="progress-bar-fill" />

    <text x="80" y="1000" class="font-primary font-normal color-text-secondary text-body">
      <tspan x="80" dy="0">第2章 / 共10章</tspan>
    </text>
    <text x="1840" y="1000" text-anchor="end" class="font-primary font-normal color-text-primary text-body">
      <tspan x="1840" dy="0">{date} | {author}</tspan>
    </text>
  </g>

</svg>