<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Colors and Typography Definitions -->
    <style type="text/css"><![CDATA[
      /* Color Palette */
      .background-color { fill: #F8FAFC; }
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .card-background { fill: #FFFFFF; }
      .card-border-color { stroke: #BAE6FD; }
      .icon-color { stroke: #4A86E8; fill: none; } /* Icons are outline style, no fill unless specified */

      /* Font Families */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* Text Styles - Based on font_sizes and font_weights */
      .main-title {
        font-size: 56px;
        font-weight: 700; /* bold */
        fill: #1E293B; /* text_primary */
      }
      .content-title {
        font-size: 28px;
        font-weight: 500; /* medium */
        fill: #64748B; /* text_secondary */
      }
      .section-title {
        font-size: 36px;
        font-weight: 600; /* semibold */
        fill: #1E293B; /* text_primary */
      }
      .body-text {
        font-size: 22px;
        font-weight: 400; /* normal */
        fill: #1E293B; /* text_primary */
      }
      .body-text-secondary {
        font-size: 22px;
        font-weight: 400; /* normal */
        fill: #64748B; /* text_secondary */
      }
      .small-text {
        font-size: 16px;
        font-weight: 400;
        fill: #64748B; /* text_secondary */
      }
      .large-emphasis-number {
        font-size: 120px; /* Custom large size for emphasis */
        font-weight: 900; /* black */
        fill: #3B82F6; /* accent_color */
      }
      .large-emphasis-label-cn {
        font-size: 32px; /* For Chinese large text */
        font-weight: 600;
        fill: #1E293B; /* text_primary */
      }
      .large-emphasis-label-en {
        font-size: 22px; /* For English small text */
        font-weight: 400;
        fill: #64748B; /* text_secondary */
      }

      /* Card Style */
      .card-shadow-style {
        filter: url(#cardShadow);
      }
      .card-border-style {
        stroke: #BAE6FD;
        stroke-width: 1px;
      }

      /* Gradients */
      .gradient-background-fill { fill: url(#gradientBackground); }
    ]]></style>

    <!-- Shadow Filter for Card -->
    <filter id="cardShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4" />
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="3" />
      <feColorMatrix result="matrixOut" in="blurOut" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" />
      <feOffset result="offOut2" in="SourceAlpha" dx="0" dy="2" />
      <feGaussianBlur result="blurOut2" in="offOut2" stdDeviation="2" />
      <feColorMatrix result="matrixOut2" in="blurOut2" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0" />
      <feMerge>
        <feMergeNode in="matrixOut" />
        <feMergeNode in="matrixOut2" />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>

    <!-- Subtle Background Gradient -->
    <linearGradient id="gradientBackground" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#F8FAFC" />
      <stop offset="100%" style="stop-color:#E0F2FE" />
    </linearGradient>

    <!-- Icon: Medical Heartbeat / ECG -->
    <g id="icon-ecg" class="icon-color" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <polyline points="0 8 10 8 15 0 25 16 30 8 40 8" />
    </g>

    <!-- Icon: Stethoscope -->
    <g id="icon-stethoscope" class="icon-color" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M12 21a9 9 0 1 0 0-18 9 9 0 0 0 0 18z" />
        <path d="M12 21v-4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v4" />
        <path d="M12 17h1a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-1a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2z" />
    </g>

  </defs>

  <!-- Background Layer -->
  <rect x="0" y="0" width="1920" height="1080" class="gradient-background-fill" />

  <!-- Main Content Group - Applies page margins -->
  <g transform="translate(80 60)">
    <!-- Header Section -->
    <text x="0" y="40" class="small-text font-primary">页面 4/10</text>
    <text x="1600" y="40" text-anchor="end" class="small-text font-primary">更新日期: {date}</text>
    <image x="0" y="80" width="150" height="auto" xlink:href="{logo_url}" />

    <!-- Page Title -->
    <text x="0" y="200" class="main-title font-primary">{title}</text>
    <text x="0" y="260" class="content-title font-secondary">{subtitle}</text>

    <!-- Section Divider -->
    <rect x="0" y="320" width="1760" height="1" fill="#BAE6FD" />

    <!-- Content Area - Organized into sections (Bento Grid inspired) -->
    <g transform="translate(0 380)">
      <!-- Left Content Column: Main Paragraph -->
      <g>
        <text x="0" y="0" class="section-title font-primary">
          <tspan x="0" dy="0">临床数据分析和研究成果</tspan>
        </text>
        <text x="0" y="50" class="body-text font-secondary">
          <tspan x="0" dy="0">{content}</tspan>
          <tspan x="0" dy="30">本研究旨在深入探讨新型治疗方案在临床应用中的有效性，</tspan>
          <tspan x="0" dy="30">通过大数据分析，我们揭示了关键的生物标记物，</tspan>
          <tspan x="0" dy="30">为个性化医疗提供了新的视角和数据支持。</tspan>
          <tspan x="0" dy="30">研究结果表明，该方案在提高患者生活质量和延长生存期方面</tspan>
          <tspan x="0" dy="30">展现出显著优势。</tspan>
        </text>
      </g>

      <!-- Right Content Column: Key Metric Card -->
      <g transform="translate(900 0)">
        <rect x="0" y="0" width="860" height="250" rx="12" ry="12" class="card-background card-shadow-style card-border-style" />
        <text x="430" y="100" text-anchor="middle" class="large-emphasis-number font-accent">85%</text>
        <text x="430" y="170" text-anchor="middle" class="large-emphasis-label-cn font-primary">治疗有效率提升</text>
        <text x="430" y="205" text-anchor="middle" class="large-emphasis-label-en font-secondary">
          (Treatment Efficacy Improvement)
        </text>
        <!-- Icon placement -->
        <use xlink:href="#icon-ecg" transform="translate(750 20)" width="40" height="16" />
      </g>

      <!-- Bottom Section: Key Points List -->
      <g transform="translate(0 420)">
        <text x="0" y="0" class="section-title font-primary">
          <tspan x="0" dy="0">核心研究要点</tspan>
        </text>
        <!-- Icon placement -->
        <use xlink:href="#icon-stethoscope" transform="translate(260 0)" width="32" height="32" />

        <!-- List Item 1 -->
        <g transform="translate(0 60)">
          <circle cx="10" cy="11" r="5" class="accent-color" />
          <text x="30" y="15" class="body-text font-secondary">
            <tspan x="30" dy="0">创新疗法：基于基因组学的精准靶向治疗方案。</tspan>
          </text>
        </g>

        <!-- List Item 2 -->
        <g transform="translate(0 120)"> <!-- 60px vertical spacing from previous item -->
          <circle cx="10" cy="11" r="5" class="accent-color" />
          <text x="30" y="15" class="body-text font-secondary">
            <tspan x="30" dy="0">数据驱动：集成多中心临床数据，进行深度机器学习分析。</tspan>
          </text>
        </g>

        <!-- List Item 3 -->
        <g transform="translate(0 180)"> <!-- 60px vertical spacing from previous item -->
          <circle cx="10" cy="11" r="5" class="accent-color" />
          <text x="30" y="15" class="body-text font-secondary">
            <tspan x="30" dy="0">安全性评估：严格遵循国际标准，确保患者用药安全和有效。</tspan>
          </text>
        </g>

        <!-- List Item 4 -->
        <g transform="translate(0 240)"> <!-- 60px vertical spacing from previous item -->
          <circle cx="10" cy="11" r="5" class="accent-color" />
          <text x="30" y="15" class="body-text font-secondary">
            <tspan x="30" dy="0">未来展望：为慢性病管理和预防提供新的策略和方向。</tspan>
          </text>
        </g>
      </g>
    </g>
  </g>
</svg>