<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Background Radial Gradient for "Diffuse" effect with dark theme -->
    <radialGradient id="backgroundGradient" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(960 540) rotate(90) scale(1080 1920)">
      <stop offset="0%" stop-color="#1E293B" /> <!-- Darker blue-black from text primary -->
      <stop offset="100%" stop-color="#0F172A" /> <!-- Slightly darker, almost black -->
    </radialGradient>

    <!-- Primary Gradient for elements (Blue tones) -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>

    <!-- Accent Gradient (Tesla Red for strong highlight) -->
    <linearGradient id="accentGradientRed" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#E31937" />
      <stop offset="100%" stop-color="rgba(227, 25, 55, 0.6)" />
    </linearGradient>

    <!-- Soft Glow Filter (for diffuse effect on elements) -->
    <filter id="softGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="15" result="blur" />
      <feMerge>
        <feMergeNode in="blur" />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>

    <!-- Card Shadow Filter -->
    <filter id="cardShadow" x="-10%" y="-10%" width="120%" height="120%">
      <feOffset dx="0" dy="8" />
      <feGaussianBlur stdDeviation="10" result="offsetBlur" />
      <feFlood flood-color="rgba(0, 0, 0, 0.3)" />
      <feComposite operator="in" in2="offsetBlur" />
      <feMerge>
        <feMergeNode />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>

  </defs>

  <style>
    /* General styles */
    .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
    .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
    .font-accent { font-family: 'Times New Roman', serif; }

    /* Text Colors */
    .text-primary { fill: #F8FAFC; } /* Light text on dark background */
    .text-secondary { fill: #94A3B8; } /* Lighter grey for secondary text */
    .text-accent { fill: url(#accentGradientRed); } /* Tesla Red for highlights */
    .text-blue { fill: #3B82F6; } /* Primary blue for specific titles */

    /* Font Sizes */
    .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
    .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
    .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; }
    .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; }
    .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
    .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; }
    .caption { font-size: 14px; font-weight: 300; line-height: 1.6; }

    /* Card Styling */
    .card-background { fill: #1E293B; filter: url(#cardShadow); rx: 12; ry: 12; } /* Dark card background, using text_primary dark blue */
    .card-border { stroke: #3B82F6; stroke-width: 2; rx: 12; ry: 12; } /* Primary blue border */

    /* Timeline specific styles */
    .timeline-line { stroke: url(#primaryGradient); stroke-width: 4; stroke-linecap: round; }
    .timeline-node { fill: url(#primaryGradient); stroke: #BAE6FD; stroke-width: 3; }
    .milestone-node { fill: url(#accentGradientRed); stroke: #E31937; stroke-width: 4; filter: url(#softGlow); }

    /* Decorative elements */
    .outline-shape { stroke: #06B6D4; stroke-width: 2; fill: none; opacity: 0.3; }
  </style>

  <!-- Background Layer -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)" />

  <!-- Decorative Background Elements (Subtle diffuse glow, using palette colors) -->
  <circle cx="100" cy="980" r="150" fill="#3B82F6" opacity="0.05" filter="url(#softGlow)" />
  <circle cx="1800" cy="100" r="180" fill="#06B6D4" opacity="0.05" filter="url(#softGlow)" />
  <rect x="100" y="100" width="300" height="150" rx="20" ry="20" fill="#1E40AF" opacity="0.08" filter="url(#softGlow)" />
  <rect x="1520" y="800" width="300" height="150" rx="20" ry="20" fill="#06B6D4" opacity="0.08" filter="url(#softGlow)" />

  <!-- Header Section -->
  <g id="header">
    <text x="120" y="100" class="hero-title font-primary text-primary">
      <tspan x="120" y="100">我的个人</tspan>
      <tspan x="120" y="170" class="text-accent">发展历程</tspan>
    </text>
    <text x="120" y="240" class="body-text font-secondary text-secondary">
      <tspan x="120" y="240">My Personal Journey and Milestones</tspan>
    </text>
  </g>

  <!-- Timeline Content -->
  <g id="timeline-section">
    <!-- Timeline Line -->
    <line x1="960" y1="280" x2="960" y2="1000" class="timeline-line" />

    <!-- Timeline Nodes and Events -->

    <!-- Event 1: Early Career -->
    <g id="event-1">
      <circle cx="960" cy="320" r="10" class="timeline-node" />
      <rect x="1000" y="280" width="400" height="150" class="card-background" />
      <rect x="1000" y="280" width="400" height="150" class="card-border" />
      <text x="1020" y="320" class="content-title font-primary text-blue">
        <tspan x="1020" y="320">{date} - 职业初期</tspan>
      </text>
      <text x="1020" y="355" class="body-text font-secondary text-primary">
        <tspan x="1020" y="355">Early Career Development</tspan>
        <tspan x="1020" y="385" class="small-text text-secondary">
          {content} 基础技能学习和项目实践，奠定职业生涯基石。
        </tspan>
      </text>
    </g>

    <!-- Event 2: Skill Enhancement -->
    <g id="event-2">
      <circle cx="960" cy="480" r="10" class="timeline-node" />
      <rect x="520" y="440" width="400" height="150" class="card-background" />
      <rect x="520" y="440" width="400" height="150" class="card-border" />
      <text x="540" y="480" class="content-title font-primary text-blue">
        <tspan x="540" y="480">{date} - 技能提升</tspan>
      </text>
      <text x="540" y="515" class="body-text font-secondary text-primary">
        <tspan x="540" y="515">Skill Enhancement and Growth</tspan>
        <tspan x="540" y="545" class="small-text text-secondary">
          {content} 深入学习前沿技术，提升解决复杂问题的能力。
        </tspan>
      </text>
    </g>

    <!-- Milestone 1: Key Project Completion (Highlighted) -->
    <g id="milestone-1">
      <circle cx="960" cy="640" r="18" class="milestone-node" />
      <rect x="1000" y="600" width="450" height="180" class="card-background" />
      <rect x="1000" y="600" width="450" height="180" class="card-border" />
      <text x="1020" y="640" class="section-title font-primary text-accent">
        <tspan x="1020" y="640">{date} - 里程碑项目</tspan>
      </text>
      <text x="1020" y="680" class="content-title font-secondary text-primary">
        <tspan x="1020" y="680">Major Project Completion</tspan>
        <tspan x="1020" y="715" class="body-text text-secondary">
          {content} 成功主导并完成核心项目，获得业界认可。
        </tspan>
        <tspan x="1020" y="745" class="small-text text-secondary">
          突出贡献 和#38; 团队协作
        </tspan>
      </text>
    </g>

    <!-- Event 3: Leadership Role -->
    <g id="event-3">
      <circle cx="960" cy="800" r="10" class="timeline-node" />
      <rect x="520" y="760" width="400" height="150" class="card-background" />
      <rect x="520" y="760" width="400" height="150" class="card-border" />
      <text x="540" y="800" class="content-title font-primary text-blue">
        <tspan x="540" y="800">{date} - 领导力发展</tspan>
      </text>
      <text x="540" y="835" class="body-text font-secondary text-primary">
        <tspan x="540" y="835">Leadership Development</tspan>
        <tspan x="540" y="865" class="small-text text-secondary">
          {content} 承担团队管理职责，培养和指导新人。
        </tspan>
      </text>
    </g>

    <!-- Event 4: Future Outlook -->
    <g id="event-4">
      <circle cx="960" cy="960" r="10" class="timeline-node" />
      <rect x="1000" y="920" width="400" height="150" class="card-background" />
      <rect x="1000" y="920" width="400" height="150" class="card-border" />
      <text x="1020" y="960" class="content-title font-primary text-blue">
        <tspan x="1020" y="960">{date} - 未来展望</tspan>
      </text>
      <text x="1020" y="995" class="body-text font-secondary text-primary">
        <tspan x="1020" y="995">Future Outlook and Goals</tspan>
        <tspan x="1020" y="1025" class="small-text text-secondary">
          {content} 持续学习，探索新领域，迎接更大挑战。
        </tspan>
      </text>
    </g>

  </g>

  <!-- Large Emphasized Number / Decorative Element -->
  <g id="emphasis-element">
    <text x="960" y="540" text-anchor="middle" class="hero-title font-accent text-accent" opacity="0.15">
      <tspan x="960" y="540">08</tspan>
    </text>
    <text x="960" y="600" text-anchor="middle" class="section-title font-primary text-secondary" opacity="0.2">
      <tspan x="960" y="600">Timeline</tspan>
    </text>
  </g>

  <!-- Simple Outline Graphics (Bento Grid like) -->
  <g id="decorative-graphics">
    <!-- Top-left corner -->
    <rect x="80" y="60" width="40" height="40" class="outline-shape" />
    <line x1="80" y1="60" x2="180" y2="60" class="outline-shape" />
    <line x1="80" y1="60" x2="80" y2="160" class="outline-shape" />

    <!-- Bottom-right corner -->
    <rect x="1800" y="980" width="40" height="40" class="outline-shape" />
    <line x1="1740" y1="1020" x2="1840" y2="1020" class="outline-shape" />
    <line x1="1840" y1="920" x2="1840" y2="1020" class="outline-shape" />

    <!-- Center-right element -->
    <rect x="1500" y="400" width="100" height="100" class="outline-shape" />
    <line x1="1500" y1="400" x2="1650" y2="400" class="outline-shape" />
    <line x1="1600" y1="400" x2="1600" y2="550" class="outline-shape" />

    <!-- Center-left element -->
    <rect x="300" y="600" width="100" height="100" class="outline-shape" />
    <line x1="250" y1="700" x2="400" y2="700" class="outline-shape" />
    <line x1="300" y1="550" x2="300" y2="700" class="outline-shape" />
  </g>

  <!-- Logo Placeholder (Top-left, subtle) -->
  <g id="logo-placeholder">
    <text x="120" y="40" class="small-text font-accent text-secondary" opacity="0.6">
      <tspan x="120" y="40">MyPortfolio</tspan>
    </text>
    <!-- Placeholder for an actual logo image if {logo_url} was used -->
    <!-- <image href="{logo_url}" x="120" y="20" width="80" height="auto" /> -->
  </g>

</svg>