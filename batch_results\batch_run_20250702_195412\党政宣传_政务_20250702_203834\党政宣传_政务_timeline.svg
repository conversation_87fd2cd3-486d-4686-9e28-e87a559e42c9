<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Colors and Typography Definitions -->
    <style type="text/css">
      /* Color Palette */
      .primary-color { fill: #1E3A8A; }
      .secondary-color { fill: #1E40AF; }
      .accent-color { fill: #3B82F6; }
      .background-color { fill: #F8FAFC; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .card-background { fill: #FFFFFF; }
      .card-border { stroke: #BAE6FD; }

      /* Font System */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      .hero-title { font-size: 72px; font-weight: 700; }
      .main-title { font-size: 56px; font-weight: 700; }
      .section-title { font-size: 36px; font-weight: 600; }
      .content-title { font-size: 28px; font-weight: 500; }
      .body-text { font-size: 22px; font-weight: 400; } /* Line height handled by dy in tspans */
      .small-text { font-size: 16px; font-weight: 400; }
      .caption-text { font-size: 14px; font-weight: 400; }

      /* Text Alignment */
      .text-center { text-anchor: middle; }
      .text-left { text-anchor: start; }
      .text-right { text-anchor: end; }

      /* Timeline Specific Styles */
      .timeline-line { stroke: #1E3A8A; stroke-width: 4; stroke-linecap: round; }
      .timeline-node-circle { fill: #1E3A8A; stroke: #F8FAFC; stroke-width: 6; }
      .milestone-node-circle { fill: #3B82F6; stroke: #F8FAFC; stroke-width: 8; }
      .node-connector-line { stroke: #64748B; stroke-width: 2; stroke-dasharray: 4 4; } /* Dashed line for connection */

      /* Card Styling with Shadow */
      .card-base { rx: 12; } /* Border radius applied directly */
      .card-shadow-filter { filter: url(#drop-shadow); }
    </style>

    <!-- Gradients for Decorative Elements -->
    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#1E3A8A" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>
    <linearGradient id="highlightGradient" x1="0" y1="0" x2="0" y2="1">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.8" />
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0.1" />
    </linearGradient>

    <!-- Drop Shadow Filter for Cards (subtle shadow) -->
    <filter id="drop-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="4"/>
      <feGaussianBlur stdDeviation="6"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Icon for Milestones (example: a star) -->
    <symbol id="icon-star" viewBox="0 0 24 24">
      <path fill="#F8FAFC" d="M12 17.27L18.18 21L16.54 13.97L22 9.24L14.81 8.62L12 2L9.19 8.62L2 9.24L7.46 13.97L5.82 21L12 17.27Z"/>
    </symbol>
  </defs>

  <!-- Background Layer -->
  <rect x="0" y="0" width="1920" height="1080" class="background-color"/>

  <!-- Header Section -->
  <g id="header-section">
    <text x="960" y="120" class="main-title text-primary font-primary text-center">{title}</text>
    <text x="960" y="180" class="section-title text-secondary font-primary text-center">{subtitle}</text>
  </g>

  <!-- Main Timeline Line -->
  <line x1="960" y1="280" x2="960" y2="890" class="timeline-line"/>

  <!-- Timeline Nodes and Content Blocks -->
  <g id="timeline-content">
    <!-- Node 1: Left Content Block -->
    <g id="timeline-node-1">
      <circle cx="960" cy="320" r="12" class="timeline-node-circle"/>
      <line x1="960" y1="320" x2="700" y2="320" class="node-connector-line"/>
      <rect x="140" y="250" width="560" height="140" class="card-background card-border card-shadow-filter"/>
      <text x="180" y="290" class="content-title text-primary font-primary text-left">{date} 年</text>
      <text x="180" y="330" class="body-text text-primary font-primary text-left">
        <tspan x="180" dy="0">{content}</tspan>
        <tspan x="180" dy="30">本段描述了重要的历史事件和</tspan>
        <tspan x="180" dy="30">关键的成就。</tspan>
      </text>
    </g>

    <!-- Node 2: Right Content Block -->
    <g id="timeline-node-2">
      <circle cx="960" cy="510" r="12" class="timeline-node-circle"/>
      <line x1="960" y1="510" x2="1220" y2="510" class="node-connector-line"/>
      <rect x="1220" y="440" width="560" height="140" class="card-background card-border card-shadow-filter"/>
      <text x="1740" y="480" class="content-title text-primary font-primary text-right">{date} 年</text>
      <text x="1740" y="520" class="body-text text-primary font-primary text-right">
        <tspan x="1740" dy="0">{content}</tspan>
        <tspan x="1740" dy="30">这是发展历程中的一个重要节点</tspan>
        <tspan x="1740" dy="30">标志新的阶段。</tspan>
      </text>
    </g>

    <!-- Node 3: Left Content Block -->
    <g id="timeline-node-3">
      <circle cx="960" cy="700" r="12" class="timeline-node-circle"/>
      <line x1="960" y1="700" x2="700" y2="700" class="node-connector-line"/>
      <rect x="140" y="630" width="560" height="140" class="card-background card-border card-shadow-filter"/>
      <text x="180" y="670" class="content-title text-primary font-primary text-left">{date} 年</text>
      <text x="180" y="710" class="body-text text-primary font-primary text-left">
        <tspan x="180" dy="0">{content}</tspan>
        <tspan x="180" dy="30">政策深入实施和成果的显现</tspan>
        <tspan x="180" dy="30">为未来奠定坚实基础。</tspan>
      </text>
    </g>

    <!-- Milestone Node: Centered and Prominent -->
    <g id="milestone-node">
      <circle cx="960" cy="890" r="20" class="milestone-node-circle"/>
      <use xlink:href="#icon-star" x="944" y="874" width="32" height="32"/> <!-- Icon inside milestone circle -->
      <text x="960" y="940" class="section-title text-primary font-primary text-center">
        <tspan x="960" dy="0">{date} 关键里程碑</tspan>
      </text>
      <text x="960" y="980" class="body-text text-secondary font-primary text-center">
        <tspan x="960" dy="0">A Key Milestone and Achievement</tspan>
      </text>
      <text x="960" y="1010" class="body-text text-primary font-primary text-center">
        <tspan x="960" dy="0">{content}</tspan>
        <tspan x="960" dy="30">这一阶段的重大突破和创新。</tspan>
      </text>
    </g>
  </g>

  <!-- Decorative Elements for Visual Enhancement -->
  <g id="decorative-background-elements">
    <!-- Top-left abstract shape -->
    <path d="M0 0 H400 V100 C300 150, 100 150, 0 100 V0 Z" fill="url(#primaryGradient)" opacity="0.1"/>
    <!-- Bottom-right abstract shape -->
    <path d="M1920 1080 H1520 V980 C1620 930, 1820 930, 1920 980 V1080 Z" fill="url(#primaryGradient)" opacity="0.1" transform="rotate(180 1920 1080)"/>

    <!-- Subtle vertical gradient lines for tech feel -->
    <rect x="50" y="50" width="2" height="200" fill="url(#highlightGradient)"/>
    <rect x="1868" y="830" width="2" height="200" fill="url(#highlightGradient)"/>
  </g>
</svg>