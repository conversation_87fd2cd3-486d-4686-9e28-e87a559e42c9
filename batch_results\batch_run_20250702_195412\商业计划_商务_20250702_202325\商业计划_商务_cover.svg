<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 背景渐变 -->
    <linearGradient id="backgroundGradient" x1="960" y1="0" x2="960" y2="1080" gradientUnits="userSpaceOnUse">
      <stop stop-color="#F8FAFC"/>
      <stop offset="1" stop-color="#E0F2FE"/>
    </linearGradient>

    <!-- 强调色渐变 -->
    <linearGradient id="accentGradient" x1="0" y1="0" x2="1920" y2="1080" gradientUnits="userSpaceOnUse">
      <stop stop-color="#3B82F6"/>
      <stop offset="1" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- 主色渐变 -->
    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1920" y2="1080" gradientUnits="userSpaceOnUse">
      <stop stop-color="#1E40AF"/>
      <stop offset="1" stop-color="#475569"/>
    </linearGradient>

    <!-- 文字渐变 (如果需要，目前使用纯色文字) -->
    <linearGradient id="textGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox">
      <stop stop-color="#1E3A8A"/>
      <stop offset="1" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- 阴影滤镜 -->
    <filter id="cardShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="6" flood-color="rgba(0, 0, 0, 0.1)"/>
    </filter>
  </defs>

  <style>
    /* 字体定义 */
    @font-face {
      font-family: 'Microsoft YaHei';
      src: local('Microsoft YaHei'), local('微软雅黑');
    }
    @font-face {
      font-family: 'Segoe UI';
      src: local('Segoe UI');
    }
    @font-face {
      font-family: 'Source Han Sans CN';
      src: local('Source Han Sans CN'), local('思源黑体 CN');
    }

    /* 基础样式 */
    .background {
      fill: url(#backgroundGradient);
    }
    .main-title {
      font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
      font-size: 72px; /* hero_title */
      font-weight: 700; /* bold */
      fill: #1E293B; /* text_primary */
      text-anchor: middle;
      line-height: 1.1; /* tight */
    }
    .subtitle {
      font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
      font-size: 36px; /* section_title */
      font-weight: 400; /* normal */
      fill: #475569; /* secondary_color */
      text-anchor: middle;
      line-height: 1.4; /* normal */
    }
    .date-author {
      font-family: 'Source Han Sans CN', 'Microsoft YaHei', sans-serif;
      font-size: 22px; /* body_text */
      font-weight: 400; /* normal */
      fill: #64748B; /* text_secondary */
      text-anchor: middle;
      line-height: 1.6; /* relaxed */
    }
    .page-number {
      font-family: 'Source Han Sans CN', 'Microsoft YaHei', sans-serif;
      font-size: 16px; /* small_text */
      font-weight: 400; /* normal */
      fill: #94A3B8; /* text_light */
      text-anchor: end;
    }
    /* 颜色类 */
    .primary-color-fill {
      fill: #1E40AF;
    }
    .accent-color-fill {
      fill: #3B82F6;
    }
    .card-background {
      fill: #FFFFFF;
      stroke: #BAE6FD;
      stroke-width: 1px;
      filter: url(#cardShadow);
    }
  </style>

  <!-- 背景层 -->
  <rect x="0" y="0" width="1920" height="1080" class="background" />

  <!-- 装饰元素：左下角抽象曲线 -->
  <path d="M0 800 C 300 950, 600 1080, 960 1080 L 0 1080 Z" class="primary-color-fill" opacity="0.05" />
  <path d="M0 850 C 250 980, 500 1080, 800 1080 L 0 1080 Z" class="primary-color-fill" opacity="0.08" />

  <!-- 装饰元素：右上角抽象几何块 -->
  <path d="M1920 0 L 1500 0 C 1700 200, 1920 400, 1920 600 Z" class="accent-color-fill" opacity="0.05" />
  <path d="M1920 0 L 1600 0 C 1750 150, 1920 300, 1920 450 Z" class="accent-color-fill" opacity="0.08" />

  <!-- Logo区域 -->
  <g class="logo-container">
    <rect x="80" y="60" width="200" height="80" rx="8" class="card-background" />
    <!-- Logo图片占位符，请替换为实际Logo的URL -->
    <image href="{logo_url}" x="100" y="70" width="160" height="60" preserveAspectRatio="xMidYMid meet" />
  </g>

  <!-- 主标题和副标题区域 -->
  <g transform="translate(960, 480)">
    <!-- 主标题 -->
    <text x="0" y="0" class="main-title">
      <tspan x="0" dy="0">{title}</tspan>
    </text>

    <!-- 副标题 -->
    <text x="0" y="90" class="subtitle">
      <tspan x="0" dy="0">{subtitle}</tspan>
    </text>

    <!-- 日期和作者信息 -->
    <text x="0" y="200" class="date-author">
      <tspan x="0" dy="0">日期: {date}</tspan>
      <tspan x="0" dy="40">作者: {author}</tspan>
    </text>
  </g>

  <!-- 页面序号 -->
  <text x="1840" y="1030" class="page-number">
    <tspan x="1840" dy="0">1/10</tspan>
  </text>
</svg>