<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Filters for shadows -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="6" flood-color="#000000" flood-opacity="0.1"/>
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="#000000" flood-opacity="0.06"/>
    </filter>
    <filter id="hover_shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="10" stdDeviation="15" flood-color="#000000" flood-opacity="0.1"/>
      <feDropShadow dx="0" dy="4" stdDeviation="6" flood-color="#000000" flood-opacity="0.05"/>
    </filter>

    <!-- Gradients -->
    <linearGradient id="gradientAccent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#1E3A8A"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- Icon Example (a simple chart icon) -->
    <g id="iconChart">
      <rect x="0" y="10" width="6" height="20" fill="#3B82F6"/>
      <rect x="10" y="0" width="6" height="30" fill="#3B82F6"/>
      <rect x="20" y="15" width="6" height="15" fill="#3B82F6"/>
    </g>
  </defs>

  <style>
    /* Font Definitions */
    .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
    .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
    .font-accent { font-family: 'Times New Roman', serif; }

    /* Font Sizes */
    .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; } /* 100px vertical space */
    .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
    .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; } /* 30px minimum dy */
    .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; }
    .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; } /* 30px minimum dy */
    .small-text { font-size: 16px; font-weight: 400; line-height: 1.4; }
    .caption-text { font-size: 14px; font-weight: 400; line-height: 1.4; }

    /* Colors */
    .bg-color { fill: #F8FAFC; }
    .primary-color { fill: #1E40AF; }
    .secondary-color { fill: #475569; }
    .accent-color { fill: #3B82F6; }
    .text-primary-color { fill: #1E293B; }
    .text-secondary-color { fill: #64748B; }
    .text-light-color { fill: #94A3B8; }
    .card-background-color { fill: #FFFFFF; }
    .card-border-color { stroke: #BAE6FD; }

    /* Card Styling */
    .card-style {
      fill: #FFFFFF;
      stroke: #BAE6FD;
      stroke-width: 1px;
      filter: url(#shadow);
    }
    /* Note: :hover pseudo-class is not consistently supported for SVG elements in all contexts,
       but is included here for completeness based on design principles. */
    .card-style:hover {
      filter: url(#hover_shadow);
    }

    /* General text alignment */
    .text-align-left { text-anchor: start; }
    .text-align-right { text-anchor: end; }
    .text-align-center { text-anchor: middle; }
  </style>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color"/>

  <!-- Decorative geometric element (subtle gradient) -->
  <rect x="0" y="0" width="300" height="300" fill="url(#gradientAccent)" opacity="0.05" transform="rotate(45 0 0) translate(-100 800)"/>
  <rect x="1620" y="0" width="300" height="300" fill="url(#gradientAccent)" opacity="0.05" transform="rotate(45 1920 0) translate(-100 -500)"/>

  <!-- Logo Placeholder -->
  <image x="80" y="60" width="120" height="40" href="{logo_url}" />
  <text x="80" y="120" class="font-primary small-text text-primary-color">公司标志</text>

  <!-- Main Content Area -->
  <!-- Content area for text and image: Max width 1760px, centered horizontally within 1920px -->
  <!-- Left margin: 80px, Right margin: 80px -->

  <!-- Image Column (Left) -->
  <!-- Image container: 800px width, 600px height as suggested -->
  <!-- Centered vertically: (1080 - 60 - 60 - 600) / 2 + 60 = 180 + 60 = 240px -->
  <g id="image-column">
    <rect x="120" y="240" width="800" height="600" rx="12" ry="12" class="card-style"/>
    <!-- Image placeholder inside the card -->
    <image x="120" y="240" width="800" height="600" href="{image_url}" preserveAspectRatio="xMidYMid slice"/>
    <text x="520" y="540" class="font-primary text-secondary-color body-text text-align-center" fill="#94A3B8">
      <tspan x="520" y="540">图片占位符</tspan>
      <tspan x="520" dy="30">建议尺寸 800x600px</tspan>
    </text>
  </g>

  <!-- Text Column (Right) -->
  <!-- Starting X: Image right edge (120 + 800 = 920) + 80px spacing = 1000px -->
  <!-- Text column width: 1920 - 80 (right margin) - 1000 = 840px -->
  <g id="text-column">
    <!-- Title -->
    <text x="1000" y="280" class="font-primary section-title text-primary-color text-align-left">
      <tspan x="1000" y="280">{title}</tspan>
    </text>

    <!-- Subtitle -->
    <text x="1000" y="340" class="font-primary content-title text-secondary-color text-align-left">
      <tspan x="1000" y="340">{subtitle}</tspan>
    </text>

    <!-- Large Number/Metric -->
    <!-- Ensure 100px vertical space for large font -->
    <text x="1000" y="480" class="font-primary hero-title accent-color text-align-left">
      <tspan x="1000" y="480">95%</tspan>
    </text>
    <text x="1000" y="530" class="font-primary small-text text-secondary-color text-align-left">
      <tspan x="1000" y="530">市场份额增长预估</tspan>
      <tspan x="1000" dy="25" class="font-secondary">Market Share Growth Estimate</tspan>
    </text>

    <!-- Body Content -->
    <!-- Starting Y for body content, ensuring 50px space from previous element -->
    <text x="1000" y="620" class="font-primary body-text text-primary-color text-align-left">
      <tspan x="1000" y="620">{content}</tspan>
      <tspan x="1000" dy="30">我们深入分析了市场动态和消费者行为，</tspan>
      <tspan x="1000" dy="30">识别出关键增长点和潜在机遇。本报告</tspan>
      <tspan x="1000" dy="30">将详细阐述我们的战略规划和财务预测，</tspan>
      <tspan x="1000" dy="30">旨在为投资者提供清晰和全面的视图。</tspan>
      <tspan x="1000" dy="50" class="font-secondary">Our in-depth analysis of market dynamics and</tspan>
      <tspan x="1000" dy="30" class="font-secondary">consumer behavior identifies key growth points</tspan>
      <tspan x="1000" dy="30" class="font-secondary">and potential opportunities. This report will detail</tspan>
      <tspan x="1000" dy="30" class="font-secondary">our strategic planning and financial forecasts,</tspan>
      <tspan x="1000" dy="30" class="font-secondary">aiming to provide investors with a clear and</tspan>
      <tspan x="1000" dy="30" class="font-secondary">comprehensive view.</tspan>
    </text>

    <!-- Simple Icon for visual embellishment -->
    <use href="#iconChart" x="1000" y="900" width="30" height="30"/>
    <text x="1040" y="918" class="font-primary small-text text-secondary-color">数据分析支持</text>
  </g>

  <!-- Footer -->
  <g id="footer">
    <text x="80" y="1020" class="font-primary small-text text-light-color text-align-left">
      <tspan x="80" y="1020">日期: {date}</tspan>
      <tspan x="80" dy="20">作者: {author}</tspan>
    </text>
    <text x="1840" y="1020" class="font-primary small-text text-light-color text-align-right">5/10</text>
  </g>

</svg>