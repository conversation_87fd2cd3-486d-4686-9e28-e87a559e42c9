<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- 渐变色定义 -->
    <linearGradient id="gradientPrimary" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E3A8A" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>
    <linearGradient id="gradientAccent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E3A8A" />
    </linearGradient>
    <linearGradient id="gradientAccentTransparent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="1" />
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0.3" />
    </linearGradient>
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#F8FAFC" />
      <stop offset="100%" stop-color="#E0F2FE" />
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E3A8A" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>

    <!-- 滤镜定义 -->
    <filter id="shadowFilter" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4" />
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="4" />
      <feBlend in="SourceGraphic" in2="blurOut" mode="normal" />
    </filter>
  </defs>

  <style>
    /* 字体定义 */
    .font-primary {
      font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
    }
    .font-secondary {
      font-family: "Source Han Sans CN", "Noto Sans CJK SC", sans-serif;
    }
    .font-accent {
      font-family: "Times New Roman", serif;
    }

    /* 字体大小和粗细 */
    .hero-title {
      font-size: 72px;
      font-weight: 700; /* bold */
      line-height: 1.1; /* tight */
    }
    .main-title {
      font-size: 56px;
      font-weight: 700; /* bold */
      line-height: 1.1;
    }
    .section-title {
      font-size: 36px;
      font-weight: 600; /* semibold */
      line-height: 1.4; /* normal */
    }
    .content-title {
      font-size: 28px;
      font-weight: 500; /* medium */
      line-height: 1.4;
    }
    .body-text {
      font-size: 22px;
      font-weight: 400; /* normal */
      line-height: 1.6; /* relaxed */
    }
    .small-text {
      font-size: 16px;
      font-weight: 400;
      line-height: 1.4;
    }
    .caption-text {
      font-size: 14px;
      font-weight: 400;
      line-height: 1.4;
    }
    .large-number {
      font-size: 240px; /* 超大尺寸强调 */
      font-weight: 900; /* black */
      line-height: 1;
      letter-spacing: -0.05em;
    }

    /* 颜色定义 */
    .fill-primary { fill: #1E3A8A; }
    .fill-secondary { fill: #1E40AF; }
    .fill-accent { fill: #3B82F6; }
    .fill-background { fill: #F8FAFC; }
    .fill-text-primary { fill: #1E293B; }
    .fill-text-secondary { fill: #64748B; }
    .fill-container-background { fill: #E0F2FE; }
    .stroke-accent { stroke: #3B82F6; }
    .stroke-card-border { stroke: #BAE6FD; }

    /* 文本对齐 */
    .text-center { text-anchor: middle; }
    .text-left { text-anchor: start; }
    .text-right { text-anchor: end; }

    /* Bento Grid 元素样式 */
    .bento-card {
      fill: #FFFFFF; /* card_background */
      stroke: #BAE6FD; /* card_border */
      stroke-width: 1px;
      rx: 12; /* border_radius */
      ry: 12;
      filter: url(#shadowFilter);
    }
    .bento-module-dark {
      fill: #1E293B; /* 使用深色文本主色作为深色模块背景 */
      rx: 12;
      ry: 12;
      filter: url(#shadowFilter);
    }
    .bento-module-light {
      fill: #E0F2FE; /* container_background */
      rx: 12;
      ry: 12;
      filter: url(#shadowFilter);
    }
  </style>

  <!-- 背景 -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)" />

  <!-- 装饰元素 - 左上角 (抽象波浪/几何图形) -->
  <path d="M0 0 H400 C350 50 300 100 250 150 C200 200 150 250 100 300 C50 350 0 400 0 400 V0 Z" fill="url(#gradientPrimary)" opacity="0.8"/>
  <path d="M0 0 H350 C300 40 250 80 200 120 C150 160 100 200 50 240 C25 280 0 320 0 320 V0 Z" fill="url(#gradientAccent)" opacity="0.6"/>

  <!-- 装饰元素 - 右下角 (抽象几何图形) -->
  <path d="M1920 1080 V700 C1870 750 1820 800 1770 850 C1720 900 1670 950 1620 1000 C1570 1050 1520 1080 1520 1080 H1920 Z" fill="url(#gradientPrimary)" opacity="0.8"/>
  <path d="M1920 1080 V780 C1870 820 1820 860 1770 900 C1720 940 1670 980 1620 1020 C1570 1060 1520 1080 1520 1080 H1920 Z" fill="url(#gradientAccent)" opacity="0.6"/>

  <!-- 主要内容区域 - Bento Grid 风格布局 -->
  <!-- 标题中心模块 -->
  <rect x="180" y="200" width="1560" height="480" class="bento-module-dark" />

  <!-- 侧边模块1 (左侧) -->
  <rect x="180" y="720" width="760" height="280" class="bento-module-light" />

  <!-- 侧边模块2 (右侧) -->
  <rect x="980" y="720" width="760" height="280" class="bento-module-light" />

  <!-- Logo 占位符 -->
  <g class="logo-area" transform="translate(80, 60)">
    <rect x="0" y="0" width="200" height="60" fill="#FFFFFF" rx="8" ry="8" filter="url(#shadowFilter)"/>
    <text x="100" y="38" class="small-text font-primary fill-text-primary text-center">
      {logo_url}
    </text>
    <!-- 实际使用时，可替换为 <image xlink:href="{logo_url}" x="10" y="10" width="180" height="40" /> -->
  </g>

  <!-- 超大数字/强调元素 -->
  <text x="960" y="520" class="large-number font-accent text-center" fill="url(#gradientAccentTransparent)">
    2024
  </text>

  <!-- 主标题 -->
  <text x="960" y="380" class="hero-title font-primary text-center" fill="url(#textGradient)">
    <tspan x="960" dy="0">{title}</tspan>
  </text>

  <!-- 副标题 -->
  <text x="960" y="460" class="section-title font-secondary fill-text-secondary text-center">
    <tspan x="960" dy="0">{subtitle}</tspan>
  </text>

  <!-- 占位符：简洁的勾线图形化元素 (例如：图表图标) -->
  <g transform="translate(260, 780)">
    <rect x="0" y="0" width="100" height="80" rx="8" ry="8" stroke="#3B82F6" stroke-width="2" fill="none"/>
    <line x1="10" y1="70" x2="30" y2="40" stroke="#3B82F6" stroke-width="2"/>
    <line x1="30" y1="40" x2="50" y2="60" stroke="#3B82F6" stroke-width="2"/>
    <line x1="50" y1="60" x2="70" y2="30" stroke="#3B82F6" stroke-width="2"/>
    <line x1="70" y1="30" x2="90" y2="50" stroke="#3B82F6" stroke-width="2"/>
    <circle cx="15" cy="70" r="3" fill="#3B82F6"/>
    <circle cx="35" cy="40" r="3" fill="#3B82F6"/>
    <circle cx="55" cy="60" r="3" fill="#3B82F6"/>
    <circle cx="75" cy="30" r="3" fill="#3B82F6"/>
    <circle cx="95" cy="50" r="3" fill="#3B82F6"/>
  </g>

  <text x="310" y="900" class="content-title font-primary fill-text-primary text-center">
    <tspan x="310" dy="0">工作成效</tspan>
    <tspan x="310" dy="35" class="small-text font-secondary fill-text-secondary">Work Achievements</tspan>
  </text>

  <!-- 占位符：另一个简洁的勾线图形化元素 (例如：齿轮图标) -->
  <g transform="translate(1060, 780)">
    <circle cx="50" cy="50" r="40" stroke="#3B82F6" stroke-width="2" fill="none"/>
    <circle cx="50" cy="50" r="15" stroke="#3B82F6" stroke-width="2" fill="none"/>
    <line x1="50" y1="10" x2="50" y2="90" stroke="#3B82F6" stroke-width="2"/>
    <line x1="10" y1="50" x2="90" y2="50" stroke="#3B82F6" stroke-width="2"/>
    <line x1="20" y1="20" x2="80" y2="80" stroke="#3B82F6" stroke-width="2"/>
    <line x1="20" y1="80" x2="80" y2="20" stroke="#3B82F6" stroke-width="2"/>
  </g>

  <text x="1110" y="900" class="content-title font-primary fill-text-primary text-center">
    <tspan x="1110" dy="0">发展规划</tspan>
    <tspan x="1110" dy="35" class="small-text font-secondary fill-text-secondary">Development Plan</tspan>
  </text>

  <!-- 页脚/页码 -->
  <text x="1840" y="1020" class="caption-text font-secondary fill-text-secondary text-right">
    页面 1/10
  </text>

</svg>