<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- 样式定义 -->
    <style type="text/css">
      /* 调色板 */
      .bg-color { fill: #F8FAFC; }
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .card-background { fill: #FFFFFF; }
      .card-border { stroke: #BAE6FD; stroke-width: 1; }
      .icon-color { fill: #3B82F6; stroke: #3B82F6; stroke-width: 2; } /* 使用强调色作为图标颜色 */

      /* 字体 */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* 字号 */
      .hero-title-size { font-size: 72px; }
      .main-title-size { font-size: 56px; }
      .section-title-size { font-size: 36px; }
      .content-title-size { font-size: 28px; }
      .body-text-size { font-size: 22px; }
      .small-text-size { font-size: 16px; }
      .caption-size { font-size: 14px; }

      /* 字重 */
      .font-normal { font-weight: 400; }
      .font-medium { font-weight: 500; }
      .font-semibold { font-weight: 600; }
      .font-bold { font-weight: 700; }

      /* 通用文本样式 */
      .title-style {
        font-size: 56px; /* main_title */
        font-weight: 700; /* bold */
        fill: #1E293B; /* text_primary */
        text-anchor: start;
      }
      .subtitle-style {
        font-size: 28px; /* content_title */
        font-weight: 500; /* medium */
        fill: #475569; /* secondary_color or text_secondary */
        text-anchor: start;
      }
      .body-text-style {
        font-size: 22px; /* body_text */
        font-weight: 400; /* normal */
        fill: #1E293B; /* text_primary */
        line-height: 1.6; /* 宽松行高 */
      }
      .list-item-style {
        font-size: 22px; /* body_text */
        font-weight: 400; /* normal */
        fill: #1E293B; /* text_primary */
      }
      .bullet-point-style {
        fill: #3B82F6; /* accent_color */
      }
      .emphasis-text-style {
        font-size: 36px; /* section_title */
        font-weight: 700; /* bold */
        fill: #3B82F6; /* accent_color */
      }

      /* 卡片样式 */
      .card-style {
        fill: #FFFFFF;
        stroke: #BAE6FD;
        stroke-width: 1;
        rx: 12; /* border_radius */
        ry: 12;
      }

      /* 阴影效果 (通过滤镜实现) */
      .shadow-effect {
        filter: url(#drop-shadow);
      }

    </style>

    <!-- 滤镜定义：投影 -->
    <filter id="drop-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="4"></feGaussianBlur>
      <feOffset dx="0" dy="4" result="offsetblur"></feOffset>
      <feFlood flood-color="rgba(0,0,0,0.1)"></feFlood>
      <feComposite in2="offsetblur" operator="in"></feComposite>
      <feMerge>
        <feMergeNode></feMergeNode>
        <feMergeNode in="SourceGraphic"></feMergeNode>
      </feMerge>
    </filter>

    <!-- 渐变定义 -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#475569" />
    </linearGradient>

    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>

    <linearGradient id="accentTransparentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.8" />
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0.2" />
    </linearGradient>

    <!-- 可复用图标路径 (概念性：代表创新和技术) -->
    <symbol id="icon-gear" viewBox="0 0 24 24">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.71-7-4.23-7-8.93h2c0 3.95 3.05 7 7 7V19.93zm6.93-2.66c-1.57 1.34-3.66 2.23-5.93 2.23V17c2.09 0 3.99-.78 5.46-2.04l1.47 1.63zM12 4.07V6c-3.95 0-7 3.05-7 7H3c0-4.7 3.05-8.22 7-8.93V4.07zm5.93 2.66l-1.47 1.63C15.01 7.78 13.11 7 11 7V4.07c2.27 0 4.36.89 5.93 2.23zM19.93 11c.71 3.95-2.23 7-6.93 7h-2c3.95 0 7-3.05 7-7h2z"></path>
    </symbol>

    <!-- 可复用图标路径 (概念性：代表团队和人才) -->
    <symbol id="icon-team" viewBox="0 0 24 24">
      <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.93 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"></path>
    </symbol>

    <!-- 可复用图标路径 (概念性：代表发展历程和经验) -->
    <symbol id="icon-history" viewBox="0 0 24 24">
      <path d="M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.51 0-2.91-.49-4.06-1.3L7.17 18.17C8.75 19.33 10.76 20 13 20c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 16V12h-2v5.7l5.27 3.28.73-1.21-4.5-2.79z"></path>
    </symbol>

  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color" />

  <!-- 装饰元素 - 柔和的几何图形和渐变 -->
  <rect x="0" y="0" width="300" height="1080" fill="url(#primaryGradient)" opacity="0.05" />
  <rect x="1620" y="0" width="300" height="1080" fill="url(#accentTransparentGradient)" opacity="0.05" />
  <circle cx="1700" cy="200" r="150" fill="#3B82F6" opacity="0.08" />
  <circle cx="220" cy="900" r="180" fill="#1E40AF" opacity="0.06" />

  <!-- Logo 占位符 -->
  <g id="logo-area">
    <rect x="80" y="60" width="200" height="50" rx="8" ry="8" fill="#FFFFFF" class="shadow-effect" />
    <text x="180" y="95" class="font-secondary small-text-size font-bold text-primary" text-anchor="middle">
      <tspan>企业Logo</tspan>
    </text>
    <!-- 实际Logo图片的占位符 -->
    <!-- <image xlink:href="{logo_url}" x="80" y="60" width="200" height="50" /> -->
  </g>

  <!-- 主内容区域 -->
  <g transform="translate(80, 180)">
    <!-- 标题 -->
    <text x="0" y="0" class="font-primary title-style">
      <tspan>{title}</tspan>
    </text>

    <!-- 副标题 -->
    <!-- 副标题的Y坐标计算：标题底部Y (0 + 56px字体大小) + 60px间距 = 116px -->
    <text x="0" y="116" class="font-primary subtitle-style">
      <tspan>{subtitle}</tspan>
    </text>

    <!-- 主要内容 - 类似Bento Grid布局：左侧段落，右侧要点列表 -->
    <!-- 内容块起始Y坐标：副标题底部Y (116 + 28px字体大小) + 48px区域间距 = 192px -->
    <g transform="translate(0, 192)">
      <!-- 左栏：主要段落文本 -->
      <!-- 使用foreignObject以便更好地处理多行文本的换行和行高 -->
      <foreignObject x="0" y="0" width="800" height="300">
        <div xmlns="http://www.w3.org/1999/xhtml" style="
          font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
          font-size: 22px;
          font-weight: 400;
          color: #1E293B;
          line-height: 1.6; /* 宽松行高确保易读性 */
          text-align: left;
          word-wrap: break-word;
        ">
          <p>{content}</p>
          <p>我们致力于通过持续创新和卓越服务，为客户提供定制化的解决方案。公司在行业内积累了深厚的技术实力和丰富的项目经验，赢得了广泛赞誉和信赖。</p>
          <p>未来，我们将继续深耕核心业务，拓展新兴领域，与全球合作伙伴携手共创辉煌。</p>
        </div>
      </foreignObject>

      <!-- 右栏：要点列表 -->
      <!-- 定位在段落区域的右侧。段落宽度800px，间距48px。所以X坐标为800 + 48 = 848 -->
      <g transform="translate(848, 0)">
        <text x="0" y="0" class="font-primary content-title-size font-semibold text-primary">
          <tspan>核心优势</tspan>
        </text>

        <!-- 要点列表项 -->
        <!-- 第一个要点从“核心优势”标题下方40px处开始 (28px字体大小 + 12px文本间距) -->
        <g class="font-primary list-item-style">
          <circle cx="-15" cy="40" r="6" class="bullet-point-style" />
          <text x="0" y="45">
            <tspan>创新技术和解决方案</tspan>
          </text>

          <circle cx="-15" cy="85" r="6" class="bullet-point-style" />
          <text x="0" y="90">
            <tspan>专业的研发和实施团队</tspan>
          </text>

          <circle cx="-15" cy="130" r="6" class="bullet-point-style" />
          <text x="0" y="135">
            <tspan>严格的质量控制和管理</tspan>
          </text>

          <circle cx="-15" cy="175" r="6" class="bullet-point-style" />
          <text x="0" y="180">
            <tspan>完善的客户服务体系</tspan>
          </text>

          <circle cx="-15" cy="220" r="6" class="bullet-point-style" />
          <text x="0" y="225">
            <tspan>稳健的市场拓展和布局</tspan>
          </text>
        </g>
      </g>
    </g>
  </g>

  <!-- 概念性数据可视化/信息图表区域 -->
  <!-- 定位在主内容下方。主内容高度大约为192 (translate Y) + 300 (foreignObject高度) = 492。
       此区域起始Y坐标为：180 (主内容translate Y) + 492 (主内容总高) + 60 (区域间距) = 732 -->
  <g transform="translate(80, 780)">
    <text x="0" y="0" class="font-primary section-title-size font-bold text-primary">
      <tspan>数据概览</tspan>
    </text>

    <!-- 卡片 1: 完成项目数 -->
    <rect x="0" y="60" width="300" height="150" class="card-style shadow-effect" />
    <use xlink:href="#icon-gear" x="20" y="80" width="32" height="32" class="icon-color" />
    <text x="60" y="105" class="font-primary small-text-size font-medium text-secondary">
      <tspan>完成项目数</tspan>
    </text>
    <text x="150" y="160" class="font-accent hero-title-size font-bold accent-color" text-anchor="middle">
      <tspan>500+</tspan>
    </text>

    <!-- 卡片 2: 核心团队成员 -->
    <rect x="340" y="60" width="300" height="150" class="card-style shadow-effect" />
    <use xlink:href="#icon-team" x="360" y="80" width="32" height="32" class="icon-color" />
    <text x="400" y="105" class="font-primary small-text-size font-medium text-secondary">
      <tspan>核心团队</tspan>
    </text>
    <text x="490" y="160" class="font-accent hero-title-size font-bold accent-color" text-anchor="middle">
      <tspan>120+</tspan>
    </text>

    <!-- 卡片 3: 行业经验年限 -->
    <rect x="680" y="60" width="300" height="150" class="card-style shadow-effect" />
    <use xlink:href="#icon-history" x="700" y="80" width="32" height="32" class="icon-color" />
    <text x="740" y="105" class="font-primary small-text-size font-medium text-secondary">
      <tspan>行业经验</tspan>
    </text>
    <text x="830" y="160" class="font-accent hero-title-size font-bold accent-color" text-anchor="middle">
      <tspan>15+</tspan>
    </text>

    <!-- 概念性条形图 (简化表示) -->
    <g transform="translate(1050, 60)">
      <text x="0" y="-20" class="font-primary content-title-size font-semibold text-primary">
        <tspan>市场份额增长</tspan>
      </text>
      <!-- X轴线 -->
      <line x1="0" y1="150" x2="600" y2="150" stroke="#BAE6FD" stroke-width="1" />
      <!-- Y轴线 -->
      <line x1="0" y1="0" x2="0" y2="150" stroke="#BAE6FD" stroke-width="1" />

      <!-- 条形图柱 -->
      <rect x="50" y="100" width="60" height="50" fill="#3B82F6" rx="5" ry="5" />
      <text x="80" y="170" class="font-secondary small-text-size text-secondary" text-anchor="middle">
        <tspan>2021</tspan>
      </text>
      <text x="80" y="90" class="font-secondary small-text-size text-primary" text-anchor="middle">
        <tspan>10%</tspan>
      </text>

      <rect x="150" y="70" width="60" height="80" fill="#3B82F6" rx="5" ry="5" />
      <text x="180" y="170" class="font-secondary small-text-size text-secondary" text-anchor="middle">
        <tspan>2022</tspan>
      </text>
      <text x="180" y="60" class="font-secondary small-text-size text-primary" text-anchor="middle">
        <tspan>16%</tspan>
      </text>

      <rect x="250" y="40" width="60" height="110" fill="#3B82F6" rx="5" ry="5" />
      <text x="280" y="170" class="font-secondary small-text-size text-secondary" text-anchor="middle">
        <tspan>2023</tspan>
      </text>
      <text x="280" y="30" class="font-secondary small-text-size text-primary" text-anchor="middle">
        <tspan>22%</tspan>
      </text>

      <rect x="350" y="10" width="60" height="140" fill="#3B82F6" rx="5" ry="5" />
      <text x="380" y="170" class="font-secondary small-text-size text-secondary" text-anchor="middle">
        <tspan>2024</tspan>
      </text>
      <text x="380" y="0" class="font-secondary small-text-size text-primary" text-anchor="middle">
        <tspan>28%</tspan>
      </text>
    </g>
  </g>

</svg>