<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Color Palette Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF"/>
      <stop offset="100%" stop-color="#475569"/>
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E3A8A"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>
    <!-- Special Highlight Color: Tesla Red -->
    <linearGradient id="teslaRedGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#E31937"/>
      <stop offset="100%" stop-color="#E31937"/>
    </linearGradient>
  </defs>

  <style>
    /* Global Styles */
    svg {
      font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
      background-color: #F8FAFC; /* Background color */
    }

    /* Colors */
    .bg-color { fill: #F8FAFC; }
    .primary-color { fill: #1E40AF; }
    .secondary-color { fill: #475569; }
    .accent-color { fill: #3B82F6; }
    .text-primary { fill: #1E293B; }
    .text-secondary { fill: #64748B; }
    .text-light { fill: #94A3B8; }
    .card-background { fill: #FFFFFF; }
    .card-border { stroke: #BAE6FD; }
    .tesla-red { fill: #E31937; }
    .tesla-red-stroke { stroke: #E31937; }

    /* Fonts */
    .font-hero-title { font-size: 72px; font-weight: 700; } /* bold */
    .font-main-title { font-size: 56px; font-weight: 700; }
    .font-section-title { font-size: 36px; font-weight: 700; }
    .font-content-title { font-size: 28px; font-weight: 600; } /* semibold */
    .font-body-text { font-size: 22px; font-weight: 400; }
    .font-small-text { font-size: 16px; font-weight: 400; }
    .font-caption { font-size: 14px; font-weight: 400; }

    /* Text alignment */
    .text-center { text-anchor: middle; }
    .text-left { text-anchor: start; }
    .text-right { text-anchor: end; }

    /* Card Style */
    .card {
      fill: #FFFFFF;
      stroke: #BAE6FD;
      stroke-width: 1px;
      rx: 12px; /* border-radius */
      ry: 12px;
    }
    /* Simple shadow filter */
    .card-shadow {
      filter: url(#cardShadow);
    }
    /* Timeline specific styles */
    .timeline-line {
      stroke: url(#primaryGradient);
      stroke-width: 4px;
      stroke-linecap: round;
    }
    .timeline-node {
      fill: #1E40AF;
      stroke: #F8FAFC;
      stroke-width: 4px;
    }
    .timeline-milestone {
      fill: url(#teslaRedGradient);
      stroke: #F8FAFC;
      stroke-width: 4px;
    }
    .timeline-date {
      font-size: 20px;
      font-weight: 600;
      fill: #1E293B;
    }
    .timeline-description {
      font-size: 18px;
      fill: #64748B;
      font-weight: 400;
    }
    .timeline-connector {
      stroke: #475569;
      stroke-width: 2px;
      stroke-dasharray: 4 4;
    }
    .icon-style {
      stroke: #4A86E8;
      stroke-width: 2;
      fill: none;
    }
    .divider-line {
      stroke: #BAE6FD;
      stroke-width: 1px;
    }
  </style>

  <!-- Filters for shadows -->
  <filter id="cardShadow" x="-50%" y="-50%" width="200%" height="200%">
    <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4"/>
    <feGaussianBlur result="blurOut" in="offOut" stdDeviation="3"/>
    <feColorMatrix result="matrixOut" in="blurOut" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
    <feOffset result="offOut2" in="SourceAlpha" dx="0" dy="2"/>
    <feGaussianBlur result="blurOut2" in="offOut2" stdDeviation="2"/>
    <feColorMatrix result="matrixOut2" in="blurOut2" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0"/>
    <feMerge>
      <feMergeNode in="matrixOut"/>
      <feMergeNode in="matrixOut2"/>
      <feMergeNode in="SourceGraphic"/>
    </feMerge>
  </filter>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color"/>

  <!-- Header Section -->
  <g id="header">
    <!-- Logo Placeholder -->
    <text x="80" y="100" class="font-section-title text-primary">{logo_url}</text> 
    <text x="960" y="100" class="font-main-title text-center text-primary">年中总结：成果和展望</text>
    <text x="960" y="150" class="font-content-title text-center text-secondary">Mid-Year Review: Achievements and Outlook</text>
    <line x1="80" y1="180" x2="1840" y2="180" class="divider-line"/>
  </g>

  <!-- Main Content - Timeline -->
  <g id="timeline-content">
    <!-- Central Timeline Line -->
    <line x1="960" y1="280" x2="960" y2="900" class="timeline-line"/>

    <!-- Timeline Nodes and Descriptions -->

    <!-- Node 1 -->
    <circle cx="960" cy="320" r="12" class="timeline-node"/>
    <line x1="960" y1="320" x2="1100" y2="320" class="timeline-connector"/>
    <rect x="1100" y="280" width="400" height="100" class="card card-shadow"/>
    <text x="1120" y="310" class="timeline-date">2023年1月 - {date}</text>
    <text x="1120" y="340" class="timeline-description">项目启动和团队组建</text>
    <text x="1120" y="365" class="timeline-description">Project Launch and Team Formation</text>


    <!-- Node 2 -->
    <circle cx="960" cy="450" r="12" class="timeline-node"/>
    <line x1="960" y1="450" x2="820" y2="450" class="timeline-connector"/>
    <rect x="420" y="410" width="400" height="100" class="card card-shadow"/>
    <text x="440" y="440" class="timeline-date">2023年3月 - {date}</text>
    <text x="440" y="470" class="timeline-description">核心技术研发和创新</text>
    <text x="440" y="495" class="timeline-description">Core Technology R和#38;D and Innovation</text>


    <!-- Milestone Node 3 -->
    <circle cx="960" cy="580" r="18" class="timeline-milestone"/>
    <text x="960" y="580" class="font-small-text text-center" fill="#FFFFFF" dy="5">里程碑</text>
    <line x1="960" y1="580" x2="1100" y2="580" class="timeline-connector"/>
    <rect x="1100" y="540" width="400" height="120" class="card card-shadow"/>
    <text x="1120" y="570" class="timeline-date" fill="url(#teslaRedGradient)">2023年5月 - {date}</text>
    <text x="1120" y="600" class="font-content-title" fill="#1E293B">产品内测成功</text>
    <text x="1120" y="625" class="timeline-description">Product Internal Testing Success</text>
    <text x="1120" y="650" class="timeline-description">用户满意度达90%</text>


    <!-- Node 4 -->
    <circle cx="960" cy="710" r="12" class="timeline-node"/>
    <line x1="960" y1="710" x2="820" y2="710" class="timeline-connector"/>
    <rect x="420" y="670" width="400" height="100" class="card card-shadow"/>
    <text x="440" y="700" class="timeline-date">2023年6月 - {date}</text>
    <text x="440" y="730" class="timeline-description">市场调研和策略制定</text>
    <text x="440" y="755" class="timeline-description">Market Research and Strategy Formulation</text>

    <!-- Node 5 -->
    <circle cx="960" cy="840" r="12" class="timeline-node"/>
    <line x1="960" y1="840" x2="1100" y2="840" class="timeline-connector"/>
    <rect x="1100" y="800" width="400" height="100" class="card card-shadow"/>
    <text x="1120" y="830" class="timeline-date">2023年7月 - {date}</text>
    <text x="1120" y="860" class="timeline-description">合作伙伴洽谈进展</text>
    <text x="1120" y="885" class="timeline-description">Partnership Negotiations Progress</text>

  </g>

  <!-- Decorative Elements / Bento Grid style (simplified) -->
  <g id="decorative-elements">
    <!-- Top-left abstract shape -->
    <rect x="0" y="0" width="300" height="200" fill="url(#primaryGradient)" opacity="0.05" rx="20" ry="20"/>
    <rect x="20" y="20" width="260" height="160" fill="none" stroke="#BAE6FD" stroke-width="2" rx="15" ry="15"/>

    <!-- Bottom-right abstract shape -->
    <rect x="1620" y="880" width="300" height="200" fill="url(#accentGradient)" opacity="0.05" rx="20" ry="20"/>
    <rect x="1640" y="900" width="260" height="160" fill="none" stroke="#BAE6FD" stroke-width="2" rx="15" ry="15"/>

    <!-- Simple outline graphic (placeholder for data visualization) -->
    <g transform="translate(100, 300)" opacity="0.1">
      <circle cx="0" cy="0" r="80" class="icon-style"/>
      <line x1="-70" y1="0" x2="70" y2="0" class="icon-style"/>
      <line x1="0" y1="-70" x2="0" y2="70" class="icon-style"/>
      <circle cx="0" cy="0" r="40" class="icon-style"/>
      <path d="M -50 0 Q 0 -50 50 0" class="icon-style"/>
      <path d="M -50 0 Q 0 50 50 0" class="icon-style"/>
    </g>

    <g transform="translate(1700, 600)" opacity="0.1">
      <rect x="0" y="0" width="100" height="100" class="icon-style" rx="10" ry="10"/>
      <rect x="20" y="20" width="60" height="60" class="icon-style" rx="5" ry="5"/>
      <line x1="0" y1="0" x2="100" y2="100" class="icon-style"/>
      <line x1="100" y1="0" x2="0" y2="100" class="icon-style"/>
    </g>

    <!-- Large number emphasis (example) -->
    <text x="160" y="550" class="font-hero-title text-primary" fill="url(#textGradient)" opacity="0.2">85%</text>
    <text x="160" y="620" class="font-section-title text-secondary" opacity="0.2">增长率</text>

  </g>

  <!-- Footer Section -->
  <g id="footer">
    <line x1="80" y1="960" x2="1840" y2="960" class="divider-line"/>
    <text x="960" y="1000" class="font-small-text text-secondary text-center">页面 8 / 10</text>
    <text x="960" y="1025" class="font-small-text text-secondary text-center">Mid-Year Review Presentation</text>
  </g>

</svg>