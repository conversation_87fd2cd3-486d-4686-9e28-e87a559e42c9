<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <style type="text/css">
      /* 颜色定义 */
      .bg-color { fill: #F8FAFC; }
      .primary-color { fill: #3B82F6; }
      .secondary-color { fill: #7DD3FC; }
      .accent-color { fill: #BAE6FD; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }

      /* 字体定义 */
      .font-inter { font-family: 'Inter', Helvetica, Arial, sans-serif; }
      .font-poppins { font-family: 'Poppins', sans-serif; }

      /* 字体大小和字重 */
      .title-hero { font-size: 72px; font-weight: 700; } /* 用于主标题，强调超大字体 */
      .title-main { font-size: 56px; font-weight: 700; }
      .title-section { font-size: 36px; font-weight: 600; }
      .text-content { font-size: 28px; font-weight: 400; } /* 用于副标题 */
      .text-body { font-size: 22px; font-weight: 400; }
      .text-small { font-size: 16px; font-weight: 400; }
      .text-caption { font-size: 14px; font-weight: 400; }

      /* 通用文本对齐 */
      .text-center { text-anchor: middle; }
      .text-left { text-anchor: start; }

      /* 装饰元素样式 */
      .decorative-line {
        stroke: #7DD3FC;
        stroke-width: 1.5px;
        fill: none;
        opacity: 0.5;
      }
      .decorative-shape-fill {
        fill: #BAE6FD;
        opacity: 0.15;
      }
      .decorative-shape-stroke {
        stroke: #3B82F6;
        stroke-width: 2px;
        fill: none;
        opacity: 0.3;
      }
    </style>

    <!-- 渐变定义 -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#7DD3FC"/>
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#BAE6FD"/>
      <stop offset="100%" stop-color="#3B82F6"/>
    </linearGradient>
    <linearGradient id="secondaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#7DD3FC"/>
      <stop offset="100%" stop-color="#BAE6FD"/>
    </linearGradient>

    <!-- 中心发光效果的径向渐变 -->
    <radialGradient id="centralGlow" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#BAE6FD" stop-opacity="0.3"/>
      <stop offset="100%" stop-color="#F8FAFC" stop-opacity="0"/>
    </radialGradient>

  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color" />

  <!-- 装饰元素组 - 学术、简约、结构化、微妙动态感 -->
  <g id="decorative-elements">
    <!-- 作为内容框架或容器提示的大型、微妙矩形 -->
    <rect x="80" y="60" width="1760" height="960" fill="none" stroke="#BAE6FD" stroke-width="1" opacity="0.4" rx="10" ry="10" />

    <!-- 主标题后方的中心微妙发光效果 -->
    <circle cx="960" cy="500" r="400" fill="url(#centralGlow)" />

    <!-- 抽象几何形状，增加视觉趣味 -->
    <circle cx="200" cy="900" r="120" class="decorative-shape-fill" />
    <rect x="1600" y="100" width="250" height="150" class="decorative-shape-fill" transform="rotate(10 1600 100)" />
    <path d="M 100 400 Q 300 300 500 400 T 900 400" class="decorative-line" />
    <path d="M 1820 700 Q 1700 800 1500 700 T 1100 700" class="decorative-line" />

    <!-- 更多微妙线条，暗示连接或数据流 -->
    <line x1="150" y1="150" x2="300" y2="180" class="decorative-line" opacity="0.3" />
    <line x1="1770" y1="930" x2="1600" y2="900" class="decorative-line" opacity="0.3" />
    <line x1="960" y1="200" x2="960" y2="880" class="decorative-line" opacity="0.2" />
  </g>

  <!-- Logo区域 -->
  <g id="logo-section">
    <!-- Logo文本占位符，采用中英文混用风格 -->
    <text x="80" y="100" class="text-small font-inter text-left text-primary">
      <tspan x="80" y="100" font-weight="700">UNIVERSITY</tspan>
      <tspan x="80" y="120" class="text-caption text-secondary">学术中心</tspan>
    </text>
    <!-- 如果需要图片Logo，请使用以下代码并替换 {logo_url}，确保URL中&#38;替代& -->
    <!-- <image x="80" y="60" width="200" height="60" xlink:href="{logo_url}" /> -->
  </g>

  <!-- 主要内容区：主标题和副标题 -->
  <g id="main-content">
    <!-- 主标题占位符 {title} - 中文大字体粗体，视觉冲击力强 -->
    <text x="960" y="450" class="title-hero font-inter text-center text-primary">
      <tspan x="960" y="450">{title}</tspan>
    </text>

    <!-- 副标题占位符 {subtitle} - 英文小字作为点缀 -->
    <text x="960" y="530" class="text-content font-inter text-center text-secondary">
      <tspan x="960" y="530">{subtitle}</tspan>
    </text>
  </g>

  <!-- 信息区：日期和作者 -->
  <g id="info-section">
    <text x="960" y="800" class="text-body font-inter text-center text-secondary">
      <tspan x="960" y="800">日期: {date}</tspan>
      <tspan x="960" dy="35">作者: {author}</tspan>
    </text>
  </g>

</svg>