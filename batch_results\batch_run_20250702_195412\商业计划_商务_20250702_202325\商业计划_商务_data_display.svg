<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette Definitions -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#3B82F6" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#F8FAFC" />
      <stop offset="100%" stop-color="#E0F2FE" />
    </linearGradient>

    <!-- Filter for Card Shadow -->
    <filter id="cardShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="3" flood-color="rgba(0, 0, 0, 0.1)" flood-opacity="1"/>
      <feDropShadow dx="0" dy="2" stdDeviation="2" flood-color="rgba(0, 0, 0, 0.06)" flood-opacity="1"/>
    </filter>

    <!-- CSS Styles -->
    <style>
      /* General Color Classes */
      .bg-color { fill: #F8FAFC; }
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .card-background { fill: #FFFFFF; }
      .card-border { stroke: #BAE6FD; stroke-width: 1px; }

      /* Font Styles */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
      .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; }
      .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; }
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; }
      .caption-text { font-size: 14px; font-weight: 400; line-height: 1.6; }

      /* Specific Element Styles for Data Visualization */
      .chart-line { stroke: url(#accentGradient); stroke-width: 4; fill: none; stroke-linecap: round; stroke-linejoin: round; }
      .chart-bar { fill: url(#primaryGradient); }
      .chart-label { fill: #1E293B; font-size: 18px; font-weight: 500; }
      .chart-axis-label { fill: #64748B; font-size: 16px; }
      .data-number-large { font-size: 64px; font-weight: 700; fill: url(#accentGradient); }
      .data-number-medium { font-size: 48px; font-weight: 700; fill: #1E40AF; }
      .data-card-title { font-size: 24px; font-weight: 600; fill: #1E293B; }
      .data-card-value { font-size: 48px; font-weight: 700; fill: #1E40AF; }
      .data-card-unit { font-size: 18px; font-weight: 400; fill: #64748B; }
      .divider-line { stroke: #BAE6FD; stroke-width: 1px; }

      /* Icon Style */
      .icon-stroke { stroke: #3B82F6; stroke-width: 2; fill: none; stroke-linecap: round; stroke-linejoin: round; }
    </style>
  </defs>

  <!-- Page Background -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color" />

  <!-- Page Header: Title and Subtitle -->
  <g id="page-header">
    <text x="80" y="100" class="section-title font-primary text-primary">
      <tspan>{title}</tspan>
    </text>
    <text x="80" y="145" class="body-text font-secondary text-secondary">
      <tspan>{subtitle}</tspan>
    </text>
  </g>

  <!-- Main Content Area: Bento Grid Layout -->
  <g id="main-content" transform="translate(80, 200)">
    <!-- Left Column: Large Chart Area Card -->
    <rect x="0" y="0" width="1000" height="700" rx="12" ry="12" class="card-background card-border" filter="url(#cardShadow)" />

    <g id="large-chart-container">
      <text x="40" y="50" class="content-title font-primary text-primary">
        <tspan>市场增长趋势分析</tspan>
      </text>
      <text x="40" y="85" class="small-text font-secondary text-secondary">
        <tspan>Market Growth Trend Analysis</tspan>
      </text>

      <!-- Placeholder Line Chart -->
      <g transform="translate(120, 190)">
        <!-- X-axis -->
        <line x1="0" y1="350" x2="760" y2="350" class="divider-line" />
        <text x="0" y="375" class="chart-axis-label font-secondary">Q1</text>
        <text x="190" y="375" class="chart-axis-label font-secondary">Q2</text>
        <text x="380" y="375" class="chart-axis-label font-secondary">Q3</text>
        <text x="570" y="375" class="chart-axis-label font-secondary">Q4</text>
        <text x="760" y="375" class="chart-axis-label font-secondary">年度</text>

        <!-- Y-axis -->
        <line x1="0" y1="0" x2="0" y2="350" class="divider-line" />
        <text x="-40" y="350" text-anchor="end" class="chart-axis-label font-secondary">0%</text>
        <text x="-40" y="260" text-anchor="end" class="chart-axis-label font-secondary">25%</text>
        <text x="-40" y="170" text-anchor="end" class="chart-axis-label font-secondary">50%</text>
        <text x="-40" y="80" text-anchor="end" class="chart-axis-label font-secondary">75%</text>
        <text x="-40" y="-10" text-anchor="end" class="chart-axis-label font-secondary">100%</text>

        <!-- Line Path (example data) -->
        <path d="M0 300 C190 150, 380 250, 570 100, 760 50" class="chart-line" />

        <!-- Data Points and Labels (ensure no overlap) -->
        <circle cx="0" cy="300" r="6" fill="#3B82F6" />
        <text x="0" y="280" text-anchor="middle" class="chart-label font-primary">20%</text>

        <circle cx="190" cy="150" r="6" fill="#3B82F6" />
        <text x="190" y="130" text-anchor="middle" class="chart-label font-primary">60%</text>

        <circle cx="380" cy="250" r="6" fill="#3B82F6" />
        <text x="380" y="230" text-anchor="middle" class="chart-label font-primary">35%</text>

        <circle cx="570" cy="100" r="6" fill="#3B82F6" />
        <text x="570" y="80" text-anchor="middle" class="chart-label font-primary">75%</text>

        <circle cx="760" cy="50" r="6" fill="#3B82F6" />
        <text x="760" y="30" text-anchor="middle" class="chart-label font-primary">85%</text>
      </g>
    </g>

    <!-- Right Column: Data Cards and Small Chart -->
    <g transform="translate(1040, 0)">
      <!-- Data Card 1: Total Project Investment -->
      <rect x="0" y="0" width="720" height="220" rx="12" ry="12" class="card-background card-border" filter="url(#cardShadow)" />
      <g transform="translate(40, 40)">
        <text x="0" y="0" class="data-card-title font-primary">
          <tspan>总项目投资</tspan>
        </text>
        <text x="0" y="30" class="small-text font-secondary text-secondary">
          <tspan>Total Project Investment</tspan>
        </text>
        <text x="0" y="120" class="data-card-value font-primary">
          <tspan>$125M</tspan>
        </text>
        <text x="180" y="120" class="data-card-unit font-secondary">
          <tspan>百万美元</tspan>
        </text>
        <!-- Icon: Money Bag (simple outline) -->
        <path d="M600 50 L600 150 M600 100 Q620 100 620 120 M600 100 Q580 100 580 80 M580 80 L580 120 M620 80 L620 120 M580 80 A20 20 0 0 1 620 80 Z M580 120 A20 20 0 0 0 620 120 Z" class="icon-stroke" />
      </g>

      <!-- Data Card 2: Expected Annual Return -->
      <rect x="0" y="240" width="720" height="220" rx="12" ry="12" class="card-background card-border" filter="url(#cardShadow)" />
      <g transform="translate(40, 280)">
        <text x="0" y="0" class="data-card-title font-primary">
          <tspan>预期年化收益</tspan>
        </text>
        <text x="0" y="30" class="small-text font-secondary text-secondary">
          <tspan>Expected Annual Return</tspan>
        </text>
        <text x="0" y="120" class="data-card-value font-primary">
          <tspan>28.5%</tspan>
        </text>
        <text x="180" y="120" class="data-card-unit font-secondary">
          <tspan>百分比</tspan>
        </text>
        <!-- Icon: Trending Up (simple outline) -->
        <path d="M600 140 L600 60 L680 60 M520 140 L560 100 L600 140 L640 100 L680 140" class="icon-stroke" />
      </g>

      <!-- Small Bar Chart Card: User Growth Statistics -->
      <rect x="0" y="480" width="720" height="220" rx="12" ry="12" class="card-background card-border" filter="url(#cardShadow)" />
      <g transform="translate(40, 520)">
        <text x="0" y="0" class="content-title font-primary text-primary">
          <tspan>用户增长统计</tspan>
        </text>
        <text x="0" y="30" class="small-text font-secondary text-secondary">
          <tspan>User Growth Statistics</tspan>
        </text>

        <!-- Placeholder Bar Chart -->
        <g transform="translate(0, 70)">
          <!-- Bars -->
          <rect x="0" y="80" width="80" height="60" rx="4" ry="4" class="chart-bar" />
          <text x="40" y="160" text-anchor="middle" class="chart-axis-label font-secondary">2021</text>
          <text x="40" y="70" text-anchor="middle" class="chart-label font-primary">60K</text>

          <rect x="120" y="50" width="80" height="90" rx="4" ry="4" class="chart-bar" />
          <text x="160" y="160" text-anchor="middle" class="chart-axis-label font-secondary">2022</text>
          <text x="160" y="40" text-anchor="middle" class="chart-label font-primary">90K</text>

          <rect x="240" y="20" width="80" height="120" rx="4" ry="4" class="chart-bar" />
          <text x="280" y="160" text-anchor="middle" class="chart-axis-label font-secondary">2023</text>
          <text x="280" y="10" text-anchor="middle" class="chart-label font-primary">120K</text>

          <rect x="360" y="0" width="80" height="140" rx="4" ry="4" class="chart-bar" />
          <text x="400" y="160" text-anchor="middle" class="chart-axis-label font-secondary">2024</text>
          <text x="400" y="-10" text-anchor="middle" class="chart-label font-primary">140K</text>
        </g>
      </g>
    </g>
  </g>

  <!-- Footer: Date and Page Number -->
  <g id="page-footer">
    <text x="1840" y="1020" text-anchor="end" class="small-text font-secondary text-light">
      <tspan>{date}</tspan>
    </text>
    <text x="80" y="1020" class="small-text font-secondary text-light">
      <tspan>页面 6/10</tspan>
    </text>
  </g>

  <!-- Logo Placeholder (Top Left Corner) -->
  <g id="logo-placeholder">
    <rect x="80" y="40" width="120" height="40" fill="#E0F2FE" rx="8" ry="8" />
    <text x="140" y="65" text-anchor="middle" class="small-text font-primary text-primary">
      <tspan>{logo_url}</tspan>
    </text>
  </g>

</svg>