<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <style type="text/css">
      /* Color Palette */
      .bg-color { fill: #F8FAFC; }
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .card-background { fill: #FFFFFF; }
      .card-border { stroke: #BAE6FD; }
      .container-background { fill: #E0F2FE; }

      /* Font Styles */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han <PERSON>s CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* Font Sizes and Weights */
      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
      .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; }
      .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; }
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; }
      .caption-text { font-size: 14px; font-weight: 400; line-height: 1.6; }

      /* Shadows */
      .shadow-card { filter: url(#shadowCard); }
    </style>

    <!-- Filter for card shadow -->
    <filter id="shadowCard" x="-5%" y="-5%" width="110%" height="110%">
      <feDropShadow dx="0" dy="10" stdDeviation="15" flood-color="rgba(0, 0, 0, 0.1)"/>
      <feDropShadow dx="0" dy="4" stdDeviation="6" flood-color="rgba(0, 0, 0, 0.05)"/>
    </filter>

    <!-- Gradient for primary decorative elements -->
    <linearGradient id="gradientPrimary" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#475569" />
    </linearGradient>
    <!-- Gradient for accent decorative elements -->
    <linearGradient id="gradientAccent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color"/>

  <!-- Decorative elements (subtle, non-distracting geometric shapes) -->
  <rect x="0" y="0" width="300" height="150" class="container-background" rx="20" ry="20" opacity="0.3"/>
  <rect x="1620" y="930" width="300" height="150" class="container-background" rx="20" ry="20" opacity="0.3"/>
  <circle cx="1700" cy="100" r="80" class="accent-color" opacity="0.1"/>
  <circle cx="220" cy="980" r="60" class="primary-color" opacity="0.1"/>

  <!-- Header Section -->
  <g id="header">
    <!-- Logo Placeholder -->
    <image xlink:href="{logo_url}" x="80" y="60" width="120" height="40" preserveAspectRatio="xMidYMid meet"/>
    
    <!-- Title and Subtitle -->
    <text x="80" y="150" class="section-title text-primary font-primary">
      <tspan>{title}</tspan>
      <tspan x="80" dy="45" class="content-title text-secondary font-secondary">{subtitle}</tspan>
    </text>
  </g>

  <!-- Main Content Section: Image and Text Layout -->
  <g id="main-content">
    <!-- Image Column (Left, ~40% width) -->
    <g id="image-section">
      <!-- Image frame/card background -->
      <rect x="80" y="250" width="704" height="528" rx="12" ry="12" class="card-background card-border shadow-card"/>
      <!-- Image content -->
      <image xlink:href="{image_url}" x="80" y="250" width="704" height="528" preserveAspectRatio="xMidYMid slice" clip-path="url(#imageClip)"/>
      <!-- Clip path for image to respect border radius -->
      <clipPath id="imageClip">
        <rect x="80" y="250" width="704" height="528" rx="12" ry="12"/>
      </clipPath>
      
      <!-- Image overlay for subtle effect -->
      <rect x="80" y="250" width="704" height="528" rx="12" ry="12" fill="url(#gradientPrimary)" opacity="0.1"/>
      
      <!-- Decorative line linking image and text (at least 80px spacing) -->
      <line x1="784" y1="514" x2="960" y2="514" stroke="#3B82F6" stroke-width="3" stroke-dasharray="8 8"/>
      <circle cx="960" cy="514" r="8" fill="#3B82F6"/>
    </g>

    <!-- Text Column (Right, ~50% width) -->
    <g id="text-section">
      <!-- Content Title -->
      <text x="960" y="250" class="content-title text-primary font-primary">
        <tspan>市场分析和战略定位</tspan>
        <tspan x="960" dy="45" class="body-text text-secondary font-secondary">Market Analysis and Strategic Positioning</tspan>
      </text>

      <!-- Content text block 1 (body text, at least 30px dy) -->
      <text x="960" y="370" class="body-text text-primary font-secondary">
        <tspan>我们深入分析了当前的市场环境，包括宏观经济趋势、行业发展动态</tspan>
        <tspan x="960" dy="30">以及竞争格局。通过数据驱动的洞察，我们识别出关键的市场机遇</tspan>
        <tspan x="960" dy="30">和潜在挑战，为公司的战略规划提供了坚实的基础。</tspan>
      </text>

      <!-- Large Number / Key Metric (with at least 100px vertical space) -->
      <g id="key-metric">
        <rect x="960" y="520" width="300" height="100" rx="10" ry="10" class="container-background" opacity="0.6"/>
        <text x="975" y="585" class="hero-title accent-color font-accent">
          <tspan>+35%</tspan>
        </text>
        <text x="1290" y="570" class="small-text text-primary font-secondary">
          <tspan>预期市场增长</tspan>
          <tspan x="1290" dy="25" class="caption-text text-secondary font-secondary">Expected Market Growth</tspan>
        </text>
      </g>

      <!-- Content text block 2 (body text, at least 30px dy) -->
      <text x="960" y="680" class="body-text text-primary font-secondary">
        <tspan>本阶段的战略定位旨在最大限度地发挥我们的核心优势，</tspan>
        <tspan x="960" dy="30">并抓住快速增长的市场细分。我们专注于创新和客户价值，</tspan>
        <tspan x="960" dy="30">确保在竞争中保持领先地位，实现可持续发展和盈利增长。</tspan>
      </text>
    </g>
  </g>

  <!-- Footer Section -->
  <g id="footer">
    <text x="80" y="1020" class="caption-text text-light font-secondary">
      <tspan>{date} | {author}</tspan>
    </text>
    <text x="1840" y="1020" text-anchor="end" class="caption-text text-light font-secondary">
      <tspan>5 / 10</tspan>
    </text>
  </g>

</svg>